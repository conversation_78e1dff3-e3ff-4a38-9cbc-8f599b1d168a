import{Ecc}from"./qrcodegen";export const ERROR_LEVEL_MAP={L:Ecc.LOW,M:Ecc.MEDIUM,Q:Ecc.QUARTILE,H:Ecc.HIGH};export const DEFAULT_SIZE=160;export const DEFAULT_LEVEL="M";export const DEFAULT_BACKGROUND_COLOR="#FFFFFF";export const DEFAULT_FRONT_COLOR="#000000";export const DEFAULT_NEED_MARGIN=!1;export const DEFAULT_MINVERSION=1;export const SPEC_MARGIN_SIZE=4;export const DEFAULT_MARGIN_SIZE=0;export const DEFAULT_IMG_SCALE=.1;export const generatePath=(t,o=0)=>{const e=[];return t.forEach((t,n)=>{let r=null;t.forEach((c,l)=>{if(!c&&null!==r)return e.push(`M${r+o} ${n+o}h${l-r}v1H${r+o}z`),void(r=null);if(l!==t.length-1)c&&null===r&&(r=l);else{if(!c)return;null===r?e.push(`M${l+o},${n+o} h1v1H${l+o}z`):e.push(`M${r+o},${n+o} h${l+1-r}v1H${r+o}z`)}})}),e.join("")};export const excavateModules=(t,o)=>t.slice().map((t,e)=>e<o.y||e>=o.y+o.h?t:t.map((t,e)=>(e<o.x||e>=o.x+o.w)&&t));export const getImageSettings=(t,o,e,n)=>{if(null==n)return null;const r=t.length+2*e,c=Math.floor(.1*o),l=r/o,a=(n.width||c)*l,h=(n.height||c)*l,s=null==n.x?t.length/2-a/2:n.x*l,E=null==n.y?t.length/2-h/2:n.y*l,p=null==n.opacity?1:n.opacity;let i=null;if(n.excavate){const t=Math.floor(s),o=Math.floor(E);i={x:t,y:o,w:Math.ceil(a+s-t),h:Math.ceil(h+E-o)}}const{crossOrigin:x}=n;return{x:s,y:E,h:h,w:a,excavation:i,opacity:p,crossOrigin:x}};export const getMarginSize=(t,o)=>null!=o?Math.max(Math.floor(o),0):t?4:0;export const isSupportPath2d=(()=>{try{(new Path2D).addPath(new Path2D)}catch(t){return!1}return!0})();