import{__awaiter,__decorate}from"tslib";import props from"./props";import config from"../common/config";import{SuperComponent,wxComponent}from"../common/src/index";const{prefix:prefix}=config,name=`${prefix}-qrcode`;let QRCode=class extends SuperComponent{constructor(){super(...arguments),this.externalClasses=[`${prefix}-class`],this.options={multipleSlots:!0,virtualHost:!0},this.properties=Object.assign(Object.assign({},props),{statusRender:{type:Boolean,value:!1},style:{type:String,value:""},customStyle:{type:String,value:""}}),this.data={prefix:prefix,showMask:!1,classPrefix:name,canvasReady:!1},this.lifetimes={ready(){return __awaiter(this,void 0,void 0,function*(){const e=this.selectComponent("#qrcodeCanvas"),t=yield e.getCanvasNode();this.setData({canvasNode:t})})},attached(){this.setData({showMask:"active"!==this.properties.status})}},this.observers={status:function(e){this.setData({showMask:"active"!==e})}},this.methods={handleDrawCompleted(){this.setData({canvasReady:!0})},handleDrawError(e){console.error("二维码绘制失败",e)},handleRefresh(){this.triggerEvent("refresh")},handleDownload(){return __awaiter(this,void 0,void 0,function*(){this.data.canvasNode?wx.canvasToTempFilePath({canvas:this.data.canvasNode,success:e=>{wx.saveImageToPhotosAlbum({filePath:e.tempFilePath})},fail:e=>{console.error("canvasToTempFilePath failed",e)}},this):console.error("未找到 canvas 节点")})}}}};QRCode=__decorate([wxComponent()],QRCode);export default QRCode;