/* pages/word-detail/index.wxss */
.word-detail-container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

/* 单词详情 */
.word-detail {
  padding: 20rpx;
}

/* 头部信息 */
.word-header {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.word-main {
  display: flex;
  flex-direction: column;
}

.word-name-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.word-name {
  font-size: 48rpx;
  font-weight: 700;
  color: #333;
  flex: 1;
}

.word-actions {
  display: flex;
  align-items: center;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background-color: #f8f8f8;
  margin-left: 16rpx;
  transition: all 0.3s;
}

.action-btn:active {
  background-color: #e8e8e8;
  transform: scale(0.95);
}

.word-phonetic {
  margin-bottom: 16rpx;
}

.word-phonetic text {
  font-size: 32rpx;
  color: #667eea;
  font-style: italic;
}

.word-category {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.category-tag {
  display: inline-block;
  padding: 8rpx 16rpx;
  background-color: #e8f0fe;
  color: #667eea;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.difficulty {
  display: flex;
  align-items: center;
}

.difficulty-text {
  padding: 6rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  color: #fff;
  font-weight: 500;
}

.difficulty-1 {
  background-color: #52c41a;
}

.difficulty-2 {
  background-color: #faad14;
}

.difficulty-3 {
  background-color: #f5222d;
}

/* 内容区块 */
.word-section {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  display: flex;
  align-items: center;
  padding: 30rpx 40rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.section-title text {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-left: 12rpx;
}

.section-content {
  padding: 20rpx 40rpx 40rpx;
}

/* 翻译 */
.translation-item {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}

.translation-text {
  font-size: 32rpx;
  color: #333;
  line-height: 1.6;
  flex: 1;
  margin-right: 20rpx;
}

.copy-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
  background-color: #f8f8f8;
  transition: all 0.3s;
}

.copy-btn:active {
  background-color: #e8e8e8;
  transform: scale(0.95);
}

/* 例句 */
.example-item {
  display: flex;
  flex-direction: column;
}

.example-en {
  font-size: 30rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 16rpx;
  font-style: italic;
}

.example-cn {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  padding-left: 20rpx;
  border-left: 4rpx solid #667eea;
}

/* 来源信息 */
.source-path {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 20rpx;
}

.source-item {
  display: flex;
  align-items: center;
  margin-right: 16rpx;
  margin-bottom: 8rpx;
}

.source-item text {
  font-size: 28rpx;
  color: #666;
  margin-left: 8rpx;
}

.source-arrow {
  display: flex;
  align-items: center;
  margin-right: 16rpx;
}

.textbook-info {
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 26rpx;
  color: #999;
  min-width: 120rpx;
}

.info-value {
  font-size: 26rpx;
  color: #666;
  flex: 1;
}

/* 备注 */
.remark-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 错误状态 */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  padding: 40rpx;
}

.error-text {
  font-size: 32rpx;
  color: #999;
  margin: 30rpx 0;
}

.error-actions {
  margin-top: 40rpx;
}

.back-btn {
  background-color: #667eea;
  color: #fff;
  border: none;
  border-radius: 25rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #fff;
  border-top: 1rpx solid #eee;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  margin: 0 10rpx;
  transition: all 0.3s;
}

.action-button text {
  margin-left: 8rpx;
}

.action-button.secondary {
  background-color: #f8f8f8;
  color: #666;
}

.action-button.secondary:active {
  background-color: #e8e8e8;
}

.action-button.primary {
  background-color: #667eea;
  color: #fff;
}

.action-button.primary:active {
  background-color: #5a6fd8;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .word-name {
    font-size: 42rpx;
  }
  
  .translation-text {
    font-size: 30rpx;
  }
  
  .example-en {
    font-size: 28rpx;
  }
  
  .section-title text {
    font-size: 28rpx;
  }
}