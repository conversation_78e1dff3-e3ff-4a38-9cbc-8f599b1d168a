/* 创建任务页面样式 */
.create-task-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

/* 页面头部 */
.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 30rpx 60rpx;
  color: white;
}

/* 步骤指示器 */
.step-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20rpx;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  opacity: 0.5;
  transition: opacity 0.3s;
}

.step-item.active {
  opacity: 1;
}

.step-number {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.step-item.active .step-number {
  background-color: white;
  color: #667eea;
}

.step-text {
  font-size: 24rpx;
  text-align: center;
}

.step-line {
  width: 80rpx;
  height: 4rpx;
  background-color: rgba(255, 255, 255, 0.3);
  margin: 0 20rpx;
  margin-top: -30rpx;
  transition: background-color 0.3s;
}

.step-line.active {
  background-color: white;
}

/* 步骤内容 */
.step-content {
  padding: 20rpx;
}

/* 表单区域 */
.form-section {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 6rpx;
  height: 32rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 3rpx;
  margin-right: 15rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
  font-weight: 500;
}

.form-item.required .form-label::after {
  content: '*';
  color: #ff4757;
  margin-left: 5rpx;
}

/* 筛选工具栏 */
.filter-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: white;
  padding: 10rpx 10rpx;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.quick-select {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.quick-label {
  font-size: 26rpx;
  color: #666;
}

/* 筛选面板 */
.filter-panel {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.filter-row {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.filter-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15rpx;
  margin-top: 30rpx;
}

/* 已选单词区域 */
.selected-words-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.section-actions {
  display: flex;
  gap: 15rpx;
}

.selected-words-list {
  white-space: nowrap;
  padding: 10rpx 0;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300rpx, 1fr));
  gap: 20rpx;
  margin-top: 20rpx;
}

/* 可选单词区域 */
.available-words-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.word-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300rpx, 1fr));
  gap: 20rpx;
  margin-top: 20rpx;
}

/* 单词卡片 */
.word-card {
  background: #f8f9fa;
  border-radius: 15rpx;
  padding: 25rpx;
  position: relative;
  transition: all 0.3s;
  border: 2rpx solid transparent;
}

.word-card.available {
  cursor: pointer;
}

.word-card.available:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.word-card.selected {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  margin-right: 15rpx;
  min-width: 200rpx;
  display: inline-block;
}

.word-content {
  padding-right: 40rpx;
}

.word-name {
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  color: inherit;
}

.word-meaning {
  font-size: 26rpx;
  color: inherit;
  opacity: 0.8;
  margin-bottom: 10rpx;
}

.word-meta {
  display: flex;
  gap: 10rpx;
  font-size: 22rpx;
  opacity: 0.6;
}

.word-meta text {
  background: rgba(0, 0, 0, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

.add-btn, .remove-btn {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.add-btn:hover {
  background: #667eea;
  color: white;
}

.remove-btn {
  background: rgba(255, 255, 255, 0.2);
}

.remove-btn:hover {
  background: #ff4757;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 0;
  color: #999;
}

.empty-state text {
  display: block;
  margin-top: 20rpx;
  font-size: 28rpx;
}

/* 加载更多 */
.load-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
  color: #999;
  gap: 15rpx;
}

/* 确认区域 */
.confirm-section {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.info-card {
  background: #f8f9fa;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.info-item {
  display: flex;
  margin-bottom: 20rpx;
  align-items: flex-start;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 28rpx;
  color: #666;
  min-width: 140rpx;
  margin-right: 20rpx;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  word-break: break-all;
}

/* 单词预览 */
.words-preview {
  margin-top: 30rpx;
}

.word-preview-list {
  background: #f8f9fa;
  border-radius: 15rpx;
  padding: 30rpx;
  max-height: 400rpx;
  overflow-y: auto;
}

.word-preview-item {
  display: flex;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #eee;
}

.word-preview-item:last-child {
  border-bottom: none;
}

.word-index {
  font-size: 24rpx;
  color: #999;
  min-width: 60rpx;
}

.word-preview-item .word-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  min-width: 120rpx;
  margin-right: 20rpx;
}

.word-preview-item .word-meaning {
  font-size: 26rpx;
  color: #666;
  flex: 1;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 30rpx;
  border-top: 1rpx solid #eee;
  display: flex;
  gap: 20rpx;
  z-index: 100;
}

.bottom-actions .t-button {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .word-grid {
    grid-template-columns: 1fr;
  }
  
  .filter-row {
    flex-direction: column;
    gap: 15rpx;
  }
  
  .quick-select {
    flex-wrap: wrap;
  }
}

/* 动画效果 */
.step-content {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.word-card {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}