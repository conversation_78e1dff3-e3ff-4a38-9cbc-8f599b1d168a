<view class="create-task-page">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="step-indicator">
      <view class="step-item {{currentStep >= 1 ? 'active' : ''}}">
        <view class="step-number">1</view>
        <view class="step-text">基本信息</view>
      </view>
      <view class="step-line {{currentStep >= 2 ? 'active' : ''}}"></view>
      <view class="step-item {{currentStep >= 2 ? 'active' : ''}}">
        <view class="step-number">2</view>
        <view class="step-text">选择单词</view>
      </view>
      <view class="step-line {{currentStep >= 3 ? 'active' : ''}}"></view>
      <view class="step-item {{currentStep >= 3 ? 'active' : ''}}">
        <view class="step-number">3</view>
        <view class="step-text">确认创建</view>
      </view>
    </view>
  </view>

  <!-- 步骤1: 基本信息 -->
  <view class="step-content" wx:if="{{currentStep === 1}}">
    <view class="form-section">
      <view class="section-title">任务基本信息</view>
      
      <view class="form-item required">
        <text class="form-label">任务名称</text>
        <t-input
          value="{{taskForm.taskName}}"
          placeholder="请输入任务名称"
          bind:change="onTaskNameChange"
          maxlength="50"
        />
      </view>
      
      <view class="form-item required">
        <text class="form-label">任务类型</text>
        <picker 
          value="{{taskForm.taskTypeIndex}}"
          range="{{taskTypeOptions}}"
          range-key="label"
          bind:change="onTaskTypeChange"
        >
          <t-input 
            slot="trigger"
            value="{{selectedTaskTypeName}}"
            placeholder="请选择任务类型"
            readonly
            suffix-icon="chevron-down"
          />
        </picker>
      </view>
      
      <view class="form-item">
        <text class="form-label">教材（可选择）</text>
        <picker 
          value="{{taskForm.textbookIndex}}"
          range="{{textbookList}}"
          range-key="textbookName"
          bind:change="onTextbookChange"
          placeholder="请选择教材（可选）"
        >
          <t-input 
            slot="trigger"
            value="{{selectedTextbookName}}"
            placeholder="请选择教材（可选）"
            readonly
            suffix-icon="chevron-down"
          />
        </picker>
      </view>
      
      <view class="form-item" wx:if="{{taskForm.textbookId && unitList.length > 0}}">
        <text class="form-label">单元（可选）</text>
        <picker 
          value="{{taskForm.unitIdIndex}}"
          range="{{unitList}}"
          range-key="name"
          bind:change="onUnitChange"
          placeholder="请选择单元（可选）"
        >
          <t-input 
            slot="trigger"
            value="{{selectedUnitName}}"
            placeholder="请选择单元（可选）"
            readonly
            suffix-icon="chevron-down"
          />
        </picker>
      </view>
      
      <view class="form-item" wx:if="{{taskForm.unitId && chapterList.length > 0}}">
        <text class="form-label">章节（可选）</text>
        <picker 
          value="{{taskForm.chapterIndex}}"
          range="{{chapterList}}"
          range-key="name"
          bind:change="onChapterChange"
          placeholder="请选择章节（可选）"
        >
          <t-input 
            slot="trigger"
            value="{{selectedChapterName}}"
            placeholder="请选择章节（可选）"
            readonly
            suffix-icon="chevron-down"
          />
        </picker>
      </view>
      
      <view class="form-item">
        <text class="form-label">任务描述</text>
        <t-textarea
          value="{{taskForm.description}}"
          placeholder="请输入任务描述（可选）"
          bind:change="onDescriptionChange"
          maxlength="200"
          indicator
        />
      </view>
    </view>
  </view>

  <!-- 步骤2: 选择单词 -->
  <view class="step-content" wx:if="{{currentStep === 2}}">
    <!-- 筛选工具栏 -->
    <view class="filter-toolbar">
      <t-button 
        variant="outline" 
        size="small" 
        icon="filter"
        bind:tap="toggleFilterPanel"
      >
        筛选
      </t-button>
      
      <!-- 快速选择按钮 -->
      <view class="quick-select">
        <text class="quick-label">随机:</text>
        <t-button 
          wx:for="{{quickSelectOptions}}" 
          wx:key="*this"
          variant="outline" 
          size="small"
          data-count="{{item}}"
          bind:tap="quickSelectWords"
        >
          {{item}}个
        </t-button>
      </view>
    </view>

    <!-- 筛选面板 -->
    <view class="filter-panel" wx:if="{{showFilterPanel}}">
      <view class="filter-row">
        <t-input
          value="{{wordFilter.keyword}}"
          placeholder="搜索单词名称"
          bind:change="onFilterKeywordChange"
        />
      </view>
      
      <view class="filter-row">
        <t-input
          value="{{wordFilter.category}}"
          placeholder="单词分类"
          bind:change="onFilterCategoryChange"
        />
        <t-input
          value="{{wordFilter.difficulty}}"
          placeholder="难度等级"
          bind:change="onFilterDifficultyChange"
        />
      </view>
      
      <view class="filter-actions">
        <t-button variant="outline" size="small" bind:tap="clearFilter">清除</t-button>
        <t-button theme="primary" size="small" bind:tap="applyFilter">应用</t-button>
      </view>
    </view>

    <!-- 已选单词列表 -->
    <view class="selected-words-section" wx:if="{{selectedWords.length > 0}}">
      <view class="section-header">
        <text class="section-title">已选单词 ({{selectedWords.length}})</text>
        <view class="section-actions">
          <t-button variant="outline" size="small" bind:tap="shuffleSelectedWords">打乱</t-button>
          <t-button variant="outline" size="small" bind:tap="clearSelectedWords">清空</t-button>
        </view>
      </view>
      
      <view class="selected-words-list" scroll-x>
        <view class="word-card selected" wx:for="{{selectedWords}}" wx:key="id">
          <view class="word-content">
            <view class="word-name">{{item.wordName}}</view>
            <view class="word-meaning">{{item.meaning}}</view>
          </view>
          <t-icon 
            name="close" 
            size="16" 
            class="remove-btn"
            data-index="{{index}}"
            bind:tap="removeSelectedWord"
          />
        </view>
      </view>
    </view>

    <!-- 可选单词列表 -->
    <view class="available-words-section">
      <view class="section-title">可选单词</view>
      
      <view class="word-grid" wx:if="{{availableWords.length > 0}}">
        <view 
          class="word-card available" 
          wx:for="{{availableWords}}" 
          wx:key="id"
          data-word="{{item}}"
          bind:tap="selectWord"
        >
          <view class="word-content">
            <view class="word-name">{{item.wordName}}</view>
            <view class="word-meaning">{{item.meaning}}</view>
            <view class="word-meta" wx:if="{{item.category || item.difficulty}}">
              <text wx:if="{{item.category}}">{{item.category}}</text>
              <text wx:if="{{item.difficulty}}">{{item.difficulty}}</text>
            </view>
          </view>
          <t-icon name="add" size="16" class="add-btn" />
        </view>
      </view>
      
      <view class="empty-state" wx:else>
        <t-icon name="search" size="48" />
        <text>暂无可选单词</text>
      </view>
      
      <!-- 加载更多 -->
      <view class="load-more" wx:if="{{wordLoading}}">
        <t-loading theme="circular" size="20px" />
        <text>加载中...</text>
      </view>
    </view>
  </view>

  <!-- 步骤3: 确认创建 -->
  <view class="step-content" wx:if="{{currentStep === 3}}">
    <view class="confirm-section">
      <view class="section-title">确认任务信息</view>
      
      <view class="info-card">
        <view class="info-item">
          <text class="info-label">任务名称:</text>
          <text class="info-value">{{taskForm.taskName}}</text>
        </view>
        
        <view class="info-item">
          <text class="info-label">任务类型:</text>
          <text class="info-value">{{selectedTaskTypeName}}</text>
        </view>
        
        <view class="info-item" wx:if="{{selectedTextbookName}}">
          <text class="info-label">教材:</text>
          <text class="info-value">{{selectedTextbookName}}</text>
        </view>
        
        <view class="info-item" wx:if="{{selectedUnitName}}">
          <text class="info-label">单元:</text>
          <text class="info-value">{{selectedUnitName}}</text>
        </view>
        
        <view class="info-item" wx:if="{{selectedChapterName}}">
          <text class="info-label">章节:</text>
          <text class="info-value">{{selectedChapterName}}</text>
        </view>
        
        <view class="info-item">
          <text class="info-label">单词数量:</text>
          <text class="info-value">{{selectedWords.length}}个</text>
        </view>
        
        <view class="info-item" wx:if="{{taskForm.description}}">
          <text class="info-label">任务描述:</text>
          <text class="info-value">{{taskForm.description}}</text>
        </view>
      </view>
      
      <!-- 单词预览 -->
      <view class="words-preview">
        <view class="section-title">单词列表预览</view>
        <view class="word-preview-list">
          <view class="word-preview-item" wx:for="{{selectedWords}}" wx:key="id">
            <text class="word-index">{{index + 1}}.</text>
            <text class="word-name">{{item.wordName}}</text>
            <text class="word-meaning">{{item.meaning}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions">
    <t-button 
      wx:if="{{currentStep > 1}}"
      variant="outline" 
      bind:tap="prevStep"
    >
      上一步
    </t-button>
    
    <t-button 
      wx:if="{{currentStep < 3}}"
      theme="primary" 
      bind:tap="nextStep"
    >
      下一步
    </t-button>
    
    <t-button 
      wx:if="{{currentStep === 3}}"
      theme="primary" 
      loading="{{submitting}}"
      bind:tap="createTask"
    >
      {{submitting ? '创建中...' : '创建任务'}}
    </t-button>
  </view>
</view>