// pages/task-manage/index.js
const apiFileHost = getApp().globalData.apiFileHost
const api = require('../../utils/api');
const Toast = require('tdesign-miniprogram/toast/index');

Page({
  data: {
    apiFileHostSrc: apiFileHost,
    // 页面状态
    pageLoading: false,
    refreshing: false,
    loadingMore: false,
    hasMore: true,
    
    // 当前学生
    currentStudent: null,
    userImageSrc: '',
    
    // 学生选择器
    showStudentSelector: false,
    studentList: [],
    
    // 任务列表
    taskList: [],
    
    // 筛选条件
    filterStatus: 'all', // all, pending, finished, corrected
    searchKeyword: '',
    selectedTextbook: null,
    selectedUnit: null,
    selectedChapter: null,
    
    // 分页参数
    pageNum: 1,
    pageSize: 10,
    
    // 统计数据
    statistics: {
      total: 0,
      pending: 0,
      finished: 0,
      corrected: 0
    },
    
    // 显示状态
    showCreateModal: false,
    showFilterModal: false,
    
    // 创建任务表单
    createForm: {
      taskName: '',
      textbookId: '',
      chapterId: '',
      wordCount: 10,
      taskType: 'dictation', // dictation, review, test
      deadline: '',
      description: ''
    },
    
    // 教材数据
    textbookList: [],
    chapterList: [],
    unitList: []
  },

  async onLoad() {
    // 检查学生状态
    const currentStudent = wx.getStorageSync('currentStudent');
    
    if (currentStudent) {
      this.setData({ currentStudent, userImageSrc: apiFileHost + currentStudent.avatar });
      await this.loadTaskList(true);
      await this.loadStatistics();
    } else {
      await this.checkStudentStatus();
    }
    
    // 加载学生列表
    await this.loadStudentList();
    this.loadTextbookList();
  },

  // 检查学生状态
  async checkStudentStatus() {
    try {
      const res = await api.getStudentList({ pageSize: 1 });
      if (res.success && res.result.records && res.result.records.length > 0) {
        // 有学生但未选择
        wx.showModal({
          title: '提示',
          content: '请在首页选择学生信息',
          showCancel: false,
          success: () => {
            wx.switchTab({ url: '/pages/word-index/index' });
          }
        });
      } else {
        // 没有学生
        wx.showModal({
          title: '提示', 
          content: '请在首页添加学生信息',
          showCancel: false,
          success: () => {
            wx.switchTab({ url: '/pages/word-index/index' });
          }
        });
      }
    } catch (error) {
      console.error('检查学生状态失败:', error);
      wx.switchTab({ url: '/pages/word-index/index' });
    }
  },

  onShow() {

    // 检查学生信息是否变化
    const currentStudent = wx.getStorageSync('currentStudent');
    if (currentStudent && (!this.data.currentStudent || this.data.currentStudent.id !== currentStudent.id)) {
      this.setData({ currentStudent });
    } else if (!currentStudent) {
      this.setData({ currentStudent: null });
    }

    // 页面显示时刷新数据
    if (this.data.currentStudent) {
      this.refreshData();
    }
  },

  onPullDownRefresh() {
    this.refreshData();
  },

  onReachBottom() {
    this.loadMore();
  },

  // 刷新数据
  async refreshData() {
    this.setData({
      refreshing: true,
      pageNum: 1,
      hasMore: true
    });
    
    try {
      await Promise.all([
        this.loadTaskList(true),
        this.loadStatistics()
      ]);
    } finally {
      this.setData({ refreshing: false });
      wx.stopPullDownRefresh();
    }
  },

  // 加载任务列表
  async loadTaskList(refresh = false) {
    if (!this.data.currentStudent) return;
    
    try {
      if (refresh) {
        this.setData({ pageLoading: true });
      }
      
      const params = {
        studentId: this.data.currentStudent.id,
        pageNum: refresh ? 1 : this.data.pageNum,
        pageSize: this.data.pageSize,
        status: this.data.filterStatus === 'all' ? '' : this.data.filterStatus,
        keyword: this.data.searchKeyword,
        textbookId: this.data.selectedTextbook ? this.data.selectedTextbook.id : '',
        unitId: this.data.selectedUnit? this.data.selectedUnit.id : '',
        chapterId: this.data.selectedChapter? this.data.selectedChapter.id : ''
      };
      
      const res = await api.getTaskList(params);
      
      if (res.success) {
        const newList = res.result.records || [];
        const taskList = refresh ? newList : [...this.data.taskList, ...newList];

        //设置 task状态
        taskList.forEach(task => {
          task.statusText = this.getStatusText(task.status);
        });
        
        this.setData({
          taskList: taskList,
          hasMore: newList.length >= this.data.pageSize,
          pageNum: refresh ? 2 : this.data.pageNum + 1
        });
      } else {
        this.showToast('加载任务列表失败', 'error');
      }
    } catch (error) {
      console.error('加载任务列表失败:', error);
      this.showToast('加载失败，请重试', 'error');
    } finally {
      this.setData({ pageLoading: false });
    }
  },

  // 加载更多
  async loadMore() {
    if (!this.data.hasMore || this.data.loadingMore) return;
    
    this.setData({ loadingMore: true });
    await this.loadTaskList();
    this.setData({ loadingMore: false });
  },

  // 加载统计数据
  async loadStatistics() {
    if (!this.data.currentStudent) return;
    
    try {
      const res = await api.getStudentOverview({studentId:this.data.currentStudent.id});
      
      if (res.success) {
        this.setData({ statistics: res.result });
      }
    } catch (error) {
      console.error('加载统计数据失败:', error);
    }
  },

  // 搜索任务
  onSearchInput(e) {
    this.setData({ searchKeyword: e.detail.value });
  },

  onSearchConfirm() {
    this.refreshData();
  },

  // 筛选状态
  onFilterStatus(e) {
    const status = e.currentTarget.dataset.status;
    this.setData({ filterStatus: status });
    this.refreshData();
  },

  // 查看任务详情
  viewTaskDetail(e) {
    console.log('查看任务详情', JSON.stringify(e));
    const taskId = e.currentTarget.dataset.taskId;
    wx.navigateTo({
      url: `/pages/task-manage/detail/index?taskId=${taskId}`
    });
  },

  // 开始听写
  startDictation:function(e) {
    console.log('开始听写', JSON.stringify(e));
    const { taskId } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/task-manage/dictation/index?taskId=${taskId}`
    });
  },
  
  // 批改任务
  correctTask(e) {
    const { taskId } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/task-manage/correct/index?taskId=${taskId}`
    });
  },

  // 显示创建任务模态框
  showCreateModal() {
    // 跳转到创建任务页面
    wx.navigateTo({
      url: '/pages/task-manage/create/index'
    });
  },

  // 筛选任务
  filterTasks() {
    this.setData({ showFilterModal: true });
  },
  
  // 隐藏筛选模态框
  hideFilterModal() {
    this.setData({ showFilterModal: false });
  },
  
  // 应用筛选
  applyFilter() {
    this.setData({ 
      showFilterModal: false,
      pageNum: 1,
      taskList: [],
      hasMore: true
    });
    this.loadTaskList(true);
  },
  
  // 重置筛选
  resetFilter() {
    this.setData({
      filterStatus: 'all',
      selectedTextbook: null,
      selectedUnit: null,
      selectedChapter: null,
      searchKeyword: ''
    });
    this.applyFilter();
  },
  
  // 筛选状态改变
  onFilterStatusChange(e) {
    this.setData({ filterStatus: e.detail.value });
  },
  
  // 教材选择改变
  onTextbookChange(e) {
    const textbookIndex = e.detail.value;
    const selectedTextbook = this.data.textbookList[textbookIndex];
    const textbookId = selectedTextbook ? selectedTextbook.id : '';
    this.setData({ 
      selectedTextbookIndex: textbookIndex,
      selectedTextbook,
      selectedUnit:null,
      selectedChapter: null,
      unitList: [],
      chapterList: []
    });
    if (selectedTextbook) {
      this.loadUnitList(textbookId);
    }
  },

    // 单元选择
    onUnitChange(e) {
      const unitIdIndex = e.detail.value;
      const selectedUnit = this.data.unitList[unitIdIndex];
      const unitId = selectedUnit? selectedUnit.id : ''
      const selectedUnitName = selectedUnit ? selectedUnit.name : '';
      
      this.setData({ 
        selectedUnitIndex: unitIdIndex,
        selectedUnit,
        selectedUnitName: selectedUnitName,
        selectedChapterName: '',
        selectedChapter: null,
        chapterList: []
      });
      
      if (unitId) {
        this.loadChapterList(unitId);
      }
    },
  
  // 章节选择改变
  onChapterChange(e) {
    const chapterIndex = e.detail.value;
    const selectedChapter = this.data.chapterList[chapterIndex];
    const chapterId = selectedChapter? selectedChapter.id : ''
    const selectedChapterName = selectedChapter ? selectedChapter.name : '';
    this.setData({ 
      selectedChapterIndex: chapterIndex,
      selectedChapterName: selectedChapterName,
      selectedChapter,
     });
  },
  
  // 筛选模态框显示状态改变
  onFilterModalChange(e) {
    if (!e.detail.visible) {
      this.setData({ showFilterModal: false });
    }
  },
  


  // 加载教材列表
  async loadTextbookList() {
    try {
      const res = await api.getTextbookList();
      if (res.success) {
        this.setData({ textbookList: res.result || [] });
      }
    } catch (error) {
      console.error('加载教材列表失败:', error);
    }
  },
  
  // 加载单元列表
  async loadUnitList(textbookId) {
    try {
      const res = await api.getTextbookDetailList({ textbookId, type: 'unit' });
      if (res.success) {
        this.setData({ unitList: res.result || [] });
      }
    } catch (error) {
      console.error('加载单元列表失败:', error);
    }
  },
  
  // 加载章节列表
  async loadChapterList(unitId) {
    try {
      const res = await api.getTextbookDetailList({ parentId: unitId, type: 'chapter' });
      if (res.success) {
        this.setData({ chapterList: res.result || [] });
      }
    } catch (error) {
      console.error('加载章节列表失败:', error);
    }
  },


  // 删除任务
  deleteTask(e) {
    const taskId = e.currentTarget.dataset.taskId;
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个任务吗？删除后无法恢复。',
      success: async (res) => {
        if (res.confirm) {
          try {
            const result = await api.deleteTask(taskId);
            
            if (result.success) {
              this.showToast('删除成功', 'success');
              this.refreshData();
            } else {
              this.showToast('删除失败', 'error');
            }
          } catch (error) {
            console.error('删除任务失败:', error);
            this.showToast('删除失败，请重试', 'error');
          }
        }
      }
    });
  },

  // 格式化日期
  formatDate(dateStr) {
    if (!dateStr) return '';
    const date = new Date(dateStr);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
  },

  // 获取任务状态文本
  getStatusText(status) {
    const statusMap = {
      'pending': '待做',
      'finished': '已做',
      'corrected': '已批改'
    };
    return statusMap[status] || status;
  },

  // 获取任务类型文本
  getTaskTypeText(type) {
    const typeMap = {
      'dictation': '听写',
      'review': '复习',
      'test': '测试'
    };
    return typeMap[type] || type;
  },

  // 显示Toast
  showToast(message, theme = 'info') {
    Toast({
      context: this,
      selector: '#t-toast',
      message: message,
      theme: theme,
      direction: 'column'
    });
  },

  // 页面分享
  onShareAppMessage() {
    return {
      title: '单词指南针 - 任务管理',
      path: '/pages/word-index/index'
    };
  },

  // 加载学生列表
  async loadStudentList() {
    try {
      const res = await api.getStudentList({ pageSize: 100 });
      if (res.success && res.result) {
        const studentList = res.result || []
        this.setData({
          studentList: studentList
        });

        // 如果没有当前学生，默认选择第一个
        if (!this.data.currentStudent && studentList.length > 0) {
          const firstStudent = studentList[0]
          this.setData({ currentStudent: firstStudent, userImageSrc: apiFileHost + firstStudent.avatar })
          // 保存到全局存储
          wx.setStorageSync('currentStudent', firstStudent)

          // 刷新数据
          //this.refreshData()
        } else if (studentList.length === 0) {
          // 没有学生时清空当前学生
          this.setData({ currentStudent: null })
          wx.removeStorageSync('currentStudent')
        }

      }
    } catch (error) {
      console.error('加载学生列表失败:', error);
    }
  },

  // 显示学生选择器
  showStudentSelect() {
    this.setData({
      showStudentSelector: true
    });
  },

  // 隐藏学生选择器
  hideStudentSelect() {
    this.setData({
      showStudentSelector: false
    });
  },

  // 学生选择器显示状态变化
  onStudentSelectorVisibleChange(e) {
    this.setData({
      showStudentSelector: e.detail.visible
    });
  },

  // 选择学生
  async selectStudent(e) {
    const student = e.currentTarget.dataset.student;
    
    // 更新当前学生
    this.setData({
      currentStudent: student,
      showStudentSelector: false
    });
    
    // 保存到全局存储
    wx.setStorageSync('currentStudent', student);
    
    // 刷新任务数据
    this.setData({
      pageLoading: true
    });
    
    try {
      await this.loadTaskList(true);
      await this.loadStatistics();
      this.showToast('学生切换成功', 'success');
    } catch (error) {
      console.error('切换学生失败:', error);
      this.showToast('切换学生失败', 'error');
    } finally {
      this.setData({
        pageLoading: false
      });
    }
  },

  // 跳转到添加学生页面
  goToAddStudent() {
    this.setData({
      showStudentSelector: false
    });
    wx.navigateTo({
      url: '/pages/student/add/index'
    });
  }
});