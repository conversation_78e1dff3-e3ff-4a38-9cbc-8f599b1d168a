/* 听写页面样式 */
.dictation-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding-bottom: 120rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  color: #ffffff;
}

.loading-text {
  margin-top: 24rpx;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 页面内容 */
.page-content {
  padding: 32rpx;
}

/* 任务信息头部 */
.task-header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.task-info {
  color: #ffffff;
}

.task-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.task-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

.meta-item text {
  margin-left: 8rpx;
}

/* 听写控制区域 */
.dictation-control {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

/* 进度显示 */
.progress-section {
  margin-bottom: 32rpx;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.progress-text {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;
}

.progress-percent {
  font-size: 24rpx;
  color: #0052d9;
  font-weight: 600;
}

/* 当前单词显示 */
.current-word-section {
  margin-bottom: 32rpx;
}

.word-display {
  text-align: center;
  padding: 40rpx 24rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12rpx;
  border: 2rpx dashed #0052d9;
}

.word-number {
  display: block;
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 16rpx;
}

.word-meaning {
  display: block;
  font-size: 48rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 16rpx;
  line-height: 1.3;
}

.word-hint {
  margin-top: 16rpx;
}

.word-hint text {
  font-size: 24rpx;
  color: #666666;
  font-style: italic;
}

/* 控制按钮 */
.control-buttons {
  display: flex;
  justify-content: center;
  margin-bottom: 32rpx;
}

.control-buttons .t-button {
  min-width: 200rpx;
}

/* 导航控制 */
.navigation-controls {
  display: flex;
  justify-content: space-between;
  gap: 16rpx;
}

.navigation-controls .t-button {
  flex: 1;
}

/* 听写设置 */
.dictation-settings {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.settings-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
}

.settings-header text {
  margin-left: 12rpx;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.setting-item .t-slider {
  width: 70%;
  font-size: 14px;
  display: flex;
  align-items: center;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-label {
  font-size: 28rpx;
  color: #333333;
  flex: 1;
}

.setting-value {
  font-size: 24rpx;
  color: #666666;
  margin-left: 16rpx;
  min-width: 60rpx;
  text-align: right;
}

/* 单词列表 */
.word-list-section {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #1a1a1a;
}

.section-header text {
  margin-left: 12rpx;
}

.word-list {
  max-height: 600rpx;
}

.word-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.word-item.active {
  background: linear-gradient(135deg, #0052d9 0%, #0040a3 100%);
  color: #ffffff;
  transform: scale(1.02);
  box-shadow: 0 4rpx 16rpx rgba(0, 82, 217, 0.3);
}

.word-item:active {
  transform: scale(0.98);
}

.word-number {
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  background: #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
  color: #666666;
  margin-right: 24rpx;
}

.word-item.active .word-number {
  background: rgba(255, 255, 255, 0.2);
  color: #ffffff;
}

.word-content {
  flex: 1;
}

.word-content .word-meaning {
  display: block;
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 8rpx;
  font-weight: 500;
}

.word-item.active .word-content .word-meaning {
  color: #ffffff;
}

.word-content .word-name {
  display: block;
  font-size: 24rpx;
  color: #666666;
}

.word-item.active .word-content .word-name {
  color: rgba(255, 255, 255, 0.8);
}

.word-status {
  margin-left: 16rpx;
}

/* 底部操作区 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  padding: 24rpx 32rpx;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.action-buttons {
  display: flex;
  gap: 16rpx;
}

.action-buttons .t-button {
  flex: 1;
}

.completion-notice {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16rpx;
  background: #f0f9ff;
  border-radius: 8rpx;
  margin-top: 16rpx;
}

.completion-notice text {
  margin-left: 12rpx;
  font-size: 28rpx;
  color: #00a870;
  font-weight: 500;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .page-content {
    padding: 24rpx;
  }
  
  .task-header {
    padding: 24rpx;
  }
  
  .dictation-control {
    padding: 24rpx;
  }
  
  .word-meaning {
    font-size: 40rpx;
  }
  
  .navigation-controls {
    flex-direction: column;
  }
  
  .navigation-controls .t-button {
    margin-bottom: 12rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .dictation-page {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
  }
  
  .dictation-control {
    background: #2d3748;
  }
  
  .word-display {
    background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
    border-color: #0052d9;
  }
  
  .word-meaning {
    color: #ffffff;
  }
  
  .word-number {
    color: #e2e8f0;
  }
  
  .dictation-settings {
    background: #2d3748;
  }
  
  .settings-header {
    color: #ffffff;
  }
  
  .setting-label {
    color: #e2e8f0;
  }
  
  .word-list-section {
    background: #2d3748;
  }
  
  .section-header {
    color: #ffffff;
  }
  
  .word-item {
    background: #4a5568;
  }
  
  .word-content .word-meaning {
    color: #e2e8f0;
  }
  
  .word-content .word-name {
    color: #a0aec0;
  }
  
  .bottom-actions {
    background: #2d3748;
    border-top-color: #4a5568;
  }
}