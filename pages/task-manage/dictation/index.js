const api = require('../../../utils/api');

Page({
  data: {
    // 页面状态
    pageLoading: true,
    
    // 任务信息
    taskId: '',
    taskInfo: null,
    taskDetails: [],
    
    // 听写状态
    currentIndex: 0,
    isPlaying: false,
    isPaused: false,
    isCompleted: false,
    
    // 听写设置
    playInterval: 5, // 播放间隔（秒）
    autoPlay: false,
    showWordList: true,
    
    // 播放控制
    intervalTimer: null,
    audioContext: null
  },
  
  onLoad(options) {
    const { taskId } = options;
    if (!taskId) {
      wx.showModal({
        title: '错误',
        content: '任务ID不能为空',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
      return;
    }
    
    this.setData({ taskId });
    this.initAudioContext();
    this.loadTaskInfo();
  },
  
  onUnload() {
    // 清理定时器和音频
    this.clearTimer();
    if (this.data.audioContext) {
      this.data.audioContext.destroy();
    }
  },
  
  // 初始化音频上下文
  initAudioContext() {
    const audioContext = wx.createInnerAudioContext({
      useWebAudioImplement: true // 是否使用 WebAudio 作为底层音频驱动，默认关闭。对于短音频、播放频繁的音频建议开启此选项，开启后将获得更优的性能表现。由于开启此选项后也会带来一定的内存增长，因此对于长音频建议关闭此选项
    });
    audioContext.onError((error) => {
      console.error('音频播放错误:', error);
      this.showToast('音频播放失败', 'error');
    });
    
    audioContext.onEnded(() => {
      this.setData({ isPlaying: false });
    });
    
    this.setData({ audioContext });
  },
  
  // 加载任务信息
  async loadTaskInfo() {
    try {
      this.setData({ pageLoading: true });
      
      // 获取任务详情
      const taskRes = await api.getTaskDetail({ taskId: this.data.taskId });
      if (!taskRes.success) {
        throw new Error(taskRes.message || '获取任务信息失败');
      }
      
      // 检查任务状态
      if (taskRes.result.status !== 'pending') {
        wx.showModal({
          title: '提示',
          content: '该任务已完成听写，无法重复操作',
          showCancel: false,
          success: () => {
            wx.navigateBack();
          }
        });
        return;
      }
      
      // 获取任务明细
      const detailRes = await api.getTaskDetailList({ taskId: this.data.taskId });
      if (!detailRes.success) {
        throw new Error(detailRes.message || '获取任务明细失败');
      }
      
      this.setData({
        taskInfo: taskRes.result,
        taskDetails: detailRes.result || []
      });
      
    } catch (error) {
      console.error('加载任务信息失败:', error);
      this.showToast(error.message || '加载失败', 'error');
      setTimeout(() => {
        wx.navigateBack();
      }, 2000);
    } finally {
      this.setData({ pageLoading: false });
    }
  },
  
  // 开始听写
  startDictation() {
    if (this.data.taskDetails.length === 0) {
      this.showToast('没有单词可以听写', 'error');
      return;
    }
    
    this.setData({ 
      isPlaying: true,
      currentIndex: 0
    });
    
    this.playCurrentWord();
    
    if (this.data.autoPlay) {
      this.startAutoPlay();
    }
  },
  
  // 暂停听写
  pauseDictation() {
    this.setData({ 
      isPlaying: false,
      isPaused: true
    });
    this.clearTimer();
    //this.data.audioContext.stop();
    this.data.audioContext.pause();
  },
  
  // 继续听写
  resumeDictation() {
    this.setData({ 
      isPlaying: true,
      isPaused: false
    });

    //继续播放
    if(this.data.audioContext.src && this.data.audioContext.src !==''){
      this.data.audioContext.play();
    }
    
    
    if (this.data.autoPlay) {
      this.startAutoPlay();
    }
  },

  //重播
  replayDictation() {
    this.setData({
      isPlaying: true,
      isPaused: false
    });
    this.data.audioContext.stop();
    this.initAudioContext();//重新初始化
    this.playCurrentWord();
  },
  
  // 播放当前单词
  playCurrentWord() {
    const currentWord = this.data.taskDetails[this.data.currentIndex];
    console.log("--playCurrentWord--", JSON.stringify(currentWord));
    if (!currentWord) return;
    
    // 如果没有音频文件，使用文字转语音API
    if (!currentWord.audioFileSrc) {
      this.playWordWithTTS(currentWord.wordName);
    }else{
      // 播放音频文件
      this.data.audioContext.src = currentWord.audioFileSrc;
      this.data.audioContext.play();
    }

  },
  
  // 使用TTS播放单词
   playWordWithTTS(word) {
    // 这里可以集成百度、腾讯等TTS服务
    // 暂时使用系统提示
    wx.showToast({
      title: `播放: ${word}`,
      icon: 'none',
      duration: 1000
    });
  }, 
  
  // 上一个单词
  previousWord() {
    this.data.audioContext.stop();
    this.initAudioContext();//重新初始化
    if (this.data.currentIndex > 0) {
      this.setData({
        isPlaying: true,
        isPaused: false
      });
      this.setData({ currentIndex: this.data.currentIndex - 1 });
      this.playCurrentWord();
    }
  },
  
  // 下一个单词
  nextWord() {
    this.data.audioContext.stop();
    this.initAudioContext();//重新初始化
    if (this.data.currentIndex < this.data.taskDetails.length - 1) {
      this.setData({
        isPlaying: true,
        isPaused: false
      });
      this.setData({ currentIndex: this.data.currentIndex + 1 });
      this.playCurrentWord();
    } else {
      // 已经是最后一个单词
      this.showToast('已经是最后一个单词了', 'none');
    }
  },
  
  // 跳转到指定单词
  goToWord(e) {
    this.data.audioContext.stop();
    this.initAudioContext();//重新初始化
    this.setData({
      isPlaying: true,
      isPaused: false
    });

    const { index } = e.currentTarget.dataset;
    this.setData({ currentIndex: index });
    this.playCurrentWord();
  },
  
  // 开始自动播放
  startAutoPlay() {
    this.clearTimer();
    
    const timer = setInterval(() => {
      if (this.data.currentIndex < this.data.taskDetails.length - 1) {
        this.nextWord();
      } else {
        // 自动播放完成
        this.clearTimer();
        this.setData({ isPlaying: false });
        this.showToast('自动播放完成', 'success');
      }
    }, this.data.playInterval * 1000);
    
    this.setData({ intervalTimer: timer });
  },
  
  // 清理定时器
  clearTimer() {
    if (this.data.intervalTimer) {
      clearInterval(this.data.intervalTimer);
      this.setData({ intervalTimer: null });
    }
  },
  
  // 设置播放间隔
  onIntervalChange(e) {
    this.setData({ playInterval: e.detail.value });
  },
  
  // 切换自动播放
  toggleAutoPlay(e) {
    const autoPlay = e.detail.value;
    this.setData({ autoPlay });
    
    if (autoPlay && this.data.isPlaying) {
      this.startAutoPlay();
    } else {
      this.clearTimer();
    }
  },
  
  // 切换单词列表显示
  toggleWordList(e) {
    this.setData({ showWordList: e.detail.value });
  },
  
  // 完成听写
  completeDictation() {
    wx.showModal({
      title: '确认完成',
      content: '确定要完成听写吗？完成后将无法重新听写。',
      success: async (res) => {
        if (res.confirm) {
          await this.updateTaskStatus();
        }
      }
    });
  },
  
  // 更新任务状态
  async updateTaskStatus() {
    try {
      wx.showLoading({ title: '提交中...' });
      const taskId = this.data.taskId;
      const res = await api.updateTaskStatus({
        taskId: this.data.taskId,
        status: 'finished'
      });
      
      if (res.success) {
        this.setData({ isCompleted: true });
        this.showToast('听写完成', 'success');
        
        setTimeout(() => {
          //wx.navigateBack();
          wx.navigateTo({
            url: `/pages/task-manage/detail/index?taskId=${taskId}`
          });
        }, 2000);
      } else {
        throw new Error(res.message || '更新状态失败');
      }
    } catch (error) {
      console.error('更新任务状态失败:', error);
      this.showToast(error.message || '提交失败', 'error');
    } finally {
      wx.hideLoading();
    }
  },
  
  // 重新开始
  restartDictation() {
    this.setData({
      currentIndex: 0,
      isPlaying: false,
      isPaused: false
    });
    this.clearTimer();
  },
  
  // 显示Toast
  showToast(message, type = 'none') {
    wx.showToast({
      title: message,
      icon: type === 'success' ? 'success' : type === 'error' ? 'error' : 'none',
      duration: 2000
    });
  },
  
  // 页面分享
  onShareAppMessage() {
    return {
      title: '单词听写',
      path: `/pages/task-manage/dictation/index?taskId=${this.data.taskId}`
    };
  }
});