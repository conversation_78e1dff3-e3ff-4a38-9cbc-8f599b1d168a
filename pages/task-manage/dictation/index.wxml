<view class="dictation-page">
  <!-- 页面加载状态 -->
  <view class="loading-container" wx:if="{{pageLoading}}">
    <t-loading theme="circular" size="40px" />
    <text class="loading-text">加载中...</text>
  </view>
  
  <!-- 主要内容 -->
  <view class="page-content" wx:else>
    <!-- 任务信息头部 -->
    <view class="task-header">
      <view class="task-info">
        <text class="task-title">{{taskInfo.taskName}}</text>
        <view class="task-meta">
          <view class="meta-item">
            <t-icon name="view-list" size="16" />
            <text>{{taskDetails.length}} 个单词</text>
          </view>
          <view class="meta-item">
            <t-icon name="time" size="16" />
            <text>{{taskInfo.createTime}}</text>
          </view>
          <view class="meta-item">
            <t-tag theme="warning" size="small">{{taskInfo.status === 'pending' ? '待做' : ''}}</t-tag>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 听写控制区域 -->
    <view class="dictation-control">
      <!-- 进度显示 -->
      <view class="progress-section">
        <view class="progress-info">
          <text class="progress-text">{{currentIndex + 1}} / {{taskDetails.length}}</text>
          <text class="progress-percent">{{ (currentIndex + 1) / taskDetails.length * 100 }}%</text>
        </view>
        <t-progress 
          percentage="{{(currentIndex + 1) / taskDetails.length * 100}}"
          stroke-width="8"
        />
      </view>
      
      <!-- 当前单词显示 -->
      <view class="current-word-section">
        <view class="word-display">
          <text class="word-number">第 {{currentIndex + 1}} 个</text>
          <text class="word-meaning">译义:{{taskDetails[currentIndex] ? taskDetails[currentIndex].word.translation : ''}}</text>
          <view class="word-hint" wx:if="{{taskDetails[currentIndex] && taskDetails[currentIndex].wordHint}}">
            <text>提示：{{taskDetails[currentIndex].wordHint}}</text>
          </view>
        </view>
      </view>
      
      <!-- 播放控制按钮 -->
      <view class="control-buttons">
        <t-button 
          wx:if="{{!isPlaying && !isPaused}}"
          theme="primary" 
          size="large"
          bind:tap="startDictation"
          disabled="{{isCompleted}}"
          icon='play-circle'
        >
          开始听写
        </t-button>
        
        <t-button 
          wx:elif="{{isPlaying}}"
          theme="warning" 
          size="large"
          bind:tap="pauseDictation"
          icon='pause-circle'
        >
         
          暂停
        </t-button>
        
        <t-button 
          wx:elif="{{isPaused}}"
          theme="primary" 
          size="large"
          bind:tap="resumeDictation"
          icon='play-circle'
        >
          继续
        </t-button>
      </view>
      
      <!-- 导航控制 -->
      <view class="navigation-controls">
        <t-button 
          theme="default" 
          size="medium"
          bind:tap="previousWord"
          disabled="{{currentIndex === 0 || isCompleted}}"
          icon="left"
        >
          上一个
        </t-button>
        
        <t-button 
          theme="primary" 
          size="medium"
          bind:tap="replayDictation"
          disabled="{{isCompleted}}"
          icon="sound"
        >
          重播
        </t-button>
        
        <t-button 
          theme="default" 
          size="medium"
          bind:tap="nextWord"
          disabled="{{currentIndex === taskDetails.length - 1 || isCompleted}}"
          icon="right"
        >
          下一个
        </t-button>
      </view>
    </view>
    
    <!-- 听写设置 -->
    <view class="dictation-settings">
      <view class="settings-header">
        <t-icon name="setting" size="20" />
        <text>听写设置</text>
      </view>
      
      <view class="setting-item">
        <text class="setting-label">播放间隔</text>
        <t-slider 
          value="{{playInterval}}"
          min="1"
          max="10"
          step="1"
          bind:change="onIntervalChange"
          disabled="{{isCompleted}}"
        />
        <text class="setting-value">{{playInterval}}秒</text>
      </view>
      
      <view class="setting-item">
        <text class="setting-label">自动播放</text>
        <t-switch 
          value="{{autoPlay}}"
          bind:change="toggleAutoPlay"
          disabled="{{isCompleted}}"
        />
      </view>
      
      <view class="setting-item">
        <text class="setting-label">显示单词列表</text>
        <t-switch 
          value="{{showWordList}}"
          bind:change="toggleWordList"
        />
      </view>
    </view>
    
    <!-- 单词列表 -->
    <view class="word-list-section" wx:if="{{showWordList}}">
      <view class="section-header">
        <t-icon name="view-list" size="20" />
        <text>单词列表</text>
      </view>
      
      <scroll-view class="word-list" scroll-y>
        <view 
          class="word-item {{index === currentIndex ? 'active' : ''}}"
          wx:for="{{taskDetails}}"
          wx:key="id"
          bind:tap="goToWord"
          data-index="{{index}}"
        >
          <view class="word-number">{{index + 1}}</view>
          <view class="word-content">
            <text class="word-meaning">译义:{{item.word.translation}}</text>
            <text class="word-name" wx:if="{{isCompleted}}">{{item.wordName}}</text>
          </view>
          <view class="word-status">
            <t-icon 
              name="{{index < currentIndex ? 'check-circle' : index === currentIndex ? 'play-circle' : 'radio'}}"
              size="20"
              color="{{index < currentIndex ? '#00a870' : index === currentIndex ? '#0052d9' : '#dcdcdc'}}"
            />
          </view>
        </view>
      </scroll-view>
    </view>
    
    <!-- 底部操作区 -->
    <view class="bottom-actions">
      <view class="action-buttons">
        <t-button 
          theme="default" 
          bind:tap="restartDictation"
          disabled="{{isCompleted}}"
        >
          重新开始
        </t-button>
        
        <t-button 
          theme="primary" 
          bind:tap="completeDictation"
          disabled="{{isCompleted}}"
        >
          完成听写
        </t-button>
      </view>
      
      <view class="completion-notice" wx:if="{{isCompleted}}">
        <t-icon name="check-circle" size="24" color="#00a870" />
        <text>听写已完成</text>
      </view>
    </view>
  </view>
</view>

<!-- Toast提示 -->
<t-toast id="t-toast" />