<view class="correct-page">
  <!-- 加载状态 -->
  <t-loading wx:if="{{loading}}" theme="circular" size="40rpx" text="加载中..." />

  <!-- 页面内容 -->
  <view wx:else class="page-content">
    <!-- 任务信息头部 -->
    <view class="task-header">
      <view class="task-title">{{taskInfo.taskName}}</view>
      <view class="task-meta">
        <text class="meta-item">单词数量：{{taskDetailList.length}}</text>
        <text class="meta-item">状态：{{taskInfo.status === 'finished' ? '已做' : '待批改'}}</text>
      </view>
    </view>

    <!-- 批改模式选择 -->
    <view class="mode-selector">
      <t-radio-group value="{{correctionMode}}" bind:change="onCorrectionModeChange">
        <t-radio label="自动批改" value="auto" />
        <t-radio label="手动批改" value="manual" />
      </t-radio-group>
    </view>

    <!-- 自动批改模式 -->
    <view wx:if="{{correctionMode === 'auto'}}" class="auto-mode">
      <!-- 上传图片区域 -->
      <view class="upload-section">
        <view class="section-title">拍照上传</view>
        <view class="upload-area" bind:tap="chooseImage">
          <view wx:if="{{!uploadedImage}}" class="upload-placeholder">
            <t-icon name="camera" size="60rpx" color="#ccc" />
            <text class="upload-text">点击拍照或选择图片</text>
          </view>
          <image wx:else src="{{uploadedImage}}" class="uploaded-image" bind:tap="previewImage" />
        </view>
        <view class="upload-tips">
          <text>• 请确保图片清晰，光线充足</text>
          <text>• 单词书写工整，便于识别</text>
        </view>
      </view>

      <!-- 识别结果 -->
      <view wx:if="{{recognizedText}}" class="recognition-result">
        <view class="section-title">识别结果</view>
        <view class="recognized-text">{{recognizedText}}</view>
      </view>
    </view>

    <!-- 手动批改模式 -->
    <view wx:if="{{correctionMode === 'manual'}}" class="manual-mode">
      <view class="section-title">手动输入学生答案</view>
      <view class="manual-input-list">
        <view wx:for="{{taskDetailList}}" wx:key="id" class="manual-input-item">
          <view class="word-info">
            <text class="word-number">{{index + 1}}.</text>
            <text class="word-meaning">{{item.word.translation}}</text>
            <text class="correct-answer">({{item.wordName}})</text>
          </view>
          <t-input
            placeholder="请输入学生答案"
            value="{{manualAnswers[index] || ''}}"
            data-taskDetailId="{{item.id}}"
            data-index="{{index}}"
            bind:change="onManualAnswerInput"
            class="answer-input"
          />
          <t-radio-group value="{{item.isCorrect}}" default-value="0" borderless t-class="isCorrect-box" data-taskDetailId="{{item.id}}"  data-index="{{index}}" bind:change="onIsCorrectChange">
            <t-radio value="1" allow-uncheck label="正确"/>
            <t-radio value="0" allow-uncheck label="错误"/>
          </t-radio-group>
        </view>
      </view>
      
      <view class="manual-actions">
        <t-button theme="primary" size="large" bind:tap="performManualCorrection">
          开始批改
        </t-button>
      </view>
    </view>

    <!-- 批改结果 -->
    <view wx:if="{{showResults}}" class="correction-results">
      <view class="section-title">批改结果</view>
      
      <!-- 统计信息 -->
      <view class="result-summary">
        <view class="summary-item correct">
          <text class="summary-number">{{correctCount}}</text>
          <text class="summary-label">正确</text>
        </view>
        <view class="summary-item wrong">
          <text class="summary-number">{{wrongCount}}</text>
          <text class="summary-label">错误</text>
        </view>
        <view class="summary-item accuracy">
          <text class="summary-number">{{accuracy}}%</text>
          <text class="summary-label">正确率</text>
        </view>
      </view>

      <!-- 详细结果 -->
      <view class="result-details">
        <view wx:for="{{correctionResults}}" wx:key="id" class="result-item {{item.isCorrect === 1 ? 'correct' : 'wrong'}}">
          <view class="result-header">
            <text class="word-number">{{index + 1}}.</text>
            <text class="word-meaning">{{item.wordTranslation}}</text>
            <t-icon name="{{item.isCorrect  === 1 ? 'check-circle-filled' : 'close-circle-filled'}}" 
                   color="{{item.isCorrect  === 1 ? '#00a870' : '#e34d59'}}" 
                   size="32rpx" />
          </view>
          <view class="result-content">
            <view class="answer-row">
              <text class="label">正确答案：</text>
              <text class="correct-word">{{item.wordName}}</text>
            </view>
            <view class="answer-row">
              <text class="label">学生答案：</text>
              <text class="student-word {{item.isCorrect  === 1 ? 'correct' : 'wrong'}}">{{item.studentAnswer || '未作答'}}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部操作区 -->
    <view class="bottom-actions">
      <t-button wx:if="{{showResults}}" theme="default" size="large" bind:tap="resetCorrection">
        重新批改
      </t-button>
      <t-button wx:if="{{showResults}}" theme="primary" size="large" bind:tap="submitCorrection">
        提交结果
      </t-button>
    </view>
  </view>
</view>