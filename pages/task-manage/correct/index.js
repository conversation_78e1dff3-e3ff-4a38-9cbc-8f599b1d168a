const app = getApp()
const host = getApp().globalData.apiHost
const apiFileHost = getApp().globalData.apiFileHost
const api = require('../../../utils/api');
import Toast from 'tdesign-miniprogram/toast/index';


Page({
  data: {
    taskId: '',
    taskInfo: null,//任务信息
    taskDetailList: [],//任务明细列表
    loading: true,
    correctionMode: 'auto', // auto: 自动批改, manual: 手动批改
    uploadedImage: '',
    recognizedText: '',
    correctionResults: [],//批改结果
    showResults: false,
    correctCount: 0,// 正确的数量
    wrongCount: 0,// 错误的数量
    accuracy: 0,// 正确率
    currentIndex: 0,
    manualAnswers: {},
    correctRes: [],// 答案是否正确 {id,isCorrect}
  },

  onLoad(options) {
    if (options.taskId) {
      this.setData({ taskId: options.taskId });
      this.loadTaskInfo();
      this.loadTaskDetails();
    } else {
      wx.showToast({
        title: '任务ID不能为空',
        icon: 'error'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 加载任务信息
  async loadTaskInfo() {
    try {
      const res = await api.getTaskDetail({taskId: this.data.taskId});
      console.log(" loadTaskInfo- " + JSON.stringify(res))
      if (res.success) {
        this.setData({
          taskInfo: res.result
        });
      } else {
        wx.showToast({
          title: res.message || '获取任务信息失败',
          icon: 'error'
        });
      }
    } catch (error) {
      console.error('加载任务信息失败:', error);
      wx.showToast({
        title: '网络错误',
        icon: 'error'
      });
    }
  },

  // 加载任务明细
  async loadTaskDetails() {
    try {
      const res = await api.getTaskDetailList(
         {
          taskId: this.data.taskId
        }
      );

      if (res.success) {
        const taskDetailList = res.result.map(item => ({
          ...item,
          studentAnswer: '',
          isCorrect: "0",
          checked: false
        }));
        
        this.setData({
          taskDetailList,
          loading: false
        });
      } else {
        wx.showToast({
          title: res.message || '获取任务明细失败',
          icon: 'error'
        });
        this.setData({ loading: false });
      }
    } catch (error) {
      console.error('加载任务明细失败:', error);
      wx.showToast({
        title: '网络错误',
        icon: 'error'
      });
      this.setData({ loading: false });
    }
  },

  // 切换批改模式
  onCorrectionModeChange(e) {
    this.setData({
      correctionMode: e.detail.value,
      uploadedImage: '',
      recognizedText: '',
      showResults: false
    });
  },

  // 选择图片
  chooseImage() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFiles[0].tempFilePath;
        this.setData({
          uploadedImage: tempFilePath
        });
        this.uploadAndRecognize(tempFilePath);
      },
      fail: (error) => {
        console.error('选择图片失败:', error);
        wx.showToast({
          title: '选择图片失败',
          icon: 'error'
        });
      }
    });
  },


  //微信小程序 上传方法
  async uploadImage(filePath) {
    const task = wx.uploadFile({
      url: host + '/word/api/wechat/annex/upload', // 仅为示例，非真实的接口地址
      filePath: filePath,
      name: 'file',
      formData: { bizPath:'task_correct_images' },
      header:{
        'Content-Type': 'application/json',
        'authStr': wx.getStorageSync('authStr'),
      },
      success: async (res) =>  {
        console.log("uploadImage--upload-file--res--" + JSON.stringify(res))
        let res_data = JSON.parse(res.data)
        console.log("uploadImage--upload-file--res_data--", res_data)
        if(res_data.code == 200){
          let data = res_data.result
          let filePathName = data.filePathName
          if(data && data != ''){
            Toast({
              context: this,
              selector: '#t-toast',
              message: '上传成功!',
            });

            let imageSrc = apiFileHost + filePathName
            console.log("uploadImage--upload-file--imageSrc--", imageSrc)

            this.setData({
              filePathName: filePathName,
              imageSrc,
            });

            //TODO 调用API对图片进行识别
            //const mockRecognizedText =  await this.generateMockRecognizedText();//测试模拟识别结果
            const detailList = await this.recognizeImage(filePathName);//调用API对图片进行识别
            console.log("uploadImage--upload-file--detailList--", detailList)
            
          }else{
            Toast({
              context: this,
              selector: '#t-toast',
              message: '上传失败!重新上传!',
            });
          }
          
        }else{
          Toast({
            context: this,
            selector: '#t-toast',
            message: '出现问题!重新上传!',
          });
        }

      },
    });


  },


  // 上传图片并识别
  async uploadAndRecognize(filePath) {
    try {
      // 上传图片并自动识别
      await this.uploadImage(filePath);
    } catch (error) {
      console.error('图片识别失败:', error);
      wx.showToast({
        title: '图片识别失败',
        icon: 'error'
      });
    }
  },


  //调用API 对图片进行识别
  async recognizeImage(filePathName) {
    try {
      wx.showLoading({
        title: '识别中...'
      });
      
      // 调用自动解析图片文字接口
      const res = await api.parseImageAutoCorrect({
        taskId: this.data.taskId,
        filePathName: filePathName
      });
      
      wx.hideLoading();
      
      if (res.success) {
        const detailList = res.result.detailList;
        
        if (detailList) {
          console.log("recognizeImage--detailList--", detailList);
          
          // 执行自动批改
          this.performAutoCorrection(detailList);
          
          wx.showToast({
            title: '识别成功',
            icon: 'success'
          });
        } else {
          wx.showToast({
            title: '未识别到文字',
            icon: 'none'
          });
        }
      } else {
        wx.showToast({
          title: res.message || '识别失败',
          icon: 'error'
        });
      }
    } catch (error) {
      console.error('图片识别失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '识别失败',
        icon: 'error'
      });
    }
  },

  // 生成模拟识别文本（实际应该调用OCR接口）
  async generateMockRecognizedText() {
    const words = this.data.taskDetailList.map(item => item.wordName);
    // 模拟一些错误
    const mockAnswers = words.map(word => {
      const random = Math.random();
      if (random < 0.8) {
        return word; // 80%正确
      } else {
        return word.substring(0, word.length - 1) + 'x'; // 20%错误
      }
    });
    return mockAnswers.join('\n');
  },

  // 执行自动批改
  performAutoCorrection(detailList) {
    console.log("--执行自动批改--performAutoCorrection--detailList--", detailList)
    const answerList = detailList;
    const results = [];
    let correctCount = 0;
    let wrongCount = 0;

    this.data.taskDetailList.forEach((item, index) => {
      // 从answerList中找到对应的答案
      const studentAnswer = answerList.find(answer => answer.id === item.id)?.studentAnswer || '';
      const isCorrect = answerList.find(answer => answer.id === item.id)?.isCorrect;

      item.studentAnswer = studentAnswer
      item.isCorrect = isCorrect == 1?"1":"0"
      
      if (isCorrect === 1) {
        correctCount++;
      } else {
        wrongCount++;
      }

      results.push({
        ...item,
        studentAnswer,
        isCorrect,
        checked: true
      });
    });

    const accuracy = this.data.taskDetailList.length > 0 
      ? Math.round((correctCount / this.data.taskDetailList.length) * 100) 
      : 0;

    this.setData({
      correctionResults: results,
      correctCount,
      wrongCount,
      accuracy,
      showResults: true
    });
  },

  // 手动输入答案
  onManualAnswerInput(e) {
    const { index,  taskdetailid } = e.currentTarget.dataset;
    const value = e.detail.value;
    const manualAnswers = { ...this.data.manualAnswers };
    manualAnswers[index] = value;
    this.setData({ manualAnswers });

    //自动对比一下 答案 然后设置 批改结果 isCorrect 到 correctRes
    // 找到对应的元素并更新 isCorrect 属性
    const item = this.data.taskDetailList.find(item => item.id === taskdetailid)
    const isCorrect = value.toLowerCase().trim() === item.wordName.toLowerCase().trim();
    item.isCorrect = isCorrect?"1":"0"
    item.studentAnswer = value
    //把item 保存到 taskDetailList 中
    this.data.taskDetailList.forEach((item2, index) => {
      if(item2.id === taskdetailid){
        item2.studentAnswer = value
        item2.isCorrect = isCorrect?"1":"0"
      }
    })

    let correctRes = this.data.correctRes
    //找到对应的元素并更新 isCorrect 属性
    let correctResItem = correctRes.find(res => res.taskdetailid == taskdetailid)
    if(correctResItem){
      correctResItem.isCorrect = isCorrect?"1":"0"
    }else{
      correctRes.push({
        taskdetailid: taskdetailid,
        isCorrect: isCorrect?"1":"0",
      })
    }
    this.setData({ correctRes});
    console.log("--onManualAnswerInput--correctRes-- " + JSON.stringify(this.data.correctRes))


    this.setData({ taskDetailList: this.data.taskDetailList });
    console.log("--onManualAnswerInput-- " + JSON.stringify(this.data.taskDetailList))
  },
// 手动判断 批改结果是否正确
  onIsCorrectChange(e) {
    console.log("--onIsCorrectChange-- " + JSON.stringify(e))
    const { index, taskdetailid } = e.currentTarget.dataset;
    const value = e.detail.value;
    let correctRes = [...this.data.correctRes]
    // 找到对应的元素并更新 isCorrect 属性
    let correctResItem = correctRes.find(res => res.taskdetailid == taskdetailid)
    if(correctResItem){
      correctResItem.isCorrect = value
    }else{
      correctRes.push({
        taskdetailid: taskdetailid,
        isCorrect: value,
      })
    }
    
    this.setData({ correctRes });
    console.log("--onIsCorrectChange--correctRes-- " + JSON.stringify(correctRes))

    //将批改结果 设置到 taskdetail 中
    //console.log("--taskDetailList-- " + JSON.stringify(this.data.taskDetailList))
    this.data.taskDetailList.forEach((item, index) => {
      if(item.id === taskdetailid){
        item.isCorrect = value
      }
    })
    this.setData({ taskDetailList: this.data.taskDetailList });
    console.log("--taskDetailList-- " + JSON.stringify(this.data.taskDetailList))

  },

  // 执行手动批改
  performManualCorrection() {
    const results = [];
    let correctCount = 0;
    let wrongCount = 0;

    this.data.taskDetailList.forEach((item, index) => {
      const studentAnswer = this.data.manualAnswers[index] || '';
      //const isCorrect = studentAnswer.toLowerCase().trim() === item.wordName.toLowerCase().trim();//自动对比批改
      const isCorrect = this.data.correctRes.find(res => res.taskdetailid === item.id)?.isCorrect  
      
      if (isCorrect === "1") {
        correctCount++;
      } else {
        wrongCount++;
      }

      results.push({
        ...item,
        studentAnswer,
        isCorrect,
        checked: true
      });
    });

    const accuracy = this.data.taskDetailList.length > 0 
      ? Math.round((correctCount / this.data.taskDetailList.length) * 100) 
      : 0;

    this.setData({
      correctionResults: results,
      correctCount,
      wrongCount,
      accuracy,
      showResults: true
    });
  },

  // 提交批改结果
  async submitCorrection() {
    if (!this.data.showResults) {
      wx.showToast({
        title: '请先完成批改',
        icon: 'error'
      });
      return;
    }

    wx.showLoading({
      title: '提交中...'
    });

    try {
      // 准备批改结果数据
      const correctionResults = this.data.taskDetailList.map(item => ({
        taskDetailId: item.id,
        studentAnswer: item.studentAnswer || '',
        isCorrect: item.isCorrect
      }))
      
      // 保存批改结果
      const saveRes = await api.saveCorrectionResult({
        taskId: this.data.taskId,
        correctionResults: correctionResults
      })
      
      if (saveRes.success) {
        wx.hideLoading();
        wx.showToast({
          title: '提交成功',
          icon: 'success'
        })
        
        // 返回上一页
        setTimeout(() => {
          //wx.navigateBack()
          wx.navigateTo({
            url: '/pages/task-manage/detail/index?taskId=' + this.data.taskId,
          })
        }, 1500)
      } else {
        wx.hideLoading();
        wx.showToast({
          title: saveRes.message || '提交失败',
          icon: 'error'
        })
      }
    } catch (error) {
      console.error('提交批改结果失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '网络错误',
        icon: 'error'
      });
    }
  },

  // 重新批改
  resetCorrection() {
    this.setData({
      uploadedImage: '',
      recognizedText: '',
      correctionResults: [],
      showResults: false,
      correctCount: 0,
      wrongCount: 0,
      accuracy: 0,
      manualAnswers: {}
    });
  },

  // 预览上传的图片
  previewImage() {
    if (this.data.uploadedImage) {
      wx.previewImage({
        urls: [this.data.uploadedImage]
      });
    }
  }
});