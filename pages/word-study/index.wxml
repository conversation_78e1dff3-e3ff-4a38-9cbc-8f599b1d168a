<view class="word-study">
  <!-- 页面加载状态 -->
  <t-loading wx:if="{{pageLoading}}" theme="circular" size="40rpx" text="加载中..." />
  
  <!-- 主要内容 -->
  <view wx:else class="content">
    <!-- 顶部进度条 -->
    <view class="progress-section">
      <view class="progress-header">
        <text class="task-title">{{taskInfo.taskName || '单词学习'}}</text>
        <text class="progress-text">{{progress.current}}/{{progress.total}}</text>
      </view>
      
      <view class="progress-bar">
        <view 
          class="progress-fill" 
          style="width: {{progress.total > 0 ? (progress.current / progress.total * 100) : 0}}%"
        ></view>
      </view>
      
      <view class="progress-stats">
        <view class="stat-item correct">
          <t-icon name="check-circle" size="24rpx" />
          <text>{{progress.correct}}</text>
        </view>
        <view class="stat-item wrong">
          <t-icon name="close-circle" size="24rpx" />
          <text>{{progress.wrong}}</text>
        </view>
      </view>
    </view>

    <!-- 单词学习区域 -->
    <view wx:if="{{!showResult}}" class="study-section">
      <!-- 单词卡片 -->
      <view class="word-card">
        <view class="word-header">
          <text class="word-number">第 {{currentWordIndex + 1}} 个单词</text>
          <view class="word-actions">
            <t-button 
              size="small" 
              theme="default" 
              variant="text"
              bind:tap="showHint"
              wx:if="{{settings.showHint}}"
            >
              <t-icon name="help-circle" size="28rpx" />
              提示
            </t-button>
          </view>
        </view>
        
        <!-- 音频播放区域 -->
        <view class="audio-section">
          <view class="audio-player" bind:tap="playWord">
            <view class="play-button {{isPlaying ? 'playing' : ''}}">
              <t-icon 
                name="{{isPlaying ? 'pause-circle-filled' : 'play-circle-filled'}}" 
                size="120rpx" 
                color="#0052d9" 
              />
            </view>
            <text class="play-text">
              {{isPlaying ? '播放中...' : '点击播放'}}
            </text>
            <text class="play-count">已播放 {{playCount}}/{{maxPlayCount}} 次</text>
          </view>
        </view>
        
        <!-- 答案输入区域 -->
        <view class="answer-section">
          <view class="input-container">
            <t-input 
              value="{{userAnswer}}"
              placeholder="请输入听到的单词"
              bind:change="onAnswerInput"
              disabled="{{showAnswer}}"
              class="answer-input"
            />
          </view>
          
          <!-- 答案反馈 -->
          <view wx:if="{{showAnswer}}" class="answer-feedback">
            <view class="feedback-item {{results[currentWordIndex].isCorrect ? 'correct' : 'wrong'}}">
              <t-icon 
                name="{{results[currentWordIndex].isCorrect ? 'check-circle-filled' : 'close-circle-filled'}}" 
                size="32rpx" 
              />
              <text class="feedback-text">
                {{results[currentWordIndex].isCorrect ? '回答正确' : '回答错误'}}
              </text>
            </view>
            
            <view wx:if="{{!results[currentWordIndex].isCorrect}}" class="correct-answer">
              <text class="label">正确答案：</text>
              <text class="answer">{{currentWord.wordName}}</text>
            </view>
            
            <view wx:if="{{currentWord.translation}}" class="word-translation">
              <text class="label">中文释义：</text>
              <text class="translation">{{currentWord.translation}}</text>
            </view>
          </view>
        </view>
        
        <!-- 操作按钮 -->
        <view class="action-buttons">
          <t-button 
            wx:if="{{!showAnswer}}"
            theme="primary"
            size="large"
            bind:tap="submitAnswer"
            disabled="{{!userAnswer.trim()}}"
            block
          >
            提交答案
          </t-button>
          
          <view wx:else class="next-buttons">
            <t-button 
              wx:if="{{currentWordIndex > 0}}"
              theme="default"
              size="medium"
              bind:tap="prevWord"
            >
              上一个
            </t-button>
            
            <t-button 
              theme="primary"
              size="medium"
              bind:tap="nextWord"
              class="next-btn"
            >
              {{currentWordIndex >= wordList.length - 1 ? '查看结果' : '下一个'}}
            </t-button>
          </view>
        </view>
      </view>
    </view>

    <!-- 结果页面 -->
    <view wx:if="{{showResult}}" class="result-section">
      <view class="result-card">
        <view class="result-header">
          <t-icon name="check-circle-filled" size="80rpx" color="#00a870" />
          <text class="result-title">学习完成</text>
        </view>
        
        <view class="result-stats">
          <view class="stat-row">
            <text class="stat-label">总单词数：</text>
            <text class="stat-value">{{progress.total}}</text>
          </view>
          <view class="stat-row">
            <text class="stat-label">正确数：</text>
            <text class="stat-value correct">{{progress.correct}}</text>
          </view>
          <view class="stat-row">
            <text class="stat-label">错误数：</text>
            <text class="stat-value wrong">{{progress.wrong}}</text>
          </view>
          <view class="stat-row">
            <text class="stat-label">正确率：</text>
            <text class="stat-value">
              {{progress.total > 0 ? Math.round(progress.correct / progress.total * 100) : 0}}%
            </text>
          </view>
        </view>
        
        <view class="result-actions">
          <t-button 
            theme="default"
            size="large"
            bind:tap="viewWrongWords"
            wx:if="{{progress.wrong > 0}}"
          >
            查看错词 ({{progress.wrong}})
          </t-button>
          
          <t-button 
            theme="primary"
            size="large"
            bind:tap="submitTask"
            class="submit-btn"
          >
            提交结果
          </t-button>
          
          <t-button 
            theme="default"
            size="large"
            bind:tap="restart"
            variant="text"
          >
            重新开始
          </t-button>
        </view>
      </view>
    </view>
  </view>

  <!-- 设置面板 -->
  <view class="settings-panel">
    <view class="setting-item">
      <text class="setting-label">自动播放</text>
      <t-switch 
        value="{{settings.autoPlay}}"
        data-field="autoPlay"
        bind:change="onSettingChange"
      />
    </view>
    
    <view class="setting-item">
      <text class="setting-label">显示提示</text>
      <t-switch 
        value="{{settings.showHint}}"
        data-field="showHint"
        bind:change="onSettingChange"
      />
    </view>
    
    <view class="setting-item">
      <text class="setting-label">播放速度</text>
      <t-slider 
        value="{{settings.playSpeed * 100}}"
        min="50"
        max="150"
        step="25"
        data-field="playSpeed"
        bind:change="onSettingChange"
      />
    </view>
  </view>

  <!-- Toast 提示 -->
  <t-toast id="t-toast" />
</view>