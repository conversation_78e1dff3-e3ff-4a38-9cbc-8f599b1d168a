/* 用户中心页面样式 */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 30%, #764ba2 90%);
  padding-bottom: 80rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.loading-text {
  color: #fff;
  font-size: 32rpx;
}

.content {
  padding: 0;
}

/* 用户信息区域 */
.user-info-section {
  display: flex;
  align-items: center;
  padding: 60rpx 40rpx 40rpx;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  margin: 0rpx 20rpx 20rpx;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.user-avatar {
  position: relative;
  margin-right: 30rpx;
}

.avatar-img {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.user-level {
  position: absolute;
  bottom: -8rpx;
  right: -8rpx;
  background: #ffd700;
  color: #333;
  font-size: 20rpx;
  font-weight: 600;
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
  border: 2rpx solid #fff;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 26rpx;
  font-weight: 600;
  color: #fff;
  margin-bottom: 8rpx;
}

.user-type {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  background: rgba(255, 255, 255, 0.2);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  display: inline-block;
  margin-bottom: 12rpx;
}

.user-exp {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.exp-bar {
  flex: 1;
  height: 8rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4rpx;
  overflow: hidden;
}

.exp-fill {
  height: 100%;
  background: #ffd700;
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.exp-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  white-space: nowrap;
}

.login-btn {
  margin-left: 20rpx;
}

/* 通用标题样式 */
.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 快捷操作区域 */
.quick-actions-section {
  background: #fff;
  margin: 0 20rpx 20rpx;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.quick-actions-grid {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.quick-action-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: #f8f9ff;
  border-radius: 16rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.quick-action-item:active {
  background: #e8edff;
  border-color: #667eea;
  transform: scale(0.98);
}

.action-icon {
  margin-right: 24rpx;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 12rpx;
}

.action-content {
  flex: 1;
}

.action-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 4rpx;
}

.action-desc {
  font-size: 26rpx;
  color: #666;
}

/* 学习统计区域 */
.stats-section {
  background: #fff;
  margin: 0 30rpx 30rpx;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.stats-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 30rpx;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 40rpx;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 26rpx;
  color: #666;
}

/* 任务进度区域 */
.progress-section {
  background: #fff;
  margin: 0 30rpx 30rpx;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.progress-content {
  /* 进度内容样式 */
}

.progress-item {
  margin-bottom: 20rpx;
}

.progress-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
  font-size: 28rpx;
  color: #333;
}

.progress-count {
  font-weight: 600;
  color: #667eea;
}

.progress-bar {
  height: 12rpx;
  background: #f0f0f0;
  border-radius: 6rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 6rpx;
  transition: width 0.3s ease;
}

/* 成就展示区域 */
.achievements-section {
  background: #fff;
  margin: 0 30rpx 30rpx;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.achievement-count {
  font-size: 26rpx;
  color: #667eea;
  font-weight: 500;
}

.achievements-grid {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.achievement-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.achievement-item.unlocked {
  background: #fff7e6;
  border: 2rpx solid #ffd700;
}

.achievement-item.locked {
  background: #f5f5f5;
  border: 2rpx solid #e0e0e0;
  opacity: 0.6;
}

.achievement-item:active {
  transform: scale(0.98);
}

.achievement-icon {
  margin-right: 20rpx;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8rpx;
}

.achievement-info {
  flex: 1;
}

.achievement-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 4rpx;
}

.achievement-progress {
  font-size: 24rpx;
  color: #666;
}

/* 功能菜单区域 */
.menu-section {
  background: #fff;
  margin: 0 20rpx 20rpx;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.menu-list {
  /* 菜单列表样式 */
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background-color: #f8f9ff;
  transform: scale(0.98);
}

.menu-icon {
  margin-right: 24rpx;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 12rpx;
}

.menu-content {
  flex: 1;
}

.menu-title-text {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
}

.menu-desc {
  font-size: 26rpx;
  color: #666;
}

.menu-arrow {
  margin-left: 16rpx;
}

/* 退出登录区域 */
.logout-section {
  margin: 40rpx 30rpx 0;
  display: flex;
  flex-flow: row;
  justify-content: space-between;
  gap: 20rpx;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .stats-grid {
    grid-template-columns: 1fr 1fr;
  }
  
  .stat-number {
    font-size: 36rpx;
  }
  
  .stat-label {
    font-size: 24rpx;
  }
}

@media (max-width: 600rpx) {
  .user-info-section {
    padding: 40rpx 30rpx;
  }
  
  .avatar-img {
    width: 100rpx;
    height: 100rpx;
  }
  
  .user-name {
    font-size: 32rpx;
  }
  
  .section-title {
    font-size: 28rpx;
  }
}

/* 响应式适配 */
@media (max-width: 375px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 20rpx;
  }
  
  .stat-number {
    font-size: 40rpx;
  }
  
  .user-info-section {
    padding: 50rpx 30rpx 30rpx;
  }
  
  .avatar-img {
    width: 100rpx;
    height: 100rpx;
    border-radius: 50rpx;
  }
  
  .user-name {
    font-size: 32rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .stats-section,
  .progress-section,
  .menu-section {
    background: #1a1a1a;
    color: #fff;
  }
  
  .stats-title,
  .progress-title,
  .menu-title {
    color: #fff;
  }
  
  .stat-item,
  .menu-item {
    background: #2a2a2a;
  }
  
  .menu-title-text {
    color: #fff;
  }
  
  .menu-desc {
    color: #ccc;
  }
  
  .progress-bar {
    background: #333;
  }
}

/* 动画效果 */
.stats-section,
.progress-section,
.menu-section {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stat-item {
  transition: all 0.3s ease;
}

.stat-item:active {
  transform: scale(0.95);
}

/* 加载状态 */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60rpx 40rpx;
  color: #999;
}

.empty-state .empty-icon {
  font-size: 120rpx;
  margin-bottom: 20rpx;
}

.empty-state .empty-text {
  font-size: 28rpx;
}

