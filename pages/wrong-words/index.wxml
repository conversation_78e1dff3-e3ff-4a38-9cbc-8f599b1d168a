<view class="wrong-words-page">
  <!-- 顶部导航栏 -->
  <view class="nav-header" hidden="true">
    <view class="nav-content">
      <view class="nav-title">
        <t-icon name="error-circle" size="48rpx" color="#fff" />
        <text class="title-text">错词本</text>
      </view>
      <view class="nav-subtitle">掌握错词，提升词汇</view>
    </view>
  </view>

  <!-- 学生信息区域 -->
  <view class="student-section">
    <view class="student-card">
      <view wx:if="{{currentStudent}}" class="student-info">
        <view class="student-avatar">
          <t-avatar size="80rpx" image="{{userImageSrc || '/pages/image/default-avatar.png'}}" />
        </view>
        <view class="student-details">
          <text class="student-name">{{currentStudent.studentName}}</text>
          <text class="student-grade">{{currentStudent.grade}}</text>
        </view>
      </view>
      <view wx:else class="student-info">
        <view class="student-avatar empty">
          <t-icon name="user-add" size="44rpx" color="#999" />
        </view>
        <view class="student-details">
          <text class="student-name empty">未选择学生</text>
          <text class="student-grade">请先选择学生查看错词</text>
        </view>
      </view>
      <t-button 
        size="small" 
        theme="light" 
        variant=""
        shape=""
        bind:tap="showStudentSelect"
        class="switch-btn"
        icon='swap'
      >
        切换
      </t-button>
    </view>
  </view>

  <!-- 页面加载状态 -->
  <view wx:if="{{loading && wrongWords.length === 0}}" class="loading-container">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>
  </view>

  <!-- 主要内容 -->
  <view wx:else class="content">
    <!-- 统计卡片区域 -->
    <view class="statistics-section">
      <view class="stats-container">
        <view class="stat-card total" bindtap="onStatCardTap" data-type="total">
          <view class="stat-icon">
            <t-icon name="error-circle" size="44rpx" color="#ff6b6b" />
          </view>
          <view class="stat-content">
            <text class="stat-label">总错词</text>
            <text class="stat-number">{{statistics.total}}</text>
          </view>
        </view>
        
        <view class="stat-card recent" bindtap="onStatCardTap" data-type="recent">
          <view class="stat-icon">
            <t-icon name="time" size="44rpx" color="#ffa726" />
          </view>
          <view class="stat-content">
            <text class="stat-label">最近错误</text>
            <text class="stat-number">{{statistics.recent}}</text>
          </view>
        </view>
        
        <view class="stat-card reviewing" bindtap="onStatCardTap" data-type="reviewing">
          <view class="stat-icon">
            <t-icon name="refresh" size="44rpx" color="#42a5f5" />
          </view>
          <view class="stat-content">
            <text class="stat-label">复习中</text>
            <text class="stat-number">{{statistics.reviewing}}</text>
          </view>
        </view>
        
        <view class="stat-card mastered" bindtap="onStatCardTap" data-type="mastered">
          <view class="stat-icon">
            <t-icon name="check-circle" size="44rpx" color="#66bb6a" />
          </view>
          <view class="stat-content">
            <text class="stat-label">已掌握</text>
            <text class="stat-number">{{statistics.mastered}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 搜索和筛选区域 -->
    <view class="search-filter-section">
      <view class="search-filter-card">
        <!-- 搜索框 -->
        <view class="search-container">
          <t-search 
            value="{{searchKeyword}}"
            placeholder="搜索单词或释义"
            shape=""
            bind:change="onSearchInput"
            bind:submit="onSearchSubmit"
            bind:clear="onSearchClear"
            class="search-input"
          />
          <t-button 
            size="small" 
            theme="light" 
            variant=""
            shape=""
            bind:tap="showFilterModal"
            icon='filter'
          >
            条件
          </t-button>
        </view>
        
        <!-- 快速筛选标签 -->
        <view class="quick-filter">
          <text class="sort-label">筛选：</text>
          <t-tag 
            wx:for="{{[{key: 'all', label: '全部', icon: 'view-list'}, {key: 'recent', label: '最近', icon: 'time'}, {key: 'frequent', label: '高频', icon: 'trending-up'}]}}" 
            wx:key="key"
            theme="{{filterType === item.key ? 'primary' : 'default'}}"
            variant="{{filterType === item.key ? 'dark' : 'outline'}}"
            shape=""
            bind:click="onFilterTypeChange"
            data-type="{{item.key}}"
            class="filter-tag"
          >
            <t-icon name="{{item.icon}}" size="24rpx" slot="icon" />
            {{item.label}}
          </t-tag>
        </view>
        
        <!-- 排序选项 -->
        <view class="sort-container">
          <view class="sort-options">
            <text class="sort-label">排序：</text>
            <t-tag 
              wx:for="{{[{key: 'time', label: '时间', icon: 'time'}, {key: 'frequency', label: '频次', icon: 'chart-bar'}, {key: 'difficulty', label: '难度', icon: 'star'}, {key: 'alphabet', label: '字母', icon: 'sort-ascending'}]}}" 
              wx:key="key"
              theme="{{sortType === item.key ? 'success' : 'default'}}"
              variant="{{sortType === item.key ? 'dark' : 'outline'}}"
              shape=""
              bind:click="onSortTypeChange"
              data-sort="{{item.key}}"
              class="sort-tag"
            >
              <t-icon name="{{item.icon}}" size="20rpx" slot="icon" />
              {{item.label}}
              <t-icon 
                wx:if="{{sortType === item.key}}" 
                name="{{sortOrder === 'asc' ? 'arrow-up' : 'arrow-down'}}" 
                size="24rpx" 
                slot="suffix"
              />
            </t-tag>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作栏 -->
    <view class="action-bar">
      <view class="action-card">
        <view class="action-left">

          <view class="select-info">
            <t-tag theme="primary" variant="light" shape="round" class="select-count-tag">
              已选择 {{selectedWords.length}} 个
            </t-tag>
          </view>

          <view  class="batch-actions">
            <t-button 
              size="extra-small" 
              theme="light" 
              shape=""
              bind:tap="showStudyModeSelect"
              disabled="{{wrongWords.length === 0}}"
              class="study-btn"
              icon='play-circle'
            >
              随机复习
            </t-button>
            <t-button 
              size="extra-small" 
              theme="light" 
              shape=""
              bind:tap="batchReview"
              disabled="{{selectedWords.length === 0}}"
              class="batch-btn"
              icon="play-circle"
            >
              复习
            </t-button>
            <t-button 
              size="extra-small" 
              theme="light" 
              shape=""
              bind:tap="batchMarkAsMastered"
              disabled="{{selectedWords.length === 0}}"
              class="batch-btn"
              icon="check-circle"
            >
              掌握
            </t-button>
            <t-button 
              size="extra-small" 
              theme="light" 
              shape=""
              bind:tap="batchDelete"
              disabled="{{selectedWords.length === 0}}"
              class="batch-btn"
              icon="delete"
            >
              删除
            </t-button>
          </view>
        </view>
        
      </view>
    </view>

    <!-- 错词列表 -->
    <view wx:if="{{wrongWords.length > 0}}" class="word-list-section">
      <view class="word-list">
        <view 
          wx:for="{{wrongWords}}" 
          wx:key="id" 
          class="word-card {{ isWordSelected(item) ? 'selected' : ''}}"
          data-word="{{item}}"
        >
          <!-- 选择框 -->
          <view class="word-checkbox">
            <t-checkbox 
              value="{{isWordSelected(item)}}"
              color="#0052d9"
              size="small"
              class="checkbox"
              data-word="{{item}}"
              bind:change="onWordSelect"
            />
          </view>
          
          <!-- 单词内容 -->
          <view class="word-content">
            <view class="word-header">
              <view class="word-main">
                <view class="word-title-row">
                  <text class="word-text">{{item.wordName}}</text>
                  <view class="difficulty-badge">
                    <t-tag 
                      theme="{{item.difficulty <= 2 ? 'success' : item.difficulty <= 4 ? 'warning' : 'danger'}}"
                      variant="light"
                      size="small"
                      shape="round"
                    >
                      {{getDifficultyText(item.difficulty)}}
                    </t-tag>
                  </view>
                </view>
                <text class="word-phonetic">{{item.phonetic}}</text>
              </view>
              <view class="word-actions">
                <t-button 
                  size="small"
                  theme="primary"
                  variant="outline"
                  shape="circle"
                  bind:tap="playWordPronunciation" 
                  data-word="{{item}}"
                  class="action-btn play-btn"
                >
                  <t-icon name="sound" size="32rpx" />
                </t-button>
                <t-button 
                  size="small"
                  theme="success"
                  variant="outline"
                  shape="circle"
                  bind:tap="markAsMastered" 
                  data-word="{{item}}"
                  class="action-btn master-btn"
                >
                  <t-icon name="check" size="32rpx" />
                </t-button>
                 <t-button 
                  size="small"
                  theme="success"
                  variant="outline"
                  shape="circle"
                  bind:tap="onWordTap" 
                  data-word="{{item}}"
                  class="action-btn master-btn"
                >
                  <t-icon name="file" size="32rpx" />
                </t-button>
              </view>
            </view>
            
            <view class="word-translation">
              <text class="translation-text">{{item.translation}}</text>
            </view>
            
            <!-- 单词溯源信息 -->
            <view wx:if="{{item.textbookId}}" class="word-source">
              <t-tag 
                theme="default" 
                variant="outline" 
                size="small" 
                shape="round"
                class="source-tag"
              >
                <t-icon name="book" size="20rpx" slot="icon" />
                {{item.textbookName}}
              </t-tag>
              <t-tag 
                wx:if="{{item.unitName}}"
                theme="default" 
                variant="outline" 
                size="small" 
                shape="round"
                class="source-tag"
              >
                <t-icon name="layers" size="20rpx" slot="icon" />
                {{item.unitName}}
              </t-tag>
              <t-tag 
                wx:if="{{item.chapterName}}"
                theme="default" 
                variant="outline" 
                size="small" 
                shape="round"
                class="source-tag"
              >
                <t-icon name="file-text" size="20rpx" slot="icon" />
                {{item.chapterName}}
              </t-tag>
            </view>
            
            <view class="word-meta">
              <view class="meta-row">
                <t-tag 
                  theme="danger" 
                  variant="light" 
                  size="small" 
                  shape="round"
                  class="meta-tag"
                >
                  <t-icon name="close-circle" size="20rpx" slot="icon" />
                  错误{{item.errorCount}}次
                </t-tag>
                
                <t-tag 
                  theme="primary" 
                  variant="light" 
                  size="small" 
                  shape="round"
                  class="meta-tag"
                >
                  <t-icon name="edit" size="20rpx" slot="icon" />
                  听写{{item.dictationCount || 0}}次
                </t-tag>
                
                <t-tag 
                  theme="default" 
                  variant="light" 
                  size="small" 
                  shape="round"
                  class="meta-tag"
                >
                  <t-icon name="time" size="20rpx" slot="icon" />
                  {{item.lastDictationTime}}
                </t-tag>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 加载更多 -->
      <view wx:if="{{pagination.hasMore}}" class="load-more">
        <view wx:if="{{loading}}" class="loading-more">
          <t-loading theme="circular" size="40rpx" text="加载中..." />
        </view>
        <view wx:else class="load-more-hint">
          <t-icon name="arrow-up" size="32rpx" color="rgba(255,255,255,0.6)" />
          <text class="load-more-text">上拉加载更多</text>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view wx:if="{{wrongWords.length === 0 && !loading}}" class="empty-state">
      <view class="empty-card">
        <view class="empty-content">
          <t-icon name="check-circle" size="80rpx" color="#66bb6a" />
          <text class="empty-title">太棒了！暂无错词</text>
          <text class="empty-subtitle">继续保持，词汇掌握得很好</text>
          <view class="empty-actions">
            <t-button 
              theme="primary" 
              size="small" 
              shape=""
              bind:tap="goToWordStudy"
              class="empty-btn"
            >
              <t-icon name="play-circle" size="32rpx" slot="icon" />
              继续学习
            </t-button>
            <t-button 
              theme="default" 
              variant="outline"
              size="small" 
              shape=""
              bind:tap="goToWordLibrary"
              class="empty-btn"
            >
              <t-icon name="book" size="32rpx" slot="icon" />
              词库浏览
            </t-button>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 单词详情弹窗 -->
  <view wx:if="{{showWordDetail}}" class="word-detail-modal" bind:tap="hideWordDetail">
    <view class="word-detail-content" catch:tap="true">
      <view class="detail-header">
        <text class="detail-title">单词详情</text>
        <t-icon name="close" size="32rpx" color="#999" bind:tap="hideWordDetail" />
      </view>
      
      <view wx:if="{{currentWord}}" class="detail-body">
        <view class="detail-word">
          <text class="detail-word-text">{{currentWord.wordName}}</text>
          <view class="detail-play-btn" bind:tap="playWordPronunciation" data-word="{{currentWord}}">
            <t-icon name="sound" size="32rpx" color="#0052d9" />
          </view>
        </view>
        
        <view class="detail-phonetic">
          <text class="phonetic-text">{{currentWord.phonetic}}</text>
        </view>
        
        <view class="detail-translation">
          <text class="translation-text">{{currentWord.translation}}</text>
        </view>
        
        <view wx:if="{{currentWord.exampleSentence}}" class="detail-example">
          <text class="example-label">例句：</text>
          <text class="example-text">{{currentWord.exampleSentence}}</text>
        </view>
        
        <view class="detail-stats">
          <view class="stat-row">
            <text class="stat-label">错误次数：</text>
            <text class="stat-value" style="color: {{ frequencyColor }}">{{currentWord.errorCount}}次</text>
          </view>
          <view class="stat-row">
            <text class="stat-label">难度等级：</text>
            <text class="stat-value">{{difficultyString}}</text>
          </view>
          <view class="stat-row">
            <text class="stat-label">最近错误：</text>
            <text class="stat-value">{{ currentWord.lastDictationTime }}</text>
          </view>
        </view>
        
        <view class="detail-actions">
          <t-button theme="primary" size="large" bind:tap="selectStudyMode" data-words="{{[currentWord]}}" disabled="{{true}}">
            随机复习
          </t-button>
          <t-button theme="default" size="large" bind:tap="markAsMastered" data-word="{{currentWord}}">
            标记掌握
          </t-button>
        </view>
      </view>
    </view>
  </view>

  <!-- 学习模式选择弹窗 -->
  <view wx:if="{{showStudyModal}}" class="study-modal" bind:tap="hideStudyModeSelect">
    <view class="study-modal-content" catch:tap="true">
      <view class="modal-header">
        <text class="modal-title">选择学习模式</text>
        <t-icon name="close" size="32rpx" color="#999" bind:tap="hideStudyModeSelect" />
      </view>
      
      <view class="study-modes">
        <view 
          class="study-mode-item" 
          bind:tap="selectStudyMode" 
          data-mode="recognition"
          hidden="{{true}}"
        >
          <view class="mode-icon">
            <t-icon name="visibility" size="48rpx" color="#0052d9" />
          </view>
          <view class="mode-content">
            <text class="mode-title">认识模式</text>
            <text class="mode-subtitle">看单词选择中文意思</text>
          </view>
        </view>
        
        <view 
          class="study-mode-item" 
          bind:tap="selectStudyMode" 
          data-mode="spelling"
        >
          <view class="mode-icon">
            <t-icon name="edit" size="48rpx" color="#00a870" />
          </view>
          <view class="mode-content">
            <text class="mode-title">拼写模式</text>
            <text class="mode-subtitle">听发音拼写单词</text>
          </view>
        </view>
        
        <view 
          class="study-mode-item" 
          bind:tap="selectStudyMode" 
          data-mode="listening"
          hidden="{{true}}"
        >
          <view class="mode-icon">
            <t-icon name="sound" size="48rpx" color="#7c4dff" />
          </view>
          <view class="mode-content">
            <text class="mode-title">听力模式</text>
            <text class="mode-subtitle">听发音写中文意思</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 学生选择器弹窗 -->
  <t-popup 
    visible="{{showStudentSelector}}" 
    placement="bottom" 
    bind:visible-change="onStudentSelectorVisibleChange"
  >
    <view class="student-selector">
      <view class="selector-header">
        <text class="selector-title">选择学生</text>
        <t-icon name="close" size="32rpx" color="#999" bind:tap="hideStudentSelect" />
      </view>
      
      <view wx:if="{{studentList.length > 0}}" class="student-list">
        <view 
          wx:for="{{studentList}}" 
          wx:key="id"
          class="student-option {{currentStudent && currentStudent.id === item.id ? 'selected' : ''}}"
          bind:tap="selectStudent"
          data-student="{{item}}"
        >
          <view class="student-info">
            <view class="student-avatar">
              <t-avatar size="60rpx" image="{{ apiFileHostSrc + item.avatar || '/pages/image/default-avatar.png'}}" />
            </view>
            <view class="student-details">
              <text class="student-name">{{item.studentName}}</text>
              <text class="student-grade">{{item.grade}}</text>
            </view>
          </view>
          <t-icon 
            wx:if="{{currentStudent && currentStudent.id === item.id}}"
            name="check" 
            size="32rpx" 
            color="#0052d9" 
          />
        </view>
      </view>
      
      <view wx:else class="empty-student">
        <t-icon name="user-add" size="80rpx" color="#ddd" />
        <text class="empty-text">暂无学生信息</text>
        <t-button theme="primary" size="large" bind:tap="goToAddStudent">
          添加学生
        </t-button>
      </view>
    </view>
  </t-popup>

  <!-- 高级筛选弹窗 -->
  <t-popup 
    visible="{{showFilterModal}}" 
    placement="bottom" 
    bind:visible-change="onFilterModalVisibleChange"
  >
    <view class="filter-modal">
      <view class="filter-header">
        <text class="filter-title">筛选条件</text>
        <t-icon name="close" size="32rpx" color="#999" bind:tap="hideFilterModal" />
      </view>
      
      <view class="filter-content">
        <!-- 字母筛选 -->
        <view class="filter-section">
          <text class="filter-section-title">按字母筛选</text>
          <view class="alphabet-filter">
            <view 
              wx:for="{{alphabetList}}" 
              wx:key="*this"
              class="alphabet-item {{filterConditions.alphabet === item ? 'active' : ''}}"
              bind:tap="selectAlphabet"
              data-letter="{{item}}"
            >
              <text class="alphabet-text">{{item}}</text>
            </view>
          </view>
        </view>
        
        <!-- 教材筛选 -->
        <view class="filter-section">
          <text class="filter-section-title">按教材筛选</text>
          <picker 
            value="{{filterConditions.textbookIndex}}" 
            range="{{textbookOptions}}"
            bind:change="onTextbookChange"
            range-key="label"
            placeholder="选择教材"
          >
            <t-input 
              slot="trigger"
              value="{{filterConditions.textbookName}}"
              placeholder="请选择教材（可选）"
              readonly
              suffix-icon="chevron-down"
            />
          </picker>
        </view>
        
        <!-- 单元筛选 -->
        <view wx:if="{{filterConditions.textbookId}}" class="filter-section">
          <text class="filter-section-title">按单元筛选</text>
          <picker 
            value="{{filterConditions.unitIdIndex}}" 
            range="{{unitOptions}}"
            bind:change="onUnitChange"
            range-key="label"
            placeholder="选择单元"
          >
            <t-input 
              slot="trigger"
              value="{{filterConditions.unitName}}"
              placeholder="请选择单元（可选）"
              readonly
              suffix-icon="chevron-down"
            />
          </picker>
        </view>
        
        <!-- 章节筛选 -->
        <view wx:if="{{filterConditions.unitId}}" class="filter-section">
          <text class="filter-section-title">按章节筛选</text>
          <picker 
            value="{{filterConditions.chapterIndex}}" 
            range="{{chapterOptions}}"
            bind:change="onChapterChange"
            range-key="label"
            placeholder="选择章节"
          >
            <t-input 
              slot="trigger"
              value="{{filterConditions.chapterName}}"
              placeholder="请选择章节（可选）"
              readonly
              suffix-icon="chevron-down"
            />
          </picker>
        </view>
        
        <!-- 错误次数筛选 -->
        <view class="filter-section">
          <text class="filter-section-title">错误次数</text>
          <view class="error-count-filter">
            <view 
              wx:for="{{[{key: '', label: '全部'}, {key: '1-3', label: '1-3次'}, {key: '4-6', label: '4-6次'}, {key: '7+', label: '7次以上'}]}}" 
              wx:key="key"
              class="error-count-item {{filterConditions.errorCountRange === item.key ? 'active' : ''}}"
              bind:tap="selectErrorCountRange"
              data-range="{{item.key}}"
            >
              <text class="error-count-text">{{item.label}}</text>
            </view>
          </view>
        </view>
      </view>
      
      <view class="filter-footer">
        <t-button theme="default" size="large" bind:tap="resetFilter">
          重置
        </t-button>
        <t-button theme="primary" size="large" bind:tap="applyFilter">
          确定
        </t-button>
      </view>
    </view>
  </t-popup>
</view>