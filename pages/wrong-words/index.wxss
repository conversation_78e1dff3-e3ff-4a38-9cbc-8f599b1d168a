/* 页面容器 */
.wrong-words-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 30%, #c3cfe2 100%);
  padding-bottom: 120rpx;
}

/* 顶部导航栏 */
.nav-header {
  padding: 40rpx 24rpx 24rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.nav-content {
  text-align: center;
}

.nav-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  margin-bottom: 8rpx;
}

.title-text {
  font-size: 44rpx;
  font-weight: bold;
  color: #fff;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.nav-subtitle {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 400;
}

/* 学生信息区域 */
.student-section {
  margin: 0rpx 16rpx 16rpx;
  position: relative;
  z-index: 10;
}

.student-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.student-info {
  display: flex;
  align-items: center;
  gap: 20rpx;
  flex: 1;
}

.student-avatar {
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 82, 217, 0.2);
}

.student-avatar.empty {
  background: linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.student-details {
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.student-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.2;
}

.student-name.empty {
  color: #999;
}

.student-grade {
  font-size: 24rpx;
  color: #666;
  font-weight: 400;
}

.switch-btn {
  flex-shrink: 0;
  margin-left: 16rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-spinner.small {
  width: 40rpx;
  height: 40rpx;
  border-width: 3rpx;
}

.loading-text {
  color: #fff;
  font-size: 28rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 主要内容 */
.content {
  padding: 0rpx;
}

/* 统计卡片区域 */
.statistics-section {
  margin: 0 16rpx 16rpx;
  position: relative;
}

.stats-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.stat-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 18rpx;
  padding: 20rpx 16rpx;
  box-shadow: 0 6rpx 24rpx rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  gap: 16rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

/* 卡片顶部装饰条 */
.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, var(--card-color, #0052d9) 0%, var(--card-color-light, #42a5f5) 100%);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 卡片背景渐变效果 */
.stat-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(var(--card-color-rgb, 0, 82, 217), 0.02) 0%, 
    rgba(var(--card-color-rgb, 0, 82, 217), 0.06) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

/* 卡片主题色彩定义 */
.stat-card.total {
  --card-color: #ff6b6b;
  --card-color-light: #ff8a80;
  --card-color-rgb: 255, 107, 107;
}

.stat-card.recent {
  --card-color: #ffa726;
  --card-color-light: #ffb74d;
  --card-color-rgb: 255, 167, 38;
}

.stat-card.reviewing {
  --card-color: #42a5f5;
  --card-color-light: #64b5f6;
  --card-color-rgb: 66, 165, 245;
}

.stat-card.mastered {
  --card-color: #66bb6a;
  --card-color-light: #81c784;
  --card-color-rgb: 102, 187, 106;
}

/* 卡片悬浮和点击效果 */
/* .stat-card:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 12rpx 36rpx rgba(0, 0, 0, 0.1);
}

.stat-card:hover::before {
  transform: scaleX(1);
}

.stat-card:hover::after {
  opacity: 1;
}

.stat-card:active {
  transform: translateY(-1rpx) scale(0.98);
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.12);
} */

/* 图标容器 */
.stat-icon {
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, 
    rgba(var(--card-color-rgb, 0, 82, 217), 0.1) 0%, 
    rgba(var(--card-color-rgb, 0, 82, 217), 0.2) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  position: relative;
  transition: all 0.3s ease;
  box-shadow: 0 6rpx 20rpx rgba(var(--card-color-rgb, 0, 82, 217), 0.15);
}

.stat-icon::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: linear-gradient(135deg, 
    rgba(var(--card-color-rgb, 0, 82, 217), 0.2) 0%, 
    rgba(var(--card-color-rgb, 0, 82, 217), 0.4) 100%);
  transform: translate(-50%, -50%) scale(0);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: -1;
}

.stat-card:hover .stat-icon::before {
  transform: translate(-50%, -50%) scale(1);
}

/* 内容区域 */
.stat-content {
  flex: 1;
  display: flex;
  flex-direction: row;
  gap: 20rpx;
}

.stat-number {
  font-size: 44rpx;
  font-weight: 700;
  color: #333;
  line-height: 1;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #333 0%, #555 100%);
  background-clip: text;
  -webkit-background-clip: text;
}

.stat-card:hover .stat-number {
  background: linear-gradient(135deg, 
    var(--card-color) 0%, 
    var(--card-color-light) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
  transition: color 0.3s ease;
  display: flex;
  align-items: center;
}

.stat-card:hover .stat-label {
  color: #333;
}

/* 数字动画效果 */
@keyframes numberPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.stat-card:active .stat-number {
  animation: numberPulse 0.3s ease;
}

/* 卡片入场动画 */
@keyframes cardSlideIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stat-card {
  animation: cardSlideIn 0.5s ease-out;
}

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }

/* 搜索筛选区域 */
.search-filter-section {
  margin: 0 16rpx 16rpx;
}

.search-filter-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 24rpx;
  box-shadow: 0 6rpx 24rpx rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.search-container {
  margin-bottom: 16rpx;
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.search-input-wrapper {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 50rpx;
  padding: 0 50rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  flex: 1;
}

.search-icon {
  flex-shrink: 0;
}

.search-input {
  flex: 1;
  height: 72rpx;
  font-size: 26rpx;
  color: #333;
}

.search-clear {
  flex-shrink: 0;
  cursor: pointer;
}

.filter-btn {
  background: rgba(255, 255, 255, 0.95) !important;
  border-radius: 50rpx !important;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  padding: 0 24rpx !important;
  height: 72rpx !important;
  display: flex !important;
  align-items: center !important;
  gap: 8rpx !important;
}

.filter-section {
  margin-bottom: 24rpx;
}

.filter-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.filter-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

/* 筛选标签 */
.filter-tabs {
  display: flex;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.filter-tab {
  flex: 1;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 40rpx;
  padding: 16rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.filter-tab.active {
  background: #0052d9;
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 20rpx rgba(0, 82, 217, 0.3);
}

.filter-tab-text {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

.filter-tab.active .filter-tab-text {
  color: #fff;
  font-weight: bold;
}

.filter-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.quick-filter{
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 8rpx;
}

.sort-label{
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}

.sort-section {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 20rpx;
}

.sort-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.sort-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

/* 排序选项 */
.sort-options {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.sort-option {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 30rpx;
  padding: 12rpx 20rpx;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.sort-option.active {
  background: #00a870;
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 16rpx rgba(0, 168, 112, 0.3);
}

.sort-option-text {
  font-size: 22rpx;
  color: #666;
}

.sort-option.active .sort-option-text {
  color: #fff;
  font-weight: bold;
}

.sort-arrow {
  opacity: 0.7;
}

/* 操作栏 */
.action-bar {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 20rpx 24rpx;
  margin: 0 16rpx 16rpx;
  box-shadow: 0 6rpx 24rpx rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.batch-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex: 1;
}

.selected-info {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.selected-count {
  font-size: 26rpx;
  color: #333;
  font-weight: 600;
}

.selected-text {
  font-size: 24rpx;
  color: #666;
}

.batch-actions {
  display: flex;
  gap: 12rpx;
  flex-shrink: 0;
}

.action-left {
  display: flex;
  flex-flow: column;
  align-items: center;
  justify-content: space-between;
  gap: 16rpx;
}

.select-info {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.select-count {
  font-size: 22rpx;
  color: #666;
}

.select-all {
  font-size: 22rpx;
  color: #0052d9;
  padding: 8rpx;
}

.action-right {
  display: flex;
  gap: 12rpx;
}

/* 错词列表 */
.word-list-section {
  margin-bottom: 24rpx;
}

.checkbox{
  --td-checkbox-vertical-padding: 0;
  --td-checkbox-icon-size: 36rpx;
  --td-checkbox-icon-color: #0052d9;
  --td-checkbox-icon-checked-color: #0052d9;
}

.word-list{
  margin: 16rpx;
}

.words-list {
  margin: 0 24rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.word-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 18rpx;
  padding: 24rpx;
  box-shadow: 0 6rpx 24rpx rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  margin-bottom: 4rpx;
}

.word-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3rpx;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.word-card.selected {
  border-color: #0052d9;
  box-shadow: 0 8rpx 32rpx rgba(0, 82, 217, 0.12);
  transform: translateY(-1rpx);
}

.word-card.selected::before {
  opacity: 1;
  background: linear-gradient(90deg, #0052d9 0%, #42a5f5 100%);
}

.word-checkbox {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  z-index: 2;
}

.word-content {
  margin-left: 0;
  padding-left: 0;
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
  flex-direction: column;
  margin-top: 25px;
}

.word-card.selected .word-content {
  margin-left: 56rpx;
}

.word-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12rpx;
  margin-bottom: 10rpx;
}

.word-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.word-info {
  display: flex;
  align-items: center;
  gap: 12rpx;
  flex: 1;
}

.word-text {
  font-size: 36rpx;
  font-weight: 700;
  color: #333;
  line-height: 1.2;
}

.word-phonetic {
  font-size: 26rpx;
  color: #666;
  font-style: italic;
  font-weight: 400;
}

.word-actions {
  display: flex;
  gap: 8rpx;
  flex-shrink: 0;
}

.action-btn {
  width: 56rpx;
  height: 56rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.play-btn {
  background: rgba(0, 82, 217, 0.1);
}

.play-btn:active {
  background: rgba(0, 82, 217, 0.2);
  transform: scale(0.95);
}

.master-btn {
  background: rgba(0, 168, 112, 0.1);
}

.master-btn:active {
  background: rgba(0, 168, 112, 0.2);
  transform: scale(0.95);
}

.word-translation {
  margin-bottom: 10rpx;
}

.translation-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  font-weight: 500;
}

/* 单词溯源信息 */
.word-source {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  /* margin-bottom: 16rpx; */
  padding: 16rpx;
  background: rgba(0, 82, 217, 0.04);
  border-radius: 12rpx;
  border-left: 3rpx solid #0052d9;
}

.source-item {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 6rpx 12rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  border: 1rpx solid rgba(0, 82, 217, 0.1);
}

.source-text {
  font-size: 22rpx;
  color: #666;
  font-weight: 500;
}

.word-meta {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.meta-text {
  font-size: 22rpx;
}

/* 加载更多 */
.load-more {
  text-align: center;
  padding: 32rpx 24rpx;
  margin: 0 24rpx;
}

.load-more-content {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 18rpx;
  padding: 24rpx;
  box-shadow: 0 6rpx 24rpx rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
}

.loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12rpx;
}

.load-more-text {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 32rpx 0rpx;
  margin: 16rpx;
}

.empty-content {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 48rpx 32rpx;
  box-shadow: 0 6rpx 24rpx rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
}

.empty-icon {
  width: 72rpx;
  height: 72rpx;
  margin: 0 auto 24rpx;
  background: linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 72rpx;
  color: #ccc;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #666;
  margin-bottom: 12rpx;
}

.empty-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #666;
  margin-bottom: 12rpx;
}

.empty-subtitle {
  font-size: 26rpx;
  color: #999;
  line-height: 1.6;
  margin-bottom: 24rpx;
}

.empty-actions {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
}

.empty-hint {
  font-size: 26rpx;
  color: #999;
  line-height: 1.6;
  margin-bottom: 24rpx;
}

.empty-action {
  margin-top: 20rpx;
}

/* 单词详情弹窗 */
.word-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 32rpx;
}

.word-detail-content {
  background: #fff;
  border-radius: 20rpx;
  width: 100%;
  max-width: 560rpx;
  max-height: 80vh;
  overflow-y: auto;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 2rpx solid #f5f5f5;
  position: relative;
}

.detail-header::before {
  content: '';
  position: absolute;
  bottom: -2rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 2rpx;
}

.detail-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.detail-body {
  padding: 24rpx;
}

.detail-word {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.detail-word-text {
  font-size: 48rpx;
  font-weight: 700;
  color: #333;
  line-height: 1.2;
}

.detail-play-btn {
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
  background: rgba(0, 82, 217, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.detail-phonetic {
  margin-bottom: 20rpx;
}

.phonetic-text {
  font-size: 30rpx;
  color: #666;
  font-style: italic;
  font-weight: 400;
}

.detail-translation {
  margin-bottom: 24rpx;
}

.translation-text {
  font-size: 28rpx;
  color: #555;
  line-height: 1.7;
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 16rpx;
  border-left: 4rpx solid #0052d9;
}

.detail-example {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 24rpx;
  border: 1rpx solid #e9ecef;
}

.example-label {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
  font-weight: 500;
}

.example-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.7;
}

.detail-stats {
  margin-bottom: 32rpx;
  gap: 16rpx;
}

.detail-stat-item {
  text-align: center;
  padding: 20rpx 16rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 16rpx;
  border: 1rpx solid #e9ecef;
  position: relative;
  overflow: hidden;
}

.detail-stat-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3rpx;
  background: linear-gradient(90deg, #0052d9 0%, #42a5f5 100%);
}

.detail-stat-value {
  font-size: 36rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 6rpx;
  line-height: 1;
}

.detail-stat-label {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.stat-row:last-child {
  border-bottom: none;
}

.stat-label {
  font-size: 26rpx;
  color: #666;
}

.stat-value {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
}

.detail-actions {
  display: flex;
  gap: 16rpx;
  padding-top: 20rpx;
  border-top: 2rpx solid #f5f5f5;
}

/* 学习模式选择弹窗 */
.study-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 32rpx;
}

.study-modal-content {
  background: #fff;
  border-radius: 20rpx;
  width: 100%;
  max-width: 560rpx;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.modal-header::after {
  content: '';
  position: absolute;
  bottom: -1rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 2rpx;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.study-modes {
  padding: 24rpx;
}

.study-mode-item {
  display: flex;
  align-items: center;
  gap: 24rpx;
  padding: 24rpx;
  border-radius: 18rpx;
  margin-bottom: 20rpx;
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  position: relative;
  overflow: hidden;
}

.study-mode-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3rpx;
  background: linear-gradient(90deg, #0052d9 0%, #42a5f5 100%);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.study-mode-item:last-child {
  margin-bottom: 0;
}

.study-mode-item:active {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  border-color: #0052d9;
  transform: translateY(-1rpx);
  box-shadow: 0 6rpx 20rpx rgba(0, 82, 217, 0.2);
}

.study-mode-item:active::before {
  transform: scaleX(1);
}

.mode-icon {
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #0052d9 0%, #42a5f5 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-shadow: 0 4rpx 16rpx rgba(0, 82, 217, 0.3);
}

.mode-content {
  flex: 1;
}

.mode-title {
  display: block;
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.2;
}

.mode-subtitle {
  display: block;
  font-size: 26rpx;
  color: #666;
  font-weight: 400;
  line-height: 1.5;
}

/* 学习模式弹窗动画 */
@keyframes modalSlideUp {
  from {
    opacity: 0;
    transform: translateY(80rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.study-modal-content {
  animation: modalSlideUp 0.3s ease-out;
}

/* ==================== 学生选择器弹窗样式 ==================== */
.student-selector {
  background: #fff;
  border-radius: 18rpx 18rpx 0 0;
  max-height: 75vh;
  overflow: hidden;
}

.selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.selector-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.student-list {
  max-height: 55vh;
  overflow-y: auto;
  padding: 16rpx 0;
}

.student-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx;
  transition: all 0.3s ease;
}

.student-option:active {
  background: #f8f9fa;
}

.student-option.selected {
  background: rgba(0, 82, 217, 0.05);
}

.empty-student {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 64rpx 24rpx;
  gap: 24rpx;
}

.empty-text {
  font-size: 26rpx;
  color: #999;
}

/* ==================== 高级筛选弹窗样式 ==================== */
.filter-modal {
  background: #fff;
  border-radius: 18rpx 18rpx 0 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  flex-shrink: 0;
}

.filter-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.filter-content {
  flex: 1;
  overflow-y: auto;
  padding: 24rpx;
}

.filter-section {
  margin-bottom: 32rpx;
}

.filter-section:last-child {
  margin-bottom: 0;
}

.filter-section-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

/* 字母筛选 */
.alphabet-filter {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 12rpx;
}

.alphabet-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 72rpx;
  height: 72rpx;
  border-radius: 12rpx;
  background: #f8f9fa;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.alphabet-item.active {
  background: #0052d9;
  border-color: #0052d9;
}

.alphabet-text {
  font-size: 26rpx;
  font-weight: 600;
  color: #666;
}

.alphabet-item.active .alphabet-text {
  color: #fff;
}

/* 错误次数筛选 */
.error-count-filter {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.error-count-item {
  padding: 12rpx 20rpx;
  border-radius: 50rpx;
  background: #f8f9fa;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.error-count-item.active {
  background: #0052d9;
  border-color: #0052d9;
}

.error-count-text {
  font-size: 24rpx;
  font-weight: 500;
  color: #666;
}

.error-count-item.active .error-count-text {
  color: #fff;
}

.filter-footer {
  display: flex;
  gap: 16rpx;
  padding: 24rpx;
  border-top: 1rpx solid #f0f0f0;
  flex-shrink: 0;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .statistics-section {
    margin: 0 16rpx 16rpx;
  }
  
  .stats-container {
    gap: 12rpx;
  }
  
  .stat-card {
    padding: 16rpx 12rpx;
    border-radius: 16rpx;
    gap: 12rpx;
  }
  
  .stat-icon {
    width: 64rpx;
    height: 64rpx;
  }
  
  .stat-number {
    font-size: 36rpx;
  }
  
  .stat-label {
    font-size: 22rpx;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 24rpx;
  }
  
  .stat-item {
    padding: 16rpx;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12rpx;
  }
  
  .filter-tabs {
    flex-direction: column;
    gap: 12rpx;
  }
  
  .sort-options {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .action-bar {
    flex-direction: column;
    gap: 16rpx;
    align-items: stretch;
  }
  
  .action-left,
  .action-right {
    justify-content: center;
  }
  
  .word-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12rpx;
  }
  
  .word-actions {
    align-self: flex-end;
  }
  
  .detail-actions {
    flex-direction: column;
  }
}