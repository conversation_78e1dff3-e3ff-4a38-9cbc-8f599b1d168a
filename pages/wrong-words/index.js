const { formatTime } = require('../../utils/util');
const api = require('../../utils/api');
const apiFileHost = getApp().globalData.apiFileHost

Page({
  data: {
    apiFileHostSrc: apiFileHost,
    wrongWords: [],
    statistics: {
      total: 0,
      recent: 0,
      reviewing: 0,
      mastered: 0
    },
    
    // 学生信息
    currentStudent: null,
    userImageSrc: '',
    studentList: [],
    showStudentSelector: false,
    
    // 筛选和排序
    filterType: 'all', // all, recent, frequent
    sortType: 'time', // time, frequency, difficulty, alphabet
    sortOrder: 'desc', // asc, desc
    searchKeyword: '',
    
    // 高级筛选
    showFilterModal: false,
    filterConditions: {
      alphabet: '',
      textbookId: '',
      textbookName: '',
      unitId: '',
      unitName: '',
      chapterId: '',
      chapterName: '',
      errorCountRange: ''
    },
    alphabetList: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'],
    textbookOptions: [],
    unitOptions: [],
    chapterOptions: [],
    
    // 分页
    pagination: {
      current: 1,
      pageSize: 20,
      total: 0,
      hasMore: true
    },
    
    // 选择模式
    selectMode: false,
    selectedWords: [],
    
    // 学习模式
    studyMode: '',
    showStudyModal: false,
    
    // 显示状态
    loading: false,
    showWordDetail: false,
    currentWord: null
  },

  onLoad(options) {
    console.log('wrong-words onLoad', options);
    
    // 获取当前学生信息
    const currentStudent = wx.getStorageSync('currentStudent');
    if (currentStudent) {
      this.setData({ currentStudent,userImageSrc: apiFileHost + currentStudent.avatar });
    }
    
    // 加载学生列表
    this.loadStudentList();
    
    // 处理传入的错词数据
    if (options.data) {
      try {
        const wrongWordsData = JSON.parse(decodeURIComponent(options.data));
        this.setData({
          wrongWords: wrongWordsData
        });
        return;
      } catch (error) {
        console.error('解析错词数据失败:', error);
      }
    }
    
    // 检查学生信息
    if (!currentStudent) {
      wx.showModal({
        title: '提示',
        content: '请先选择学生信息',
        showCancel: false,
        success: () => {
          wx.switchTab({
            url: '/pages/word-index/index'
          });
        }
      });
      return;
    }
    
    this.initPage();
  },

  onShow() {
    console.log('wrong-words onShow');
    
    // 检查学生信息是否变化
    const currentStudent = wx.getStorageSync('currentStudent');
    if (currentStudent && (!this.data.currentStudent || this.data.currentStudent.id !== currentStudent.id)) {
      this.setData({ currentStudent });
      this.initPage();
    } else if (!currentStudent) {
      this.setData({ currentStudent: null });
    }
    
    // 如果有学生信息但没有错词数据，重新加载
    if (currentStudent && this.data.wrongWords.length === 0) {
      this.loadWrongWords(true);
    }
  },

  // 初始化页面
  async initPage() {
    try {
      await Promise.all([
        this.loadStatistics(),
        this.loadWrongWords(true)
      ]);
    } catch (error) {
      console.error('初始化页面失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    }
  },

  // 加载统计信息
  async loadStatistics() {
    const { currentStudent } = this.data;
    if (!currentStudent) {
      return;
    }
    
    try {
      const userInfo = wx.getStorageSync('loginUser');
      if (!userInfo || !userInfo.userId) {
        console.error('用户信息不存在');
        return;
      }
      
      const result = await api.getWrongWordStats({
        accountId: userInfo.userId,
        studentId: currentStudent.id
      });
      
      if (result.success) {
        const stats = result.result || {};
        this.setData({
          statistics: {
            total: stats.totalCount || 0,
            recent: stats.weekCount || 0,
            mastered: stats.masteredCount || 0,
            reviewing: stats.totalCount || 0
          }
        });
      }
    } catch (error) {
      console.error('加载统计信息失败:', error);
    }
  },

  // 加载错词列表
  async loadWrongWords(reset = false) {
    const { pagination, filterType, sortType, sortOrder, searchKeyword, currentStudent, filterConditions } = this.data;
    
    if (!currentStudent) {
      console.log('没有选择学生，跳过加载错词列表');
      return;
    }
    
    if (reset) {
      this.setData({
        'pagination.current': 1,
        'pagination.hasMore': true,
        wrongWords: []
      });
    }
    
    if (!this.data.pagination.hasMore && !reset) {
      return;
    }
    
    try {
      this.setData({ loading: true });
      
      const userInfo = wx.getStorageSync('loginUser');
      if (!userInfo || !userInfo.userId) {
        console.error('用户信息不存在');
        return;
      }
      
      const params = {
        accountId: userInfo.userId,
        studentId: currentStudent.id,
        pageNo: reset ? 1 : pagination.current,
        pageSize: pagination.pageSize,
        sortType: sortType,
        sortOrder: sortOrder,
        keyword: searchKeyword || undefined,
        letter: filterConditions.alphabet || undefined,
        textbookId: filterConditions.textbookId || undefined,
        unitId: filterConditions.unitId || undefined,
        chapterId: filterConditions.chapterId || undefined,
        errorCountRange: filterConditions.errorCountRange || undefined
      };
      
      const result = await api.getWrongWordList(params);
      
      if (result.success) {
        const responseData = result.result || {};
        const newWrongWords = responseData.records || [];
        const currentList = reset ? [] : this.data.wrongWords;
        
        this.setData({
          wrongWords: [...currentList, ...newWrongWords],
          'pagination.current': (reset ? 1 : pagination.current) + 1,
          'pagination.total': responseData.total || 0,
          'pagination.hasMore': responseData.hasNext || false
        });
      } else {
        console.error('加载错词列表失败:', result);
      }
    } catch (error) {
      console.error('加载错词列表失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    } finally {
      this.setData({ loading: false, refreshing: false });
    }
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.setData({ refreshing: true });
    Promise.all([
      this.loadStatistics(),
      this.loadWrongWords(true)
    ]).finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 上拉加载更多
  onReachBottom() {
    this.loadWrongWords();
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  // 搜索提交
  onSearchSubmit() {
    this.loadWrongWords(true);
  },

  // 清空搜索
  onSearchClear() {
    this.setData({
      searchKeyword: ''
    });
    this.loadWrongWords(true);
  },

  // 筛选类型改变
  onFilterTypeChange(e) {
    const { type } = e.currentTarget.dataset;
    
    this.setData({
      filterType: type
    });
    
    this.loadWrongWords(true);
  },

  // 统计卡片点击事件
  onStatCardTap(e) {
    const { type } = e.currentTarget.dataset;
    
    // 根据统计类型设置筛选条件
    let filterType = 'all';
    let sortType = 'time';
    
    switch (type) {
      case 'total':
        filterType = 'all';
        sortType = 'time';
        break;
      case 'recent':
        filterType = 'recent';
        sortType = 'time';
        break;
      case 'reviewing':
        filterType = 'reviewing';
        sortType = 'frequency';
        break;
      case 'mastered':
        filterType = 'mastered';
        sortType = 'time';
        break;
    }
    
    this.setData({
      filterType: filterType,
      sortType: sortType,
      sortOrder: 'desc'
    });
    
    // 重新加载数据
    this.loadWrongWords(true);
    
    // 显示提示
    const typeNames = {
      total: '全部错词',
      recent: '最近错误',
      reviewing: '复习中',
      mastered: '已掌握'
    };
    
    wx.showToast({
      title: `已切换到${typeNames[type]}`,
      icon: 'success',
      duration: 1500
    });
  },

  // 排序类型改变
  onSortTypeChange(e) {
    const { sort } = e.currentTarget.dataset;
    
    this.setData({
      sortType: sort
    });
    
    this.loadWrongWords(true);
  },

  // 单词项点击
  onWordTap(e) {
    const { word } = e.currentTarget.dataset;
    this.showWordDetail(word);
  },

  onWordSelect(e) {
    console.log('单词项checkbox点击:', JSON.stringify(e));
    const { word } = e.currentTarget.dataset;
    const isCheck = e.detail.checked;
    if (isCheck) {
      this.toggleSelectWord(word);
    }else {
      this.toggleSelectWord(word);
    }
    
  },

  // 显示单词详情
  showWordDetail(word) {
    this.setData({
      currentWord: word,
      difficultyString: this.getDifficultyText(word.difficulty),
      frequencyColor: this.getErrorCountColor(word.errorCount),
      showWordDetail: true
    });
  },

  // 隐藏单词详情
  hideWordDetail() {
    this.setData({
      showWordDetail: false,
      currentWord: null
    });
  },

  // 播放单词发音
  playWordPronunciation(e) {
    //console.log('播放单词发音', JSON.stringify(e));
    const { word } = e.currentTarget.dataset;
    
    if (!word.audioFile) {
      wx.showToast({
        title: '暂无发音',
        icon: 'none'
      });
      return;
    }
    
    // 使用微信内置的语音合成
    const audioContext = wx.createInnerAudioContext({
      useWebAudioImplement: true // 是否使用 WebAudio 作为底层音频驱动，默认关闭。对于短音频、播放频繁的音频建议开启此选项，开启后将获得更优的性能表现。由于开启此选项后也会带来一定的内存增长，因此对于长音频建议关闭此选项
    });
    audioContext.src = apiFileHost + word.audioFile;
    audioContext.play();
    
    audioContext.onError((error) => {
      console.error('播放发音失败:', error);
      wx.showToast({
        title: '播放失败',
        icon: 'error'
      });
    });
  },


  // 切换选择单词
  toggleSelectWord(word) {
    const { selectedWords } = this.data;
    const index = selectedWords.findIndex(w => w.id === word.id);
    
    if (index > -1) {
      selectedWords.splice(index, 1);
    } else {
      selectedWords.push(word);
    }
    
    this.setData({ selectedWords });
  },

  // 全选/取消全选
  toggleSelectAll() {
    const { wrongWords, selectedWords } = this.data;
    
    if (selectedWords.length === wrongWords.length) {
      // 取消全选
      this.setData({ selectedWords: [] });
    } else {
      // 全选
      this.setData({ selectedWords: [...wrongWords] });
    }
  },

  // 批量删除
  async batchDelete() {
    const { selectedWords } = this.data;
    
    if (selectedWords.length === 0) {
      wx.showToast({
        title: '请选择要删除的单词',
        icon: 'none'
      });
      return;
    }
    
    const result = await new Promise((resolve) => {
      wx.showModal({
        title: '确认删除',
        content: `确定要删除选中的 ${selectedWords.length} 个单词吗？`,
        success: resolve
      });
    });
    
    if (!result.confirm) return;
    
    try {
      wx.showLoading({ title: '删除中...' });
      
      const wrongWordIds = selectedWords.map(word => word.id);
      const deleteResult = await api.batchDeleteWrongWords({
        wrongWordIds: wrongWordIds
      });
      
      if (deleteResult.success) {
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        });
        
        this.setData({
          selectMode: false,
          selectedWords: []
        });
        
        this.loadWrongWords(true);
        this.loadStatistics();
      } else {
        wx.showToast({
          title: deleteResult.message || '删除失败',
          icon: 'error'
        });
      }
    } catch (error) {
      console.error('批量删除失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 批量复习
  batchReview() {
    const { selectedWords } = this.data;
    
    if (selectedWords.length === 0) {
      wx.showToast({
        title: '请选择要复习的单词',
        icon: 'none'
      });
      return;
    }
    
    this.startStudy(selectedWords);
  },

  // 批量标记为已掌握
  async batchMarkAsMastered() {
    const { selectedWords } = this.data;
    
    if (selectedWords.length === 0) {
      wx.showToast({
        title: '请选择要标记的单词',
        icon: 'none'
      });
      return;
    }
    
    const result = await new Promise((resolve) => {
      wx.showModal({
        title: '确认标记',
        content: `确定要将选中的 ${selectedWords.length} 个单词标记为已掌握吗？`,
        success: resolve
      });
    });
    
    if (!result.confirm) return;
    
    try {
      wx.showLoading({ title: '标记中...' });
      
      const wrongWordIds = selectedWords.map(word => word.id);
      const markResult = await api.batchMarkWrongWordsAsMastered({
        wrongWordIds: wrongWordIds
      });
      
      if (markResult.success) {
        wx.showToast({
          title: '标记成功',
          icon: 'success'
        });
        
        this.setData({
          selectMode: false,
          selectedWords: []
        });
        
        this.loadWrongWords(true);
        this.loadStatistics();
      } else {
        wx.showToast({
          title: markResult.message || '标记失败',
          icon: 'error'
        });
      }
    } catch (error) {
      console.error('批量标记失败:', error);
      wx.showToast({
        title: '标记失败',
        icon: 'error'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 显示学习模式选择
  showStudyModeSelect() {
    this.setData({
      showStudyModal: true
    });
  },

  // 隐藏学习模式选择
  hideStudyModeSelect() {
    this.setData({
      showStudyModal: false
    });
  },

  // 选择学习模式
  selectStudyMode(e) {
    const { mode } = e.currentTarget.dataset;
    
    this.setData({
      studyMode: mode,
      showStudyModal: false
    });

    // 需要随机 选择 5个单词进行学习
    const studyWords = this.data.wrongWords.sort(() => Math.random() - 0.5).slice(0, 5);
    console.log('随机选择学习单词', JSON.stringify(studyWords));
    
    this.startStudy(studyWords);
  },

  // 开始学习
  async startStudy(words = null) {
    const studyWords = words || this.data.wrongWords;
    console.log('开始学习', JSON.stringify(studyWords));
    
    if (studyWords.length === 0) {
      wx.showToast({
        title: '没有可学习的单词',
        icon: 'none'
      });
      return;
    }
    
    // 将单词数据传递给学习页面
    //const wordData = encodeURIComponent(JSON.stringify(studyWords));
    //console.log('开始学习--url转码--', wordData);


    // 需要根据当前选择的单词  创建task任务
    const taskId = await this.createTask(studyWords);
    if (taskId) {
      wx.navigateTo({
        url: `/pages/task-manage/dictation/index?taskId=${taskId}&mode=${this.data.studyMode}&type=review`
      });
    }

  },


  // 创建任务
  async createTask(studyWords) {
    console.log('创建任务', JSON.stringify(studyWords));
    
    if (studyWords.length === 0) {
      wx.showToast({ title: '请至少选择一个单词', icon: 'none' });
      return;
    }
    
    this.setData({ submitting: true });
    
    try {
      // 创建任务
      const taskData = {
        taskName: '随机复习'+studyWords.length+'个单词'+ new Date().toLocaleDateString(),
        taskType: 'dictation',
        studentId: this.data.currentStudent.id,
        textbookId: null,
        unitId: null,
        chapterId: null,
        description: '随机复习',
        wordCount: studyWords.length,
        status: '待做'
      };
      
      const taskRes = await api.createTask(taskData);
      if (!taskRes.success) {
        throw new Error(taskRes.message || '创建任务失败');
      }
      
      const taskId = taskRes.result.id;
      
      // 创建任务明细
      const taskDetails = studyWords.map((word, index) => ({
        taskId: taskId,
        studentId: this.data.currentStudent.id,
        wordId: word.wordId,
        wordName: word.wordName,
        wordMeaning: word.translation,
        sortOrder: index + 1
      }));
      
      const detailRes = await api.createTaskDetails({ taskDetails });
      if (!detailRes.success) {
        throw new Error(detailRes.message || '创建任务明细失败');
      }
      
      wx.showToast({ title: '创建成功', icon: 'success' });
      
      //setTimeout(() => {
      //  wx.navigateBack();
      //}, 1500);

      return taskId;
      
    } catch (error) {
      console.error('创建任务失败:', error);
      wx.showToast({ 
        title: error.message || '创建失败', 
        icon: 'none' 
      });
    } finally {
      this.setData({ submitting: false });
    }
  },



  // 标记为已掌握
  async markAsMastered(e) {
    const { word } = e.currentTarget.dataset;
    
    try {
      const result = await api.markWrongWordAsMastered({
        wrongWordId: word.id
      });
      
      if (result.success) {
        wx.showToast({
          title: '已标记为掌握',
          icon: 'success'
        });
        
        this.loadWrongWords(true);
        this.loadStatistics();
      } else {
        wx.showToast({
          title: result.message || '操作失败',
          icon: 'error'
        });
      }
    } catch (error) {
      console.error('标记掌握失败:', error);
      wx.showToast({
        title: '操作失败',
        icon: 'error'
      });
    }
  },

  // 获取错误次数颜色
  getErrorCountColor(count) {
    if (count >= 5) return '#e34d59';
    if (count >= 3) return '#ff9f1c';
    return '#0052d9';
  },

  // 获取难度文本
  getDifficultyText(difficulty) {
    const difficultyMap = {
      easy: '简单',
      medium: '中等',
      hard: '困难'
    };
    return difficultyMap[difficulty] || '中等';
  },

  // 格式化时间
  formatTime(timeStr) {
    if (!timeStr) return '';
    
    const time = new Date(timeStr);
    const now = new Date();
    const diff = now.getTime() - time.getTime();
    
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (minutes < 60) {
      return `${minutes}分钟前`;
    } else if (hours < 24) {
      return `${hours}小时前`;
    } else if (days < 7) {
      return `${days}天前`;
    } else {
      return formatTime(time);
    }
  },

  // 页面分享
  onShareAppMessage() {
    return {
      title: '单词指南针 - 错词本',
      path: '/pages/wrong-words/index'
    };
  },

  // 跳转到单词学习页面
  goToWordStudy() {
    /* wx.navigateTo({
      url: '/pages/word-study/index'
    }); */
    wx.switchTab({
      url: '/pages/task-manage/index'
    }); 
  },

  goToWordLibrary() {
    wx.switchTab({
      url: '/pages/word-library/index'
    });
  },

  // ==================== 学生切换相关方法 ====================
  
  // 加载学生列表
  async loadStudentList() {
    try {
      const result = await api.getStudentList();
      if (result.success) {
        const studentList = result.result || []
        this.setData({
          studentList: studentList
        });
      }

      // 如果没有当前学生，默认选择第一个
      if (!this.data.currentStudent && studentList.length > 0) {
        const firstStudent = studentList[0]
        this.setData({ currentStudent: firstStudent, userImageSrc: apiFileHost + firstStudent.avatar })
        // 保存到全局存储
        wx.setStorageSync('currentStudent', firstStudent)

        // 刷新数据
        //this.refreshData()
      } else if (studentList.length === 0) {
        // 没有学生时清空当前学生
        this.setData({ currentStudent: null })
        wx.removeStorageSync('currentStudent')
      }

    } catch (error) {
      console.error('加载学生列表失败:', error);
    }
  },

  // 显示学生选择器
  showStudentSelect() {
    this.setData({
      showStudentSelector: true
    });
  },

  // 隐藏学生选择器
  hideStudentSelect() {
    this.setData({
      showStudentSelector: false
    });
  },

  // 学生选择器显示状态变化
  onStudentSelectorVisibleChange(e) {
    this.setData({
      showStudentSelector: e.detail.visible
    });
  },

  // 选择学生
  selectStudent(e) {
    const { student } = e.currentTarget.dataset;
    
    // 更新当前学生
    this.setData({
      currentStudent: student,
      showStudentSelector: false
    });
    
    // 保存到全局存储
    wx.setStorageSync('currentStudent', student);
    
    // 重新加载数据
    this.initPage();
  },

  // 跳转到添加学生页面
  goToAddStudent() {
    wx.navigateTo({
      url: '/pages/student/add/index'
    });
  },

  // ==================== 高级筛选相关方法 ====================
  
  // 显示筛选弹窗
  showFilterModal() {
    this.setData({
      showFilterModal: true
    });
    this.loadTextbookOptions();
  },

  // 隐藏筛选弹窗
  hideFilterModal() {
    this.setData({
      showFilterModal: false
    });
  },

  // 筛选弹窗显示状态变化
  onFilterModalVisibleChange(e) {
    this.setData({
      showFilterModal: e.detail.visible
    });
  },

  // 选择字母
  selectAlphabet(e) {
    const { letter } = e.currentTarget.dataset;
    const currentLetter = this.data.filterConditions.alphabet;
    
    this.setData({
      'filterConditions.alphabet': currentLetter === letter ? '' : letter
    });
  },

  // 加载教材选项
  async loadTextbookOptions() {
    try {
      const { currentStudent } = this.data;
      const userInfo = wx.getStorageSync('loginUser');
      
      const result = await api.getWrongWordFilterOptions({
        accountId: userInfo?.userId,
        studentId: currentStudent?.id,
        type: 'textbook'
      });
      
      if (result.success) {
        const options = result.result.textbooks.map(item => ({
          label: item.name,
          value: item.id
        }));
        this.setData({
          textbookOptions: options
        });
      }
    } catch (error) {
      console.error('加载教材列表失败:', error);
    }
  },

  // 教材选择变化
  async onTextbookChange(e) {
    console.log('教材选择变化:', JSON.stringify(e));
    const textbookIndex = e.detail.value;
    const textbook = this.data.textbookOptions[textbookIndex];
    const textbookId = textbook.value;
    
    this.setData({
      'filterConditions.textbookIndex': textbookIndex,
      'filterConditions.textbookId': textbook.value,
      'filterConditions.textbookName': textbook ? textbook.label : '',
      'filterConditions.unitId': '',
      'filterConditions.unitName': '',
      'filterConditions.chapterId': '',
      'filterConditions.chapterName': '',
      unitOptions: [],
      chapterOptions: []
    });
    
    if (textbookId) {
      this.loadUnitOptions(textbookId);
    }
  },

  // 加载单元选项
  async loadUnitOptions(textbookId) {
    try {
      const { currentStudent } = this.data;
      const userInfo = wx.getStorageSync('loginUser');
      
      const result = await api.getWrongWordFilterOptions({
        accountId: userInfo?.userId,
        studentId: currentStudent?.id,
        type: 'unit',
        textbookId: textbookId
      });
      
      if (result.success) {
        const options = result.result.units.map(item => ({
          label: item.name,
          value: item.id
        }));
        this.setData({
          unitOptions: options
        });
      }
    } catch (error) {
      console.error('加载单元列表失败:', error);
    }
  },

  // 单元选择变化
  async onUnitChange(e) {
    const unitIdIndex = e.detail.value;
    const unit = this.data.unitOptions[unitIdIndex];
    const unitId = unit.value;
    
    this.setData({
      'filterConditions.unitIdIndex': unitIdIndex,
      'filterConditions.unitId': unitId,
      'filterConditions.unitName': unit ? unit.label : '',
      'filterConditions.chapterId': '',
      'filterConditions.chapterName': '',
      chapterOptions: []
    });
    
    if (unitId) {
      this.loadChapterOptions(unitId);
    }
  },

  // 加载章节选项
  async loadChapterOptions(unitId) {
    try {
      const { currentStudent } = this.data;
      const userInfo = wx.getStorageSync('loginUser');
      
      const result = await api.getWrongWordFilterOptions({
        accountId: userInfo?.userId,
        studentId: currentStudent?.id,
        type: 'chapter',
        unitId: unitId
      });
      
      if (result.success) {
        const options = result.result.chapters.map(item => ({
          label: item.name,
          value: item.id
        }));
        this.setData({
          chapterOptions: options
        });
      }
    } catch (error) {
      console.error('加载章节列表失败:', error);
    }
  },

  // 章节选择变化
  onChapterChange(e) {
    const chapterIndex = e.detail.value;
    const chapter = this.data.chapterOptions[chapterIndex];
    const chapterId = chapter.value;
    
    this.setData({
      'filterConditions.chapterIndex': chapterIndex,
      'filterConditions.chapterId': chapterId,
      'filterConditions.chapterName': chapter ? chapter.label : ''
    });
  },

  // 选择错误次数范围
  selectErrorCountRange(e) {
    const { range } = e.currentTarget.dataset;
    const currentRange = this.data.filterConditions.errorCountRange;
    
    this.setData({
      'filterConditions.errorCountRange': currentRange === range ? '' : range
    });
  },

  // 重置筛选条件
  resetFilter() {
    this.setData({
      filterConditions: {
        alphabet: '',
        textbookId: '',
        textbookName: '',
        unitId: '',
        unitName: '',
        chapterId: '',
        chapterName: '',
        errorCountRange: ''
      },
      unitOptions: [],
      chapterOptions: []
    });
  },

  // 应用筛选条件
  applyFilter() {
    this.setData({
      showFilterModal: false
    });
    this.loadWrongWords(true);
  },

  // ==================== 辅助方法 ====================
  
  // 检查单词是否被选中
  isWordSelected(word) {
    return this.data.selectedWords.some(w => w.id === word.id);
  },

  // 排序类型改变时切换排序顺序
  onSortTypeChange(e) {
    const { sort } = e.currentTarget.dataset;
    const currentSort = this.data.sortType;
    const currentOrder = this.data.sortOrder;
    
    let newOrder = 'desc';
    if (currentSort === sort) {
      // 如果是同一个排序类型，切换排序顺序
      newOrder = currentOrder === 'desc' ? 'asc' : 'desc';
    }
    
    this.setData({
      sortType: sort,
      sortOrder: newOrder
    });
    
    this.loadWrongWords(true);
  }
});