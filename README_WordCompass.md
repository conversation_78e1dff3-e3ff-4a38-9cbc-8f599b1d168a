# 单词指南针系统 V1.0

## 项目简介

单词指南针系统是一个专注于英语单词学习和管理的教育平台，包含后台管理端和微信小程序端。系统核心功能包括家长布置单词听写任务、检查任务完成情况、查看学习报告、单词库管理、错词库管理等。

## 系统架构

### 技术栈
- **后端**: Spring Boot + MyBatis Plus + MySQL
- **前端**: Vue 3 + Ant Design Vue + TypeScript  
- **小程序**: 微信小程序（待开发）

### 核心模块
- 任务模块：任务管理、创建任务、任务列表、任务批改
- 教材模块：教材管理、单元章节管理、版本管理
- 单词模块：单词管理、批量导入、单词库、错词库  
- 学习报告模块：学习情况统计、进度跟踪
- 学生模块：学生信息管理
- 用户模块：账号管理、微信用户管理

## 数据库设计

### 核心实体表
1. `wc_textbook` - 教材基本信息表
2. `wc_textbook_detail` - 教材明细信息表(单元、章节)
3. `wc_word` - 单词信息表
4. `wc_wrong_word` - 错词信息表
5. `wc_task` - 任务信息表
6. `wc_task_detail` - 任务明细信息表
7. `wc_student` - 学生信息表
8. `wc_account` - 账号信息表
9. `wc_wechat_user` - 微信用户信息表

### 数据库初始化
执行 `service/wordCompass-server/src/main/resources/word_compass_tables.sql` 文件进行数据库初始化。

## 后端API接口

### 教材管理 API
- `GET /word/textbook/list` - 分页查询教材列表
- `POST /word/textbook/add` - 新增教材
- `PUT /word/textbook/edit` - 编辑教材
- `DELETE /word/textbook/delete` - 删除教材
- `GET /word/textbook/listByGrade` - 根据年级查询教材

### 单词管理 API  
- `GET /word/wcword/list` - 分页查询单词列表
- `POST /word/wcword/add` - 新增单词
- `POST /word/wcword/importWordsFromExcel` - Excel批量导入单词
- `POST /word/wcword/importWordsFromText` - 文本批量导入单词
- `GET /word/wcword/listByTextbookId` - 根据教材ID查询单词
- `GET /word/wcword/searchByName` - 根据单词名称模糊查询

### 任务管理 API
- `GET /word/task/list` - 分页查询任务列表
- `POST /word/task/add` - 创建任务
- `PUT /word/task/correct` - 批改任务
- `PUT /word/task/finish` - 完成任务

## 前端页面结构

### 教材管理
- `wordcompass-ui/src/views/word/textbook/WcTextbookList.vue` - 教材列表页面
- `wordcompass-ui/src/views/word/textbook/modules/WcTextbookModal.vue` - 教材编辑模态框
- `wordcompass-ui/src/views/word/textbook/WcTextbook.data.ts` - 表格列配置
- `wordcompass-ui/src/views/word/textbook/WcTextbook.api.ts` - API接口

### 单词管理
- `wordcompass-ui/src/views/word/wcword/WcWordList.vue` - 单词列表页面（已实现基础版本）

## 功能特性

### 1. 教材管理
- ✅ 教材基本信息维护（名称、版本、册别、年级、出版社）
- ✅ 支持条件查询和分页
- ✅ 支持Excel导入导出
- ✅ 支持按年级和版本筛选

### 2. 教材明细管理
- ✅ 树形结构展示单元、章节信息
- ✅ 支持教材切换
- ✅ 支持层级管理

### 3. 单词管理
- ✅ 单词基本信息维护（单词、音标、译义、分类、难度等）
- ✅ 支持关联到教材、单元、章节
- ✅ Excel批量导入功能
- ✅ 文本识别导入功能
- ✅ 单词库查询功能

### 4. 错词管理
- ✅ 错词统计（听写次数、错误次数、正确率）
- ✅ 掌握状态跟踪
- ✅ 按学生和掌握状态查询

### 5. 任务管理
- ✅ 任务创建（支持多种任务类型）
- ✅ 任务列表展示
- ✅ 任务批改功能
- ✅ 任务完成统计

### 6. 学生管理
- ✅ 学生信息维护
- ✅ 按账号关联学生
- ✅ 支持多学生管理

### 7. 账号管理
- ✅ 用户注册登录
- ✅ 账号类型管理（普通用户、VIP用户）
- ✅ 微信用户关联

## 部署说明

### 后端部署
1. 环境要求：JDK 8+, MySQL 5.7+
2. 配置数据库连接信息
3. 执行SQL初始化脚本
4. 启动Spring Boot应用

### 前端部署
1. 环境要求：Node.js 14+
2. 安装依赖：`npm install`
3. 开发环境：`npm run dev`
4. 生产构建：`npm run build`

## 业务流程

### 1. 教材管理流程
1. 创建教材基本信息
2. 添加教材明细（单元、章节）
3. 录入单词信息并关联到章节

### 2. 任务管理流程
1. 家长选择教材/单元/章节
2. 系统自动获取相关单词
3. 创建听写任务分配给学生
4. 学生完成任务上传答案
5. 家长批改任务给出评分
6. 系统自动统计错词并加入错词库

### 3. 学习跟踪流程
1. 系统记录每次听写结果
2. 统计单词掌握情况
3. 生成学习报告
4. 推荐薄弱单词复习

## 扩展功能（待开发）

### 微信小程序端
- 学生登录和任务查看
- 语音听写功能
- 拍照上传答案
- 学习进度查看

### 智能功能
- AI语音识别批改
- 个性化学习推荐
- 学习数据分析
- 家长学习报告

### 系统优化
- 缓存机制优化
- 搜索性能优化
- 图片音频存储优化
- 移动端适配

## 技术亮点

1. **模块化设计**：清晰的业务模块划分，便于维护和扩展
2. **数据模型设计**：完整的教材-单词-任务关联模型
3. **批量导入**：支持Excel和文本多种导入方式
4. **树形结构**：教材明细的层级管理
5. **任务流程**：完整的任务创建-执行-批改流程
6. **统计分析**：丰富的学习数据统计功能

## 联系信息

如有任何问题或建议，请联系开发团队。

---
**单词指南针系统 V1.0** - 让英语学习更智能、更高效！ 