
后台管理前端项目

===============
当前最新版本： 3.7.1（发布时间：2024-09-12）


## 简介
采用 Vue3.0、Vite、 Ant-Design-Vue4、TypeScript 等新技术方案，包括二次封装组件、utils、hooks、动态菜单、权限校验、按钮级别权限控制等功能。


## 安装与使用

*   本地环境安装 `Node.js 、npm 、pnpm`
*   Node.js 版本建议`v20.15.0`，要求`Node 20+` 版本以上

 ` ( 因为Vite5 不再支持已 EOL 的 Node.js 14 / 16 / 17 / 19，现在需要 Node.js 18 / 20+ )`


- 开发环境 - 安装依赖

```bash
 

pnpm install
```

- 配置接口地址 `.env.development`

```bash
VITE_PROXY = [["/wordCompassApi","http://localhost:8080/wordCompassApi"],["/upload","http://localhost:3300/upload"]]
VITE_GLOB_DOMAIN_URL=http://localhost:8080/wordCompassApi
```


- run

```bash
pnpm serve
```


- build

```bash
pnpm build
```


- 生产环境
```bash
修改
km-ui/.env.production

# 前端发布路径
VITE_PUBLIC_PATH = /wordcompassui

#后台接口父地址(必填)
VITE_GLOB_API_URL=/wordCompassApi

#后台接口全路径地址(必填)
VITE_GLOB_DOMAIN_URL=http://localhost:8080/wordCompassApi/
```

- 打包

```bash

1. pnpm build
2. 修改 wordcompass-ui/dist   →    wordcompassui
3. wordcompassui 拷贝到 nginx/html/ 中
4. 修改nginx配置如下：
```


- nginx 配置相关
```config
location /wordcompassui/ {
    root   html;
    index index.html index.htm;
    try_files $uri $uri/ /wordcompassui/index.html;
}

location /wordCompassApi {
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header Host $http_host;
    proxy_pass  http://127.0.0.1:8080/wordCompassApi;
}
```



## 入门必备

本项目需要一定前端基础知识，请确保掌握 Vue 的基础知识，以便能处理一些常见的问题。 建议在开发前先学一下以下内容，提前了解和学习这些知识，会对项目理解非常有帮助:

**本地开发**推荐使用`Chrome 最新版`浏览器，**不支持**`Chrome 90`以下版本。

**生产环境**支持现代浏览器，不支持 IE。
