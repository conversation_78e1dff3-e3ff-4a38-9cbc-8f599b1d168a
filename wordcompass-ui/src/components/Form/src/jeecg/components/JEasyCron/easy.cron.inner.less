//noinspection LessUnresolvedVariable
@prefix-cls: ~'@{namespace}-easy-cron-inner';

.@{prefix-cls} {
  .content {
    .ant-checkbox-wrapper + .ant-checkbox-wrapper {
      margin-left: 0;
    }
  }

  &-config-list {
    text-align: left;
    margin: 0 10px 10px 10px;

    .item {
      margin-top: 5px;
      font-size: 14px;

      span {
        padding: 0 2px;
      }
    }

    .choice {
      padding: 5px 8px;
    }

    .w60 {
      width: 60px;
      min-width: 60px;
    }

    .w80 {
      width: 80px;
      min-width: 80px;
    }

    .list {
      margin: 0 20px;
    }

    .list-check-item {
      padding: 1px 3px;
      width: 4em;
    }

    .list-cn .list-check-item {
      width: 5em;
    }

    .tip-info {
      color: #999;
    }
  }

  .allow-click {
    cursor: pointer;
  }
}
