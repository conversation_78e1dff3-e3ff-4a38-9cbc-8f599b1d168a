<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    :title="getTitle"
    :width="1200"
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm">
      <!-- 自定义PPT文件上传插槽 -->
      <template #pptFileUpload="{ model, field }">
        <div>
          <JUpload
            :value="model[field]"
            @update:value="(val) => model[field] = val"
            @change="handlePptFileChange"
            :fileType="'file'"
            :bizPath="'ppt_templates'"
            :maxCount="1"
            :accept="['.ppt', '.pptx']"
            :beforeUpload="beforePptUpload"
            text="上传PPT模板文件"
          />
          <div v-if="uploadProgress > 0 && uploadProgress < 100" style="margin-top: 8px;">
            <a-progress :percent="uploadProgress" status="active" />
            <div style="font-size: 12px; color: #666;">正在解析PPT文件...</div>
          </div>
          <!-- 调试信息 -->
          <div style="margin-top: 8px; font-size: 12px; color: #999;">
            当前文件路径: {{ model[field] || '未上传' }}
          </div>
        </div>
      </template>
    </BasicForm>
    
    <!-- 布局管理区域 -->
    <div v-if="isUpdate" style="margin-top: 20px;">
      <Divider>布局管理</Divider>
      <div style="margin-bottom: 16px;">
        <!-- <a-button type="primary" @click="handleAddLayout" preIcon="ant-design:plus-outlined">
          添加布局
        </a-button> -->
        <a-button 
          v-if="model.filePath" 
          type="default" 
          @click="handleParsePpt" 
          preIcon="ant-design:reload-outlined"
          style="margin-left: 8px;"
        >
          重新解析PPT
        </a-button>
      </div>
      
      <BasicTable 
         @register="registerLayoutTable"
         @edit-end="handleEditEnd" 
         @edit-cancel="handleEditCancel" 
         :beforeEditSubmit="beforeEditSubmit"

      >
        <template #action="{ record }">
          <TableAction :actions="getLayoutTableAction(record)" />
        </template>
      </BasicTable>
    </div>

    <!-- 布局编辑模态框 -->
    <PptTemplateLayoutModal @register="registerLayoutModal" @success="handleLayoutSuccess" />

    <!-- 图片预览模态框 -->
    <a-modal
      :open="imagePreviewVisible"
      @update:open="imagePreviewVisible = $event"
      title="图片预览"
      :footer="null"
      :width="800"
      :destroyOnClose="true"
    >
      <div style="text-align: center;">
        <img 
          :src="currentPreviewImage" 
          style="max-width: 100%; max-height: 600px; object-fit: contain;"
          alt="布局图片预览"
        />
      </div>
    </a-modal>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, computed, unref, watch, h } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import { Divider } from 'ant-design-vue';
  import { JUpload } from '/@/components/Form/src/jeecg/components/JUpload';
  import { formSchema, layoutFormSchema } from '../PptTemplate.data';
  import { saveOrUpdate, queryDetailById, parsePpt, getStaticUrl, updateLayoutType } from '../PptTemplate.api';
  import { useMessage } from '/@/hooks/web/useMessage';
  import PptTemplateLayoutModal from './PptTemplateLayoutModal.vue';

  const emit = defineEmits(['register', 'success']);
  const { createMessage } = useMessage();

  const isUpdate = ref(true);
  const model = ref<any>({});
  const layoutList = ref<any[]>([]);
  const uploadProgress = ref(0);
  const imagePreviewVisible = ref(false);
  const currentPreviewImage = ref('');

  // 注册表单
  const [registerForm, { resetFields, setFieldsValue, validate, getFieldsValue, updateSchema }] = useForm({
    labelWidth: 120,
    schemas: formSchema,
    showActionButtonGroup: false,
  });

  // 监听文件路径变化
  watch(() => model.value.filePath, (newFilePath, oldFilePath) => {
    console.log('filePath changed:', oldFilePath, '->', newFilePath);
    if (newFilePath && newFilePath !== oldFilePath && !unref(isUpdate)) {
      console.log('Auto parsing PPT for new template');
      handleParsePpt();
    }
  });

  // 注册布局表格
  const [registerLayoutTable, { reload: reloadLayoutTable }] = useTable({
    title: '布局列表',
    columns: [
      {
        title: '页面序号',
        dataIndex: 'pageIndex',
        width: 100,
      },
      {
        title: 'ID',
        dataIndex: 'id',
        ifShow: true,
      },
      {
        title: '布局类型',
        dataIndex: 'layoutType',
        width: 150,
        editComponent: 'Select',
        edit: true,
        editable: true,
        editComponentProps: {
          options: [
            { label: '主题页', value: 'topic' },
            { label: '目录页', value: 'directory' },
            { label: '章节页', value: 'chapter' },
            { label: '内容页', value: 'content' },
            { label: '条目页', value: 'item' },
            { label: '结束页', value: 'end' },
          ]
        }
      },
      {
        title: '布局描述',
        dataIndex: 'layoutDesc',
        width: 200,
      },
      {
        title: '图片预览',
        dataIndex: 'imagePath',
        width: 200,
        customRender: ({ text, record }) => {
          if (!text) return '';
          return h('div', {
            style: { cursor: 'pointer' },
            onClick: () => handleImagePreview(record)
          }, [
            h('img', {
              src: getStaticUrl + text,
              style: { width: '100px', height: '60px', objectFit: 'cover', border: '1px solid #d9d9d9' },
              alt: '布局图片'
            }),
            h('div', {
              style: { fontSize: '12px', color: '#666', marginTop: '4px', textAlign: 'center' }
            }, '点击放大')
          ]);
        }
      },
      {
        title: '创建时间',
        dataIndex: 'createTime',
        width: 180,
      },
    ],
    dataSource: layoutList,
    pagination: false,
    actionColumn: {
      title: '操作',
      width: 120,
    },
  });

  // 注册布局模态框
  const [registerLayoutModal, { openModal: openLayoutModal }] = useModal();

  // 注册模态框
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    resetFields();
    setModalProps({confirmLoading: false,showCancelBtn:data?.showFooter,showOkBtn:data?.showFooter});
    isUpdate.value = !!data?.isUpdate;
    uploadProgress.value = 0;

    if (unref(isUpdate)) {
      const record = data?.record;
      if (record) {
        // 查询详情
        const detail = await queryDetailById({ id: record.id });
        if (detail) {
          model.value = detail;
          setFieldsValue({
            ...detail,
          });
          layoutList.value = detail.layoutList || [];
        }
      }
    } else {
      model.value = {};
      layoutList.value = [];
    }
  });

  // 获取标题
  const getTitle = computed(() => (!unref(isUpdate) ? '新增PPT模板' : '编辑PPT模板'));

  // PPT文件上传前验证
  function beforePptUpload(file) {
    const isPpt = file.type === 'application/vnd.ms-powerpoint' || 
                  file.type === 'application/vnd.openxmlformats-officedocument.presentationml.presentation' ||
                  file.name.endsWith('.ppt') || 
                  file.name.endsWith('.pptx');
    
    if (!isPpt) {
      createMessage.error('只能上传PPT文件!');
      return false;
    }
    
    const isLt50M = file.size / 1024 / 1024 < 50;
    if (!isLt50M) {
      createMessage.error('PPT文件大小不能超过50MB!');
      return false;
    }
    
    return true;
  }

  // PPT文件上传后处理
  async function handlePptFileChange(filePath) {
    console.log('handlePptFileChange called with filePath:', filePath);
    
    if (!filePath) {
      console.log('filePath is empty, returning');
      return;
    }

    try {
      // 更新表单中的文件路径
      const formValues = getFieldsValue();
      formValues.filePath = filePath;
      setFieldsValue(formValues);
      
      // 同时更新model中的文件路径
      model.value.filePath = filePath;
      
      console.log('Updated model.value.filePath:', model.value.filePath);
      console.log('Current isUpdate value:', unref(isUpdate));
      
      // 注意：这里不再直接调用handleParsePpt，而是通过watch监听来处理
    } catch (error) {
      console.error('Error in handlePptFileChange:', error);
      createMessage.error('文件处理失败: ' + (error as Error).message);
    }
  }

  // 解析PPT文件
  async function handleParsePpt() {
    console.log('handleParsePpt called');
    console.log('model.value.filePath:', model.value.filePath);
    console.log('model.value:', model.value);
    
    if (!model.value.filePath) {
      console.log('filePath is empty, showing warning');
      createMessage.warning('请先上传PPT文件');
      return;
    }

    try {
      uploadProgress.value = 10;
      
      const templateId = model.value.id || 'temp_' + Date.now();
      console.log('Using templateId:', templateId);
      
      // 调用后端解析接口
      const result = await parsePpt({
        pptPath: model.value.filePath,
        templateId: templateId,
        username: 'system' // 这里应该从用户信息中获取
      });

      uploadProgress.value = 100;
      createMessage.success('PPT文件解析成功！');
      console.log('PPT文件解析成功！'+ JSON.stringify(result));
      
      // 处理返回的布局信息
      if (result) {
        // 后端返回的布局信息在result.result中
        const parsedLayoutList = result;
        console.log('Received layout list:', parsedLayoutList);
        
        // 更新前端的布局列表
        layoutList.value = parsedLayoutList;
        reloadLayoutTable();
      } else {
        // 如果是编辑模式，重新查询详情
        if (model.value.id) {
          const detail = await queryDetailById({ id: model.value.id });
          if (detail) {
            layoutList.value = detail.layoutList || [];
            reloadLayoutTable();
          }
        }
      }
    } catch (error) {
      console.error('Error in handleParsePpt:', error);
      uploadProgress.value = 0;
      createMessage.error('PPT文件解析失败: ' + (error as Error).message);
    }
  }

  // 提交表单
  async function handleSubmit() {
    try {
      const values = await validate();
      setModalProps({ confirmLoading: true });
      
      const formData = {
        ...values,
        layoutList: layoutList.value,
      };

      console.log('Submitting form data:', formData);

      const result = await saveOrUpdate(formData, unref(isUpdate));
      
      createMessage.success(unref(isUpdate) ? '编辑成功!' : '新增成功!');
      closeModal();
      emit('success');
    } catch (error) {
      console.error('Error in handleSubmit:', error);
      createMessage.error('保存失败: ' + (error as Error).message);
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }

  // 添加布局
  function handleAddLayout() {
    openLayoutModal(true, {
      isUpdate: false,
      templateId: model.value?.id,
      showFooter: true,
    });
  }

  // 编辑布局
  function handleEditLayout(record) {
    openLayoutModal(true, {
      record,
      isUpdate: true,
      templateId: model.value?.id,
      showFooter: true,
    });
  }

  // 删除布局
  function handleDeleteLayout(record) {
    const index = layoutList.value.findIndex(item => item.id === record.id);
    if (index > -1) {
      layoutList.value.splice(index, 1);
      reloadLayoutTable();
    }
  }

  // 布局操作栏
  function getLayoutTableAction(record) {
    return [
      {
        label: '编辑',
        onClick: handleEditLayout.bind(null, record),
      },
      {
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDeleteLayout.bind(null, record),
        }
      }
    ];
  }

  // 布局成功回调
  function handleLayoutSuccess(layout) {
    const index = layoutList.value.findIndex(item => item.id === layout.id);
    if (index > -1) {
      layoutList.value[index] = layout;
    } else {
      layoutList.value.push(layout);
    }
    reloadLayoutTable();
  }


// 单元格 编辑结束 触发
function handleEditEnd({ record, index, key, value }: Recordable) {
  console.log("handleEditEnd--record",JSON.stringify(record));
  console.log("handleEditEnd--index",index);
  console.log("handleEditEnd--key",key);
  console.log("handleEditEnd--value",value);
  return false;
}

//保存 数据
function feakSave({ record, id, key, value }) {
  console.log("feakSave--record-原始值",JSON.stringify(record));
  console.log("feakSave--id",id);
  console.log("feakSave--key",key);
  console.log("feakSave--value",value);
  const layoutTypeMap = {
      'topic': '主题页',
      'directory': '目录页',
      'chapter': '章节页',
      'content': '内容页',
      'item': '条目页',
      'end': '结束页',
    };

  //把 key 对应的值 赋值到 record 中
  record[key] = value;
  record.layoutTypeName = layoutTypeMap[value] || '';
  console.log("feakSave--record-修改后的值",JSON.stringify(record));
  // 调用保存API
  updateLayoutType(record).then(
    () => {
      reloadLayoutTable();
    },
    (error) => {
      console.error(error);
    }
  );
  return true;
}

// 编辑提交 触发
async function beforeEditSubmit({ record, index, key, value }) {
  console.log('单元格数据正在准备提交-record', JSON.stringify(record));
  console.log("beforeEditSubmit--修改后的值",{ index, key, value });
   return await feakSave({ record, id: record.id, key, value });
}

// 编辑取消 触发
function handleEditCancel() {
  console.log('cancel');
}



  // 图片预览处理
  function handleImagePreview(record) {
    if (record.imagePath) {
      currentPreviewImage.value = getStaticUrl + record.imagePath;
      imagePreviewVisible.value = true;
    }
  }
</script> 