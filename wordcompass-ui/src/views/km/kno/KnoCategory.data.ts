import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';

export const columns: BasicColumn[] = [
    {
    title: '类别名称',
    dataIndex: 'name',
    width: 350,
    align: 'left',
   },
   {
    title: '父类别ID',
    dataIndex: 'parentId',
    defaultHidden: true,
   },
   {
    title: '层级',
    dataIndex: 'level'
   },
   {
    title: '排序',
    dataIndex: 'sort'
   },
   {
    title: '状态',
    dataIndex: 'status',
    customRender: ({ text }) => {
      return text === 1 ? '启用' : '禁用';
    }
   },
];

export const searchFormSchema: FormSchema[] = [
 {
    label: '类别名称',
    field: 'name',
    component: 'Input',
    colProps: { span: 6 },
  },
];

export const formSchema: FormSchema[] = [
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
  {
    label: '父级节点',
    field: 'parentId',
    component: 'TreeSelect',
    componentProps: {
      fieldNames: {
        value: 'key',
      },
      dropdownStyle: {
        maxHeight: '50vh',
      },
      getPopupContainer: () => document.body,
    },
    show: ({ values }) => {
      return values.parentId !== '0';
    },
    dynamicDisabled: ({ values }) => {
      return !!values.id;
    },
  },
  {
    label: '类别名称',
    field: 'name',
    required: true,
    component: 'Input',
  },
  {
    label: '层级',
    field: 'level',
    component: 'InputNumber',
    required: true,
  },
  {
    label: '排序',
    field: 'sort',
    component: 'InputNumber',
    required: true,
  },
  {
    label: '状态',
    field: 'status',
    component: 'RadioButtonGroup',
    defaultValue: 1,
    componentProps: {
      options: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 },
      ],
    },
  },
];
