import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { list as getKnoBaseList } from './KnoBase.api';


export const columns: BasicColumn[] = [
    {
    title: '知识',
    dataIndex: 'baseTitle'
   },
   {
    title: '访问用户',
    dataIndex: 'userId'
   },
   {
    title: 'IP地址',
    dataIndex: 'ipAddress'
   },
   {
    title: '用户代理',
    dataIndex: 'userAgent'
   },
   {
    title: '访问时间',
    dataIndex: 'accessTime'
   },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '知识标题',
    field: 'knowledgeTitle',
    component: 'Input',
    componentProps: {
      placeholder: '请选择所属知识',
      suffix: '',
      onclick: () => {
        // 触发选择弹窗
        const event = new Event('openSelectModal');
        window.dispatchEvent(event);
      },
    },
  },
  {
    label: '访问用户',
    field: 'userId',
    component: 'Input'
  },
  {
    label: '知识ID',
    field: 'knowledgeId',
    component: 'Input',
    show: false, // 隐藏此字段，仅用于查询使用
  },
  {
    label: '知识',
    field: 'baseTitle',
    component: 'Input',
    show: false, 
  },

];

export const formSchema: FormSchema[] = [
  // TODO 主键隐藏字段，目前写死为ID
  {label: '', field: 'id', component: 'Input', show: false},
  {
    label: '知识',
    field: 'baseTitle',
    component: 'Input',
  },
  {
    label: '访问用户',
    field: 'userId',
    component: 'Input',
  },
  {
    label: 'IP地址',
    field: 'ipAddress',
    component: 'Input',
  },
  {
    label: '用户代理',
    field: 'userAgent',
    component: 'Input',
  },
  {
    label: '访问时间',
    field: 'accessTime',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      valueFormat: 'YYYY-MM-DD hh:mm:ss',
    },
  },
];
