import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render, downloadFile } from '/@/utils/common/renderUtils';
import { h } from 'vue';
import { getLayoutTypeOptions, getStatusOptions, getFileTypeOptions } from './PptTemplate.api';

export const columns: BasicColumn[] = [
    {
        title: '模板名称',
        dataIndex: 'templateName',
        width: 200,
    },
    {
        title: '模板描述',
        dataIndex: 'templateDesc',
        width: 300,
    },
    {
        title: '文件名称',
        dataIndex: 'fileName',
        width: 200,
        defaultHidden: true,
    },
    {
        title: '文件大小',
        dataIndex: 'fileSize',
        width: 100,
        defaultHidden: true,
        customRender: ({text}) => {
            if (!text) return '';
            const size = parseInt(text);
            if (size < 1024) {
                return size + ' B';
            } else if (size < 1024 * 1024) {
                return (size / 1024).toFixed(2) + ' KB';
            } else {
                return (size / (1024 * 1024)).toFixed(2) + ' MB';
            }
        }
    },
    {
        title: '文件类型',
        dataIndex: 'fileType_dictText',
        width: 100,
    },
    {
        title: '状态',
        dataIndex: 'status_dictText',
        width: 100,
    },
    {
        title: '创建时间',
        dataIndex: 'createTime',
        width: 180,
        defaultHidden: true,
    },
    {
        title: '更新时间',
        dataIndex: 'updateTime',
        width: 180,
        defaultHidden: true,
    },
    {
        title: '创建者',
        dataIndex: 'creatorName',
            width: 120,
        defaultHidden: true,
    },
    {
        title: '更新者',
        dataIndex: 'updaterName',
        width: 120,
        defaultHidden: true,
    },
    {
        title: '模板下载',
        dataIndex: 'filePath',
        width: 120,
        customRender: ({text}) => {
            if (!text) return '';
            return h('a', {
                onClick: () => {
                    downloadFile("/" + text);
                }
            }, '点击下载')
        }
    },
];

export const searchFormSchema: FormSchema[] = [
    {
        label: '模板名称',
        field: 'templateName',
        component: 'Input',
        colProps: { span: 4 },
    },
    {
        label: '模板描述',
        field: 'templateDesc',
        component: 'Input',
        colProps: { span: 4 },
    },
    {
        label: '状态',
        field: 'status',
        component: 'ApiSelect',
        componentProps: {
            api: getStatusOptions,
            resultField: 'result',
            labelField: 'name',
            valueField: 'code',
            allowClear: true,
        },
        colProps: { span: 4 },
    },
];

export const formSchema: FormSchema[] = [
    // 主键隐藏字段
    {label: '', field: 'id', component: 'Input', show: false},
    {
        label: '模板名称',
        field: 'templateName',
        component: 'Input',
        required: true,
        colProps: { span: 24 },
    },
    {
        label: '模板描述',
        field: 'templateDesc',
        component: 'InputTextArea',
        colProps: { span: 24 },
    },
    {
        label: 'PPT模板文件',
        field: 'filePath',
        component: 'Input',
        slot: 'pptFileUpload',
        required: true,
        colProps: { span: 24 },
    },
    {
        label: '状态',
        field: 'status',
        component: 'ApiSelect',
        required: true,
        componentProps: {
            api: getStatusOptions,
            resultField: 'result',
            labelField: 'name',
            valueField: 'code',
            allowClear: true,
        },
        colProps: { span: 12 },
    },
    {
        label: '文件类型',
        field: 'fileType',
        component: 'ApiSelect',
        componentProps: {
            api: getFileTypeOptions,
            resultField: 'result',
            labelField: 'name',
            valueField: 'code',
            allowClear: true,
            disabled: false,
        },
        colProps: { span: 12 },
    },
];

// 布局表单配置
export const layoutFormSchema: FormSchema[] = [
    {label: '', field: 'id', component: 'Input', show: false},
    {label: '', field: 'templateId', component: 'Input', show: false},
    {
        label: '页面序号',
        field: 'pageIndex',
        component: 'InputNumber',
        required: true,
        colProps: { span: 12 },
    },
    {
        label: '布局类型',
        field: 'layoutType',
        component: 'Select',
        required: true,
        componentProps: {
            api: getLayoutTypeOptions,
            resultField: 'result',
            labelField: 'name',
            valueField: 'code',
            allowClear: true,
            disabled: true,
        },
        colProps: { span: 12 },
    },
    {
        label: '布局描述',
        field: 'layoutDesc',
        component: 'InputTextArea',
        colProps: { span: 24 },
    },
    {
        label: '图片预览',
        field: 'imagePath',
        component: 'Input',
        slot: 'imagePreview',
        colProps: { span: 24 },
    },
]; 