import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';

export const columns: BasicColumn[] = [
    {
    title: '知识',
    dataIndex: 'knoBaseTitle'
   },
   {
    title: '评论用户',
    dataIndex: 'userId'
   },
   {
    title: '评论内容',
    dataIndex: 'content'
   },
   {
    title: '评论时间',
    dataIndex: 'createTime'
   },
   {
    title: '父评论ID，0表示一级评论',
    dataIndex: 'parentId',
    defaultHidden: true
   },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '所属知识',
    field: 'knowledgeTitle',
    component: 'Input',
    componentProps: {
      placeholder: '请选择所属知识',
      suffix: '',
      onclick: () => {
        // 触发选择弹窗
        const event = new Event('openSelectModal');
        window.dispatchEvent(event);
      },
    },
  },
  {
    label: '知识ID',
    field: 'knowledgeId',
    component: 'Input',
    show: false, // 隐藏此字段，仅用于查询使用
  },
 {
    label: '评论用户',
    field: 'userId',
    component: 'Input'
  },
];

export const formSchema: FormSchema[] = [
  // TODO 主键隐藏字段，目前写死为ID
  {label: '', field: 'id', component: 'Input', show: false},
  {
    label: '知识',
    field: 'knoBaseTitle',
    component: 'Input',
  },
  {
    label: '评论用户ID',
    field: 'userId',
    component: 'Input',
  },
  {
    label: '评论内容',
    field: 'content',
    component: 'Input',
  },
  {
    label: '父评论ID，0表示一级评论',
    field: 'parentId',
    component: 'InputNumber',
    show: false
  },
];
