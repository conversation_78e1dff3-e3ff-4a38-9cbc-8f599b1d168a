import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';

export const columns: BasicColumn[] = [
    {
    title: 'AI知识',
    dataIndex: 'knowledgeId'
   },
   {
    title: '历史标题',
    dataIndex: 'title'
   },
   {
    title: '历史内容',
    dataIndex: 'content'
   },
   {
    title: '版本号',
    dataIndex: 'version'
   },
   {
    title: '变更说明',
    dataIndex: 'changeLog'
   },
   {
    title: '使用的AI模型名称',
    dataIndex: 'modelName'
   },
   {
    title: '操作人名称',
    dataIndex: 'operatorName'
   },
];

export const searchFormSchema: FormSchema[] = [
 {
    label: 'AI知识',
    field: 'knowledgeId',
    component: 'Input'
  },
 {
    label: '历史标题',
    field: 'title',
    component: 'Input'
  },
];

export const formSchema: FormSchema[] = [
  // TODO 主键隐藏字段，目前写死为ID
  {label: '', field: 'id', component: 'Input', show: false},
  {
    label: 'AI知识',
    field: 'knowledgeId',
    component: 'Input',
  },
  {
    label: '历史标题',
    field: 'title',
    component: 'Input',
  },
  {
    label: '历史内容',
    field: 'content',
    component: 'Input',
  },
  {
    label: '版本号',
    field: 'version',
    component: 'InputNumber',
  },
  {
    label: '变更说明',
    field: 'changeLog',
    component: 'Input',
  },
  {
    label: '使用的AI模型名称',
    field: 'modelName',
    component: 'Input',
  },
  {
    label: '操作人名称',
    field: 'operatorName',
    component: 'Input',
  },
];
