<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" @click="handleCreate" preIcon="ant-design:plus-outlined">
          新增
        </a-button>
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined"></Icon>
                删除
              </a-menu-item>
            </a-menu>
          </template>
          <a-button style="margin-left: 8px;">
            批量操作
            <Icon icon="mdi:chevron-down"></Icon>
          </a-button>
        </a-dropdown>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction
          :actions="getTableAction(record)"
          :dropDownActions="getDropDownAction(record)"
        />
      </template>
    </BasicTable>
    
    <!-- 表单区域 -->
    <WcAccountModal @register="registerModal" @success="handleSuccess" />
  </div>
</template>

<script lang="ts" name="word-wcaccount" setup>
  import { ref } from 'vue';
  import { BasicTable, TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { Icon } from '/@/components/Icon';
  import WcAccountModal from './modules/WcAccountModal.vue';
  import { columns, searchFormSchema } from './WcAccount.data';
  import {
    list,
    deleteOne,
    batchDelete,
    changeStatus,
    createSysUser
  } from './WcAccount.api';

  const { createMessage, createConfirm } = useMessage();
  const [registerModal, { openModal }] = useModal();

  // 列表页面公共参数、方法
  const { prefixCls, tableContext } = useListPage({
    tableProps: {
      title: '用户管理',
      api: list,
      columns,
      canResize: false,
      formConfig: {
        labelWidth: 120,
        schemas: searchFormSchema,
        autoSubmitOnEnter: true,
        showAdvancedButton: true,
      },
      actionColumn: {
        width: 180,
        title: '操作',
        dataIndex: 'action',
        slots: { customRender: 'action' },
      },
      beforeFetch: (params) => {
        return Object.assign(params);
      },
    }
  });

  const [registerTable, { reload }, { rowSelection, selectedRowKeys }] = tableContext;

  /**
   * 新增事件
   */
  function handleCreate() {
    openModal(true, {
      isUpdate: false,
      showFooter: true,
    });
  }

  /**
   * 编辑事件
   */
  function handleEdit(record) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  }

  /**
   * 详情事件
   */
  function handleDetail(record) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: false,
    });
  }

  /**
   * 删除事件
   */
  function handleDelete(record) {
    deleteOne({ id: record.id }, handleSuccess);
  }

  /**
   * 批量删除事件
   */
  function batchHandleDelete() {
    batchDelete({ ids: selectedRowKeys.value }, handleSuccess);
  }

  /**
   * 修改状态事件
   */
  function handleChangeStatus(record) {
    const newStatus = record.status === 1 ? 0 : 1;
    const statusText = newStatus === 1 ? '启用' : '禁用';
    
    createConfirm({
      iconType: 'warning',
      title: `确认${statusText}`,
      content: `是否${statusText}该用户？`,
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        return changeStatus({ id: record.id, status: newStatus }).then(() => {
          createMessage.success(`${statusText}成功！`);
          reload();
        });
      }
    });
  }

  /**
   * 创建系统账号事件
   */
  function handleCreateSysUser(record) {
    createConfirm({
      iconType: 'warning',
      title: '确认创建系统账号',
      content: '是否为该用户创建系统账号？',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        return createSysUser(record.id).then(() => {
          createMessage.success('创建系统账号成功！');
          reload();
        });
      }
    });
  }

  /**
   * 成功回调
   */
  function handleSuccess() {
    reload();
  }

  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        ifShow: () => true,
      },
      {
        label: record.status === 1 ? '禁用' : '启用',
        onClick: handleChangeStatus.bind(null, record),
        ifShow: () => true,
      },
      {
        label: '创建系统账号',
        onClick: handleCreateSysUser.bind(null, record),
        ifShow: () => !record.sysUsername,
      },
    ];
  }

  /**
   * 下拉操作栏
   */
  function getDropDownAction(record) {
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      },
      {
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
        },
      },
    ];
  }
</script>

<style lang="less" scoped>
  .search-form {
    margin-bottom: 16px;
    padding: 16px;
    background: #fafafa;
    border-radius: 6px;
  }

  .table-toolbar {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
  }
</style>
