<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" @click="handleCreate" preIcon="ant-design:plus-outlined">
          新增
        </a-button>
        <a-button type="primary" @click="handleImportExcel" style="margin-left: 8px;">
          <Icon icon="ant-design:upload-outlined" />
          Excel导入
        </a-button>
        <a-button type="primary" @click="handleImportText" style="margin-left: 8px;">
          <Icon icon="ant-design:file-text-outlined" />
          文本导入
        </a-button>
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined"></Icon>
                删除
              </a-menu-item>
            </a-menu>
          </template>
          <a-button style="margin-left: 8px;">
            批量操作
            <Icon icon="mdi:chevron-down"></Icon>
          </a-button>
        </a-dropdown>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction
          :actions="getTableAction(record)"
          :dropDownActions="getDropDownAction(record)"
        />
      </template>
      <!--自定义音频播放列-->
      <template #audioFile="{ record }">
        <div v-if="record.audioFile" style="display: flex; gap: 8px; align-items: center;">
          <a-button 
            type="link" 
            @click="playAudio(record)"
            :style="{ color: playingAudioId === record.id ? '#52c41a' : '#1890ff' }"
          >
            <Icon :icon="playingAudioId === record.id ? 'ant-design:pause-circle-outlined' : 'ant-design:sound-outlined'" />
            {{ playingAudioId === record.id ? '停止' : '播放' }}
          </a-button>
        </div>
      </template>
    </BasicTable>
    
    <!-- 表单区域 -->
    <WcWordModal @register="registerModal" @success="handleSuccess" />
    
    <!-- 导入模态框 -->
    <WcWordImportModal @register="registerImportModal" @success="handleSuccess" />
  </div>
</template>

<script lang="ts" name="word-wcword" setup>
  import { ref, reactive } from 'vue';
  import { BasicTable, TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { Icon } from '/@/components/Icon';
  import WcWordModal from './modules/WcWordModal.vue';
  import WcWordImportModal from './modules/WcWordImportModal.vue';
  import { columns, searchFormSchema } from './WcWord.data';
  import {
    list,
    deleteOne,
    batchDelete
  } from './WcWord.api';
  import { getFileAccessHttpUrl } from '/@/utils/common/compUtils';

  const { createMessage } = useMessage();
  const [registerModal, { openModal }] = useModal();
  const [registerImportModal, { openModal: openImportModal }] = useModal();

  // 列表页面公共参数、方法
  const { prefixCls, tableContext } = useListPage({
    tableProps: {
      title: '单词基本信息',
      api: list,
      columns,
      canResize: false,
      formConfig: {
        labelWidth: 120,
        schemas: searchFormSchema,
        autoSubmitOnEnter: true,
        showAdvancedButton: true,
      },
      actionColumn: {
        width: 120,
        title: '操作',
        dataIndex: 'action',
        slots: { customRender: 'action' },
      },
      beforeFetch: (params) => {
        return Object.assign(params);
      },
    }
  });

  const [registerTable, { reload }, { rowSelection, selectedRowKeys }] = tableContext;

  // 在 setup 中添加音频播放状态管理
  const playingAudioId = ref<string | null>(null);
  let currentAudio: HTMLAudioElement | null = null;

  /**
   * 新增事件
   */
  function handleCreate() {
    openModal(true, {
      isUpdate: false,
      showFooter: true,
    });
  }

  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  }

  /**
   * 详情事件
   */
  function handleDetail(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: false,
    });
  }

  /**
   * 删除事件
   */
  function handleDelete(record) {
    deleteOne({ id: record.id }, handleSuccess);
  }

  /**
   * 批量删除事件
   */
  function batchHandleDelete() {
    batchDelete({ ids: selectedRowKeys.value }, handleSuccess);
  }

  /**
   * 成功回调
   */
  function handleSuccess() {
    reload();
  }

  /**
   * Excel导入事件
   */
  function handleImportExcel() {
    openImportModal(true, {
      type: 'excel',
    });
  }

  /**
   * 文本导入事件
   */
  function handleImportText() {
    openImportModal(true, {
      type: 'text',
    });
  }

  /**
   * 播放音频
   */
  function playAudio(record) {
    if (playingAudioId.value === record.id) {
      // 如果正在播放同一个音频，则停止
      stopAudio();
    } else {
      // 停止当前播放的音频
      if (currentAudio) {
        stopAudio();
      }
      
      if (record.audioFile) {
        const audioUrl = getFileAccessHttpUrl(record.audioFile);
        currentAudio = new Audio(audioUrl);
        playingAudioId.value = record.id;
        
        currentAudio.addEventListener('ended', () => {
          playingAudioId.value = null;
          currentAudio = null;
        });
        
        currentAudio.play().catch(error => {
          console.error('音频播放失败', error);
          createMessage.error('音频播放失败');
          playingAudioId.value = null;
          currentAudio = null;
        });
      }
    }
  }

  function stopAudio() {
    if (currentAudio) {
      currentAudio.pause();
      currentAudio.currentTime = 0;
      currentAudio = null;
    }
    playingAudioId.value = null;
  }

  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
      },
    ];
  }

  /**
   * 下拉操作栏
   */
  function getDropDownAction(record) {
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      },
      {
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
        },
      },
    ];
  }
</script>

<style lang="less" scoped>
  .search-form {
    margin-bottom: 16px;
    padding: 16px;
    background: #fafafa;
    border-radius: 6px;
  }

  .table-toolbar {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
  }
</style>
