<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" @click="handleCreate" preIcon="ant-design:plus-outlined">
          新增
        </a-button>
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined"></Icon>
                删除
              </a-menu-item>
            </a-menu>
          </template>
          <a-button style="margin-left: 8px;">
            批量操作
            <Icon icon="mdi:chevron-down"></Icon>
          </a-button>
        </a-dropdown>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction
          :actions="getTableAction(record)"
          :dropDownActions="getDropDownAction(record)"
        />
      </template>
    </BasicTable>
    
    <!-- 表单区域 -->
    <WcWechatUserModal @register="registerModal" @success="handleSuccess" />
  </div>
</template>

<script lang="ts" name="word-wcwechatuser" setup>
  import { ref } from 'vue';
  import { BasicTable, TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { Icon } from '/@/components/Icon';
  import WcWechatUserModal from './modules/WcWechatUserModal.vue';
  import { columns, searchFormSchema } from './WcWechatUser.data';
  import {
    list,
    deleteOne,
    batchDelete,
    changeStatus
  } from './WcWechatUser.api';

  const { createMessage, createConfirm } = useMessage();
  const [registerModal, { openModal }] = useModal();

  // 列表页面公共参数、方法
  const { prefixCls, tableContext } = useListPage({
    tableProps: {
      title: '微信用户管理',
      api: list,
      columns,
      canResize: false,
      formConfig: {
        labelWidth: 120,
        schemas: searchFormSchema,
        autoSubmitOnEnter: true,
        showAdvancedButton: true,
      },
      actionColumn: {
        width: 180,
        title: '操作',
        dataIndex: 'action',
        slots: { customRender: 'action' },
      },
      beforeFetch: (params) => {
        return Object.assign(params);
      },
    }
  });

  const [registerTable, { reload }, { rowSelection, selectedRowKeys }] = tableContext;

  /**
   * 新增事件
   */
  function handleCreate() {
    openModal(true, {
      isUpdate: false,
      showFooter: true,
    });
  }

  /**
   * 编辑事件
   */
  function handleEdit(record) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  }

  /**
   * 详情事件
   */
  function handleDetail(record) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: false,
    });
  }

  /**
   * 删除事件
   */
  function handleDelete(record) {
    deleteOne({ id: record.id }, handleSuccess);
  }

  /**
   * 批量删除事件
   */
  function batchHandleDelete() {
    batchDelete({ ids: selectedRowKeys.value }, handleSuccess);
  }

  /**
   * 修改状态事件
   */
  function handleChangeStatus(record) {
    const newStatus = record.status === 1 ? 0 : 1;
    const statusText = newStatus === 1 ? '启用' : '禁用';
    
    createConfirm({
      iconType: 'warning',
      title: `确认${statusText}`,
      content: `是否${statusText}该微信用户？`,
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        return changeStatus({ id: record.id, status: newStatus }).then(() => {
          createMessage.success(`${statusText}成功！`);
          reload();
        });
      }
    });
  }

  /**
   * 成功回调
   */
  function handleSuccess() {
    reload();
  }

  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '查看',
        onClick: handleDetail.bind(null, record),
      },
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
      },
    ];
  }

  /**
   * 下拉操作栏
   */
  function getDropDownAction(record) {
    return [
      {
        label: record.status === 1 ? '禁用' : '启用',
        popConfirm: {
          title: `确认${record.status === 1 ? '禁用' : '启用'}吗？`,
          confirm: handleChangeStatus.bind(null, record),
        },
      },
      {
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
        },
      },
    ];
  }
</script>