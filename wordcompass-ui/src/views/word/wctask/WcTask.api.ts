import { defHttp } from '/@/utils/http/axios';
import { UploadFileParams } from '/@/types/axios';
import { useGlobSetting } from '/@/hooks/setting';

const globSetting = useGlobSetting();
const baseUploadUrl = globSetting.uploadUrl;

enum Api {
  list = '/word/wctask/list',
  save = '/word/wctask/add',
  edit = '/word/wctask/edit',
  deleteOne = '/word/wctask/delete',
  deleteBatch = '/word/wctask/deleteBatch',
  getById = '/word/wctask/queryById',
  listByAccountId = '/word/wctask/listByAccountId',
  listByStudentId = '/word/wctask/listByStudentId',
  listByStatus = '/word/wctask/listByStatus',
  listByTaskType = '/word/wctask/listByTaskType',
  detailsByTaskId = '/word/wctask/detailsByTaskId',
  correctTask = '/word/wctask/correctTask',
  uploadHomeworkPhoto = '/word/wctask/uploadHomeworkPhoto',
}

/**
 * 获取任务列表
 * @param params 查询参数
 * @returns 任务列表
 */
export const list = (params) => defHttp.get({ url: Api.list, params });

/**
 * 保存任务
 * @param params 任务数据
 * @returns 保存结果
 */
export const save = (params) => defHttp.post({ url: Api.save, params });

/**
 * 编辑任务
 * @param params 任务数据
 * @returns 编辑结果
 */
export const edit = (params) => defHttp.post({ url: Api.edit, params });


/**
 * 保存或者更新
 */
export const saveOrUpdate = (params, isUpdate) => {
  const url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params }, { isTransformResponse: false });
};

/**
 * 删除任务
 * @param params 任务ID
 * @returns 删除结果
 */
export const deleteOne = (params) => defHttp.delete({ url: Api.deleteOne, params, data: params }, { joinParamsToUrl: true });

/**
 * 批量删除任务
 * @param params 任务ID数组
 * @returns 删除结果
 */
export const deleteBatch = (params) => defHttp.delete({ url: Api.deleteBatch, params }, { joinParamsToUrl: true });

/**
 * 根据ID获取任务详情
 * @param params 任务ID
 * @returns 任务详情
 */
export const getById = (params) => defHttp.get({ url: Api.getById, params });

/**
 * 根据用户ID获取任务列表
 * @param params 用户ID
 * @returns 任务列表
 */
export const listByAccountId = (params) => defHttp.get({ url: Api.listByAccountId, params });

/**
 * 根据学生ID获取任务列表
 * @param params 学生ID
 * @returns 任务列表
 */
export const listByStudentId = (params) => defHttp.get({ url: Api.listByStudentId, params });

/**
 * 根据状态获取任务列表
 * @param params 状态
 * @returns 任务列表
 */
export const listByStatus = (params) => defHttp.get({ url: Api.listByStatus, params });

/**
 * 根据任务类型获取任务列表
 * @param params 任务类型
 * @returns 任务列表
 */
export const listByTaskType = (params) => defHttp.get({ url: Api.listByTaskType, params });

/**
 * 根据任务ID获取任务明细
 * @param params 任务ID
 * @returns 任务明细列表
 */
export const detailsByTaskId = (params) => defHttp.get({ url: Api.detailsByTaskId, params });

/**
 * 根据任务ID获取任务详情（包含明细）
 * @param id 任务ID
 * @returns 任务详情（包含明细）
 */
export const getTaskDetailById = async (id) => {
  try {
    // 先获取任务基本信息
    const taskInfo = await defHttp.get({ url: Api.getById, params: { id } });
    if (!taskInfo) return null;
    
    // 再获取任务明细列表
    const detailList = await defHttp.get({ url: Api.detailsByTaskId, params: { taskId: id } });
    
    // 合并数据
    return {
      success:true,
      code:200,
      message:'获取成功',
      taskInfoRes:taskInfo || {},
      taskDetailList: detailList || [],
    };
  } catch (error) {
    console.error('获取任务详情失败', error);
    return null;
  }
};

/**
 * 批改任务
 * @param params 任务数据
 * @returns 批改结果
 */
export const correctTask = (params) => defHttp.post({ url: Api.correctTask, params });

/**
 * 上传作业照片
 * @param params 上传参数
 * @param onUploadProgress 上传进度回调
 * @returns 上传结果
 */
export function uploadHomeworkPhoto(params: UploadFileParams, onUploadProgress: (progressEvent: ProgressEvent) => void) {
  return defHttp.uploadFile(
    {
      url: Api.uploadHomeworkPhoto,
      onUploadProgress,
    },
    params
  );
}
