import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';

// 教材选择表单
export const textbookSelectSchema: FormSchema[] = [
  {
    label: '年级',
    field: 'grade',
    component: 'Select',
    componentProps: {
      options: [
        { label: '一年级', value: '一年级' },
        { label: '二年级', value: '二年级' },
        { label: '三年级', value: '三年级' },
        { label: '四年级', value: '四年级' },
        { label: '五年级', value: '五年级' },
        { label: '六年级', value: '六年级' },
        { label: '七年级', value: '七年级' },
        { label: '八年级', value: '八年级' },
        { label: '九年级', value: '九年级' },
      ],
      placeholder: '请选择年级',
      allowClear: true,
    },
    colProps: { span: 8 },
  },
  {
    label: '版本',
    field: 'version',
    component: 'Input',
    componentProps: {
      placeholder: '请输入版本',
      allowClear: true,
    },
    colProps: { span: 8 },
  },
  {
    label: '教材名称',
    field: 'textbookName',
    component: 'Input',
    componentProps: {
      placeholder: '请输入教材名称',
      allowClear: true,
    },
    colProps: { span: 8 },
  },
];

// 教材明细表单
export const detailFormSchema: FormSchema[] = [
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
  {
    label: '',
    field: 'textbookId',
    component: 'Input',
    show: false,
  },
  {
    label: '',
    field: 'parentId',
    component: 'Input',
    show: false,
  },
  {
    label: '名称',
    field: 'name',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入名称',
    },
  },
  {
    label: '类型',
    field: 'type',
    component: 'Select',
    required: true,
    componentProps: {
      options: [
        { label: '单元', value: 'unit' },
        { label: '章节', value: 'chapter' },
        { label: '课文', value: 'lesson' },
      ],
      placeholder: '请选择类型',
    },
  },
  {
    label: '内容',
    field: 'content',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入内容',
      rows: 4,
    },
  },
  {
    label: '排序',
    field: 'sortOrder',
    component: 'InputNumber',
    required: true,
    componentProps: {
      placeholder: '请输入排序号',
      min: 0,
      style: 'width: 100%',
    },
  },
  {
    label: '状态',
    field: 'status',
    component: 'Select',
    defaultValue: 1,
    componentProps: {
      options: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 },
      ],
      placeholder: '请选择状态',
    },
  },
  {
    label: '备注',
    field: 'remark',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入备注',
      rows: 2,
    },
  },
];

// 教材明细表格列
export const detailColumns: BasicColumn[] = [
  {
    title: '名称',
    dataIndex: 'name',
    width: 200,
  },
  {
    title: '类型',
    dataIndex: 'type',
    width: 100,
    customRender: ({ text }) => {
      const typeMap = {
        unit: '单元',
        chapter: '章节',
        lesson: '课文',
      };
      return typeMap[text] || text;
    },
  },
  {
    title: '内容',
    dataIndex: 'content',
    width: 300,
    ellipsis: true,
  },
  {
    title: '排序',
    dataIndex: 'sortOrder',
    width: 80,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 80,
    customRender: ({ text }) => {
      return text === 1 ? '启用' : '禁用';
    },
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 180,
  },
]; 