import { defHttp } from '/@/utils/http/axios';
import { useMessage } from "/@/hooks/web/useMessage";

const { createConfirm } = useMessage();

enum Api {
  list = '/word/wcstudent/list',
  save = '/word/wcstudent/add',
  edit = '/word/wcstudent/edit',
  deleteOne = '/word/wcstudent/delete',
  deleteBatch = '/word/wcstudent/deleteBatch',
  getById = '/word/wcstudent/queryById',
  changeStatus = '/word/wcstudent/changeStatus',
  uploadAvatar = '/word/api/annex/uploadUserAvatar',
  getAccountList = '/word/wcaccount/list',

  getStudentsByAccountId = '/word/wcstudent/listByAccountId',
}

/**
 * 获取学生列表
 * @param params 查询参数
 */
export const list = (params) => defHttp.get({ url: Api.list, params });

/**
 * 保存或更新学生信息
 * @param params 学生信息
 * @param isUpdate 是否是更新操作
 */
export const saveOrUpdate = (params, isUpdate) => {
  const url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url, params });
};

/**
 * 删除单个学生
 * @param params 包含id的参数对象
 * @param handleSuccess 成功回调函数
 */
export const deleteOne = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.deleteOne, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess && handleSuccess();
  });
};

/**
 * 批量删除学生
 * @param params 包含ids数组的参数对象
 * @param handleSuccess 成功回调函数
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据?',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.deleteBatch, params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess && handleSuccess();
      });
    },
  });
};

/**
 * 修改学生状态
 * @param params 包含id和status的参数对象
 */
export const changeStatus = (params) => {
  return defHttp.post({ url: Api.changeStatus, params });
};

/**
 * 上传头像
 * @param params 上传参数
 * @param success 成功回调函数
 */
export const uploadAvatar = (params, success) => {
  return defHttp.uploadFile({ url: Api.uploadAvatar }, params, { success });
};

/**
 * 获取用户列表（用于选择关联用户）
 * @param params 查询参数
 */
export const getAccountList = (params) => defHttp.get({ url: Api.getAccountList, params });


/**
 * 根据账号ID获取学生列表
 */
export const getStudentsByAccountId = (accountId) => {
  // 发送GET请求，参数为accountId
  return defHttp.get({ 
    url: Api.getStudentsByAccountId, 
    params: { ...accountId } 
  });
};
