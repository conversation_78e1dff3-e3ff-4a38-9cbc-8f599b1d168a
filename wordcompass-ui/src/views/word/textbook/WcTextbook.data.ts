import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';

export const columns: BasicColumn[] = [
  {
    title: '教材名称',
    dataIndex: 'textbookName',
    width: 200,
  },
  {
    title: '版本',
    dataIndex: 'version',
    width: 120,
  },
  {
    title: '册别',
    dataIndex: 'volume',
    width: 120,
  },
  {
    title: '年级',
    dataIndex: 'grade',
    width: 120,
  },
  {
    title: '出版社',
    dataIndex: 'publisher',
    width: 150,
  },
  {
    title: '排序号',
    dataIndex: 'sortOrder',
    width: 100,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 80,
    customRender: ({ text }) => {
      return render.renderDict(text, 'valid_status');
    },
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 180,
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '教材名称',
    field: 'textbookName',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '年级',
    field: 'grade',
    component: 'Select',
    componentProps: {
      options: [
        { label: '一年级', value: '一年级' },
        { label: '二年级', value: '二年级' },
        { label: '三年级', value: '三年级' },
        { label: '四年级', value: '四年级' },
        { label: '五年级', value: '五年级' },
        { label: '六年级', value: '六年级' },
        { label: '七年级', value: '七年级' },
        { label: '八年级', value: '八年级' },
        { label: '九年级', value: '九年级' },
      ],
    },
    colProps: { span: 6 },
  },
  {
    label: '版本',
    field: 'version',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '状态',
    field: 'status',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'valid_status',
      placeholder: '请选择状态',
    },
    colProps: { span: 6 },
  },
];

export const formSchema: FormSchema[] = [
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
  {
    label: '教材名称',
    field: 'textbookName',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入教材名称',
    },
  },
  {
    label: '版本',
    field: 'version',
    component: 'Input',
    componentProps: {
      placeholder: '请输入版本，如：PEP',
    },
  },
  {
    label: '册别',
    field: 'volume',
    component: 'Select',
    componentProps: {
      options: [
        { label: '上册', value: '上册' },
        { label: '下册', value: '下册' },
        { label: '全册', value: '全册' },
      ],
      placeholder: '请选择册别',
    },
  },
  {
    label: '年级',
    field: 'grade',
    component: 'Select',
    required: true,
    componentProps: {
      options: [
        { label: '一年级', value: '一年级' },
        { label: '二年级', value: '二年级' },
        { label: '三年级', value: '三年级' },
        { label: '四年级', value: '四年级' },
        { label: '五年级', value: '五年级' },
        { label: '六年级', value: '六年级' },
        { label: '七年级', value: '七年级' },
        { label: '八年级', value: '八年级' },
        { label: '九年级', value: '九年级' },
      ],
      placeholder: '请选择年级',
    },
  },
  {
    label: '出版社',
    field: 'publisher',
    component: 'Input',
    componentProps: {
      placeholder: '请输入出版社名称',
    },
  },
  {
    label: '排序号',
    field: 'sortOrder',
    component: 'InputNumber',
    componentProps: {
      placeholder: '请输入排序号',
      min: 0,
    },
  },
  {
    label: '状态',
    field: 'status',
    component: 'JDictSelectTag',
    defaultValue: 1,
    componentProps: {
      dictCode: 'valid_status',
      placeholder: '请选择状态',
    },
  },
  {
    label: '备注',
    field: 'remark',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入备注信息',
      rows: 3,
    },
  },
]; 