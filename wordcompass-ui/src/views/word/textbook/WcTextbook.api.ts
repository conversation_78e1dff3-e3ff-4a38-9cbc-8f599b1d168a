import { defHttp } from '/@/utils/http/axios';
import { useMessage } from "/@/hooks/web/useMessage";

const { createConfirm } = useMessage();

enum Api {
  list = '/word/textbook/list',
  save = '/word/textbook/add',
  edit = '/word/textbook/edit',
  deleteOne = '/word/textbook/delete',
  deleteBatch = '/word/textbook/deleteBatch',
  importExcel = '/word/textbook/importExcel',
  exportXls = '/word/textbook/exportXls',
  listByGrade = '/word/textbook/listByGrade',
  listByGradeAndVersion = '/word/textbook/listByGradeAndVersion',
}

/**
 * 导出api
 */
export const getExportUrl = Api.exportXls;

/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;

/**
 * 列表接口
 */
export const list = (params) => defHttp.get({ url: Api.list, params });

/**
 * 删除单个
 */
export const deleteOne = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.deleteOne, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 批量删除
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.deleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    }
  });
};

/**
 * 保存或者更新
 */
export const saveOrUpdate = (params, isUpdate) => {
  const url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params }, { isTransformResponse: false });
};

/**
 * 根据年级查询教材列表
 */
export const listByGrade = (params) => defHttp.get({ url: Api.listByGrade, params });

/**
 * 根据年级和版本查询教材列表
 */
export const listByGradeAndVersion = (params) => defHttp.get({ url: Api.listByGradeAndVersion, params }); 