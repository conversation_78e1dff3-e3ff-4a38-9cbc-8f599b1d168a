import { defHttp } from '/@/utils/http/axios';
import { useMessage } from "/@/hooks/web/useMessage";

const { createConfirm } = useMessage();

enum Api {
  list = '/word/textbook/list',
  save = '/word/textbook/add',
  edit = '/word/textbook/edit',
  deleteOne = '/word/textbook/delete',
  deleteBatch = '/word/textbook/deleteBatch',
  listByGrade = '/word/textbook/listByGrade',
  listByGradeAndVersion = '/word/textbook/listByGradeAndVersion',
  // 教材明细相关API
  detailTree = '/word/textbook/detail/tree',
  detailList = '/word/textbook/detail/list',
  detailListUnit = '/word/textbook/detail/listUnit',
  detailListChapter = '/word/textbook/detail/listChapter',
  detailSave = '/word/textbook/detail/save',
  detailUpdate = '/word/textbook/detail/update',
  detailDelete = '/word/textbook/detail/delete',
  detailDetail = '/word/textbook/detail/detail',
}



/**
 * 列表接口
 */
export const list = (params) => defHttp.get({ url: Api.list, params });

/**
 * 删除单个
 */
export const deleteOne = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.deleteOne, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 批量删除
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.deleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    }
  });
};

/**
 * 保存或者更新
 */
export const saveOrUpdate = (params, isUpdate) => {
  const url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params }, { isTransformResponse: false });
};

/**
 * 根据年级查询教材列表
 */
export const listByGrade = (params) => defHttp.get({ url: Api.listByGrade, params });

/**
 * 根据年级和版本查询教材列表
 */
export const listByGradeAndVersion = (params) => defHttp.get({ url: Api.listByGradeAndVersion, params });

// ==================== 教材明细相关API ====================

/**
 * 获取教材树形结构
 */
export const getDetailTree = (params) => defHttp.get({ url: Api.detailTree, params });

/**
 * 获取教材明细列表
 */
export const getDetailList = (params) => defHttp.get({ url: Api.detailList, params });

/**
 * 获取教材单元列表
 */
export const getDetailListUnit = (params) => defHttp.get({ url: Api.detailListUnit, params });


/**
 * 获取教材章节列表
 */
export const getDetailListChapter = (params) => defHttp.get({ url: Api.detailListChapter, params });




/**
 * 保存教材明细
 */
export const saveDetail = (params) => defHttp.post({ url: Api.detailSave, params });

/**
 * 更新教材明细
 */
export const updateDetail = (params) => defHttp.post({ url: Api.detailUpdate, params });

/**
 * 删除教材明细
 */
export const deleteDetail = (params) => defHttp.delete({ url: Api.detailDelete, params },{ joinParamsToUrl: true });

/**
 * 获取教材明细详情
 */
export const getDetailById = (params) => defHttp.get({ url: Api.detailDetail, params }); 
