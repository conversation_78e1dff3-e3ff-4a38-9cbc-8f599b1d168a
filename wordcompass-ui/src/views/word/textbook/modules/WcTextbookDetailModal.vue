<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    :title="getTitle"
    :width="1200"
    @ok="handleSubmit"
  >
    <div class="textbook-detail-container">
      <!-- 左侧树形结构 -->
      <div class="tree-container">
        <div class="tree-header">
          <h3>教材结构</h3>
          <a-button type="primary" size="small" @click="handleAddRoot">
            添加单元
          </a-button>
        </div>
        <a-tree
          :tree-data="treeData"
          :selected-keys="selectedKeys"
          :expanded-keys="expandedKeys"
          @select="handleSelect"
          @expand="handleExpand"
        >
          <template #title="{ title, key, type }">
            <span class="tree-node">
              <span class="node-title">{{ title }}</span>
              <span class="node-actions">
                <a-button type="link" size="small" @click.stop="handleAdd(key, type)">
                  添加
                </a-button>
                <a-button type="link" size="small" @click.stop="handleEdit(key)">
                  编辑
                </a-button>
                <a-button type="link" size="small" @click.stop="handleDelete(key)" danger>
                  删除
                </a-button>
              </span>
            </span>
          </template>
        </a-tree>
      </div>

      <!-- 右侧编辑区域 -->
      <div class="edit-container">
        <div class="edit-header">
          <h3>{{ editTitle }}</h3>
        </div>
        <div class="edit-content" v-if="currentDetail">
          <a-form
            ref="formRef"
            :model="formData"
            :rules="rules"
            layout="vertical"
          >
            <a-form-item label="名称" name="name">
              <a-input v-model:value="formData.name" placeholder="请输入名称" />
            </a-form-item>
            <a-form-item label="类型" name="type">
              <a-select v-model:value="formData.type" placeholder="请选择类型">
                <a-select-option value="unit">单元</a-select-option>
                <a-select-option value="chapter">章节</a-select-option>
                <a-select-option value="lesson">课文</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="内容" name="content">
              <a-textarea
                v-model:value="formData.content"
                placeholder="请输入内容"
                :rows="4"
              />
            </a-form-item>
            <a-form-item label="排序" name="sortOrder">
              <a-input-number
                v-model:value="formData.sortOrder"
                placeholder="请输入排序号"
                :min="0"
                style="width: 100%"
              />
            </a-form-item>
            <a-form-item label="状态" name="status">
              <a-select v-model:value="formData.status" placeholder="请选择状态">
                <a-select-option :value="1">启用</a-select-option>
                <a-select-option :value="0">禁用</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="备注" name="remark">
              <a-textarea
                v-model:value="formData.remark"
                placeholder="请输入备注"
                :rows="2"
              />
            </a-form-item>
          </a-form>
        </div>
        <div class="edit-empty" v-else>
          <a-empty description="请选择左侧节点进行编辑" />
        </div>
      </div>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, watch } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { useMessage } from '/@/hooks/web/useMessage';
import {
  getDetailTree,
  saveDetail,
  updateDetail,
  deleteDetail,
  getDetailById,
} from '../WcTextbook.api';

const { createMessage } = useMessage();

// 表单引用
const formRef = ref();

// 模态框注册
const [registerModal, { setModalProps, closeModal }] = useModalInner((data) => {
  setModalProps({ confirmLoading: false });
  if (data?.record) {
    currentTextbook.value = data.record;
    loadTreeData();
  }
});

// 当前教材
const currentTextbook = ref(null);

// 树形数据
const treeData = ref([]);
const selectedKeys = ref([]);
const expandedKeys = ref([]);

// 当前编辑的明细
const currentDetail = ref(null);
const isEdit = ref(false);

// 表单数据
const formData = reactive({
  id: '',
  textbookId: '',
  parentId: '',
  name: '',
  type: 'unit',
  content: '',
  sortOrder: 0,
  status: 1,
  remark: '',
});

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择类型', trigger: 'change' }],
  sortOrder: [{ required: true, message: '请输入排序号', trigger: 'blur' }],
};

// 计算属性
const getTitle = computed(() => {
  return `维护教材明细 - ${currentTextbook.value?.textbookName || ''}`;
});

const editTitle = computed(() => {
  if (!currentDetail.value) return '编辑区域';
  return isEdit.value ? '编辑节点' : '新增节点';
});

// 加载树形数据
const loadTreeData = async () => {
  if (!currentTextbook.value?.id) return;
  
  try {
    const result = await getDetailTree({ textbookId: currentTextbook.value.id });
    treeData.value = buildTreeData(result);
  } catch (error) {
    createMessage.error('加载树形数据失败');
  }
};

// 构建树形数据
const buildTreeData = (data) => {
  return data.map(item => ({
    key: item.id,
    title: item.name,
    type: item.type,
    children: item.children ? buildTreeData(item.children) : [],
  }));
};

// 选择节点
const handleSelect = (keys, info) => {
  selectedKeys.value = keys;
  if (keys.length > 0) {
    loadDetailData(keys[0]);
  } else {
    currentDetail.value = null;
  }
};

// 展开节点
const handleExpand = (keys) => {
  expandedKeys.value = keys;
};

// 加载明细数据
const loadDetailData = async (id) => {
  try {
    const result = await getDetailById({ id });
    currentDetail.value = result;
    isEdit.value = true;
    Object.assign(formData, result);
  } catch (error) {
    createMessage.error('加载明细数据失败');
  }
};

// 添加根节点
const handleAddRoot = () => {
  resetForm();
  currentDetail.value = { id: '', type: 'unit' };
  isEdit.value = false;
  formData.textbookId = currentTextbook.value?.id;
  formData.parentId = '';
  selectedKeys.value = [];
};

// 添加子节点
const handleAdd = (parentKey, parentType) => {
  resetForm();
  currentDetail.value = { id: '', type: getNextType(parentType) };
  isEdit.value = false;
  formData.textbookId = currentTextbook.value?.id;
  formData.parentId = parentKey;
  selectedKeys.value = [];
};

// 获取下一个类型
const getNextType = (parentType) => {
  switch (parentType) {
    case 'unit': return 'chapter';
    case 'chapter': return 'lesson';
    default: return 'unit';
  }
};

// 编辑节点
const handleEdit = (key) => {
  selectedKeys.value = [key];
  loadDetailData(key);
};

// 删除节点
const handleDelete = async (key) => {
  try {
    await deleteDetail({ id: key });
    createMessage.success('删除成功');
    loadTreeData();
    if (selectedKeys.value.includes(key)) {
      currentDetail.value = null;
      selectedKeys.value = [];
    }
  } catch (error) {
    createMessage.error('删除失败');
  }
};

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    id: '',
    textbookId: '',
    parentId: '',
    name: '',
    type: 'unit',
    content: '',
    sortOrder: 0,
    status: 1,
    remark: '',
  });
  formRef.value?.resetFields();
};

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate();
    setModalProps({ confirmLoading: true });
    
    const api = isEdit.value ? updateDetail : saveDetail;
    await api(formData);
    
    createMessage.success(isEdit.value ? '更新成功' : '保存成功');
    loadTreeData();
    closeModal();
  } catch (error) {
    createMessage.error(isEdit.value ? '更新失败' : '保存失败');
  } finally {
    setModalProps({ confirmLoading: false });
  }
};
</script>

<style lang="less" scoped>
.textbook-detail-container {
  display: flex;
  height: 600px;
  gap: 16px;
}

.tree-container {
  width: 300px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
}

.tree-header {
  padding: 12px;
  border-bottom: 1px solid #d9d9d9;
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  h3 {
    margin: 0;
    font-size: 14px;
  }
}

.tree-container :deep(.ant-tree) {
  flex: 1;
  padding: 12px;
  overflow-y: auto;
}

.tree-node {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  
  .node-title {
    flex: 1;
  }
  
  .node-actions {
    display: none;
    gap: 4px;
  }
}

.tree-container :deep(.ant-tree-node-content-wrapper:hover) .node-actions {
  display: flex;
}

.edit-container {
  flex: 1;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
}

.edit-header {
  padding: 12px;
  border-bottom: 1px solid #d9d9d9;
  
  h3 {
    margin: 0;
    font-size: 14px;
  }
}

.edit-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.edit-empty {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style> 