# 四、小程序端功能设计

## 4.1登录/注册页面

- •注册

  1. 1.自动获取微信账号信息
  2. 2.填写手机号、验证短信
  3. 3.添加账号信息、微信用户信息
  4. 4.自动登录
  
- •自动登录

  1. 1.自动获取微信信息
  2. 2.如果 通过微信信息查询到 账号信息  则自动登录
  3. 3.否则  提示 需要绑定 手机号注册
  4. 4.填写手机号、验证短信
  5. 5.添加账号信息、微信用户信息
  6. 6.自动登录

## 4.2 首页

### 学生选择

- •添加学生

   1. 1.如果该账号下没有添加学生信息，则提示添加学生信息
  2. 2.添加学生信息
  
- •切换学生

  1. 1.默认选择第一个添加的学生信息
2. 2.可以切换学生信息

### 数据概览

- •总任务数量、任务完成数量、任务完成比例、错词数量
  1. 1.显示当前选择学生的 总任务数量
  2. 2.显示当前选择学生的 任务完成数量
  3. 3.显示当前选择学生的  完成比例
  4. 4.显示当前选择学生的  总错词数量

### 最近任务列表

- •倒序展示最近的发布的任务（显示当前选择学生的）

### 快捷操作

- •单词搜索框
  1. 1.根据 英文 汉字 搜索 英文单词
  2. 2.搜索结果 显示在 单词库 页面
- •创建任务 按钮
  1. 1.可以打开创建任务页面

## 4.3 任务页面

###  4.3.1 任务主页面

- •显示创建任务按钮

  1. 1.可以打开创建任务页面

- •任务筛选

  1. 1.可以按 标题、状态，进行筛选查询

- •任务列表

  1. 1.使用卡片展示任务的基本信息

  2. 2.根据任务的状态显示不同的操作按钮

     待做：开始听写 

     已做：批改操作、查看详情 

     已批改：查看详情

### 4.3.2 创建任务页面

#### 单词库展示

- •筛选选择框
  1. 1.可以根据单词名称、教材、分类、难易，进行筛选查询
- •随机添加按钮
  1. 1.可以快速的选择若干个单词
- •单词列表
  1. 1.已卡片列表形式展示单词信息
  2. 2.可以 点击 选择单词

#### 任务基本信息添加

- •标题
  1. 1.填写任务标题
- •已选单词列表
  1. 1.显示已经选择单词
  2. 2.可以清除已经选择的单词
  3. 3.可以打乱选择的单词
- •创建任务
  1. 1.创建任务

### 4.3.3 听写页面

- •任务基本信息
  1. 1.显示任务的基本信息 任务名称、单词数量、任务状态、创建时间
- •听写模块
  1. 1.显示单词进度
  2. 2.显示单词 中文
  3. 3.显示 开始  按钮 点击开始后，按顺序播音听写
  4. 4.显示 上一个 下一个，可手动切换 
  5. 5.完成听写 按钮，点击后 任务状态改变 为 已做，不能二次听写
- •单词列表
  1. 1.卡片展示单词信息
  2. 2.听写过程中可隐藏

### 4.3.4 批改页面

- •拍照上传自动批改
  1. 1.拍照或选择照片上传
- •手动批改
  1. 1.支持 手动批改
- •批改结果展示
  1. 1.显示单词列表，学生写的单词、对错状态按钮
  2. 2.显示 正确个数、错误个数、正确率、错误标记

### 4.3.5 详情页面

- •任务基本信息

  1. 1.显示任务基本信息

- •听写状态展示

  1. 1.待做

     提示： 没有听写 请开始听写 

     显示： 开始听写 按钮

  2. 2.已做

     提示： 听写完成 

     显示： 批改按钮

  3. 3.已批改

     显示： 批改结果

- •单词列表

  1. 1.以卡片的形式 展示单词列表

## 4.4 学习报告页面

###  任务完成情况

- •完成情况概览
  1. 1.任务总次数、完成次数、正确率、平均分、进步率
- •最近的任务
  1. 1.按时间倒序展示 最近的 任务

### 单词学习情况

- •可以查看整体学习情况
  1. 1.展示 总共听写单词数量、错误数量、错误率
- •可以根据教材、单元、章节 查看学习情况
  1. 1.可以选择 教材
  2. 2.根据选择的教材，展示该教材中的单词的学习情况，如 听写单词数量、错误数量、错误率
- •可以筛选出错误率高的单词列表
  1. 1.展示 出错率最高的单词  听写次数多  错误也多

## 4.5 单词库页面

- •按关键字查询
  1. 1.根据汉语查询
  2. 2.根据英文查询
- •条件筛选
  1. 1.按字母顺序
  2. 2.按 教材 单元  章节 进行筛选查询
- •单词详细展示
  1. 1.可以查看单词的基本信息
  2. 2.需要展示单词的溯源信息，出现的教材 单元

## 4.6 错词库页面

- •条件筛选
  1. 1.按字母顺序
  2. 2.按 教材 单元 章节 筛选查询  
  3. 3.按听写次数、错误次数 排序
- •单词详细展示
  1. 1.可以查看单词的基本信息
  2. 2.听写次数、错误次数
  3. 3.需要展示单词的溯源信息，出现的教材 单元