sourceSets {
    main {
        java {
            srcDirs = ['src/main/java']
        }
        resources {
            srcDirs = ['src/main/resources', 'src/main/java']
        }
    }
}

dependencies {


    api 'org.jeecgframework.boot:jeecg-boot-common:3.6.2'

    api 'org.springframework.boot:spring-boot-starter-web:2.7.10'
    api ('org.springframework.boot:spring-boot-starter-websocket:2.7.10')
    api 'org.springframework.boot:spring-boot-starter-mail:2.7.10'
    api 'org.springframework.boot:spring-boot-starter-aop:2.7.10'
    api 'org.springframework.boot:spring-boot-starter-actuator:2.7.10'
    api 'org.springframework.boot:spring-boot-starter-validation:2.7.10'
    api 'org.springframework.boot:spring-boot-starter-freemarker:2.7.10'
    api 'org.springframework.boot:spring-boot-starter-quartz:2.7.10'
    // 引入solr
    api 'org.springframework.boot:spring-boot-starter-data-solr:2.4.9'
    // ik 分词器
    api 'com.github.magese:ik-analyzer:8.5.0'

    //监控 Java 应用性能
    api 'io.micrometer:micrometer-registry-prometheus:1.9.9'
    api 'commons-io:commons-io:2.6'
    api 'commons-lang:commons-lang:2.6'

    api 'com.baomidou:mybatis-plus-boot-starter:3.5.3.1'
    api 'com.alibaba:druid-spring-boot-starter:1.2.19'
    api 'com.baomidou:dynamic-datasource-spring-boot-starter:4.1.3'

    api 'com.auth0:java-jwt:3.11.0'
    api 'org.apache.shiro:shiro-spring-boot-starter:1.12.0'

    api 'org.crazycake:shiro-redis:3.2.2'
    api 'com.github.xiaoymin:knife4j-spring-boot-starter:3.0.3'

    api 'org.jeecgframework.boot:codegenerate:1.4.4'
    api('org.jeecgframework:autopoi-web:1.4.7')
     { exclude group: 'xerces'  }



    //api 'xerces:xercesImpl:2.12.2'

    api 'io.minio:minio:8.0.3'
    api 'com.aliyun:aliyun-java-sdk-dysmsapi:2.1.0'
    api 'com.aliyun.oss:aliyun-sdk-oss:3.11.2'
    //实现第三方授权登录
    api 'com.xkcoding.justauth:justauth-spring-boot-starter:1.3.4'
    //  api 'com.squareup.okhttp3:okhttp:4.4.1'
    api 'com.fasterxml.jackson.module:jackson-module-kotlin:2.13.5'
    api 'commons-fileupload:commons-fileupload:1.5'

    api 'cn.hutool:hutool-all:5.8.23'

//    api 'mysql:mysql-connector-java:8.0.27'
    api 'mysql:mysql-connector-java:5.1.47'

    api("org.springframework.boot:spring-boot-starter-test")
}
