sourceSets {
    main {
        java {
            srcDirs = ['src/main/java']
        }
        resources {
            srcDirs = ['src/main/resources', 'src/main/java']
        }
    }
}
dependencies {
    api project(':common')
    api project(':sys-core')
    api fileTree(includes: ['*.jar'], dir: 'lib')

    //implementation 'io.github.mymonstercat:rapidocr-ncnn-platform:0.0.6'

    //一般只需要引入一个，CPU端建议使用onnx，移动端建议使用ncnn
    //可前往maven中央仓库/，查看版本
    //https://repo1.maven.org/maven2/io/github/mymonstercat/rapidocr

    //linux 与 windows 版本需要切换
    //implementation 'io.github.mymonstercat:rapidocr-onnx-platform:0.0.6'
    implementation("io.github.mymonstercat:rapidocr-onnx-linux-x86_64:0.0.6")
    
    // iText7 PDF处理库（用于添加水印）
    implementation 'com.itextpdf:itext7-core:7.2.5'
    implementation 'com.itextpdf:font-asian:7.2.5'


    //引入数据库版本管理工具:Flyway
    // api 'org.flywaydb:flyway-core:7.15.0'
}

