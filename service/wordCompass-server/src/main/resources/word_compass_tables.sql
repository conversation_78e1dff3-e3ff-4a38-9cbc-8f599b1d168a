-- =============================================
-- 单词指南针系统数据库建表脚本
-- 创建时间: 2025-01-15
-- 版本: V1.0
-- =============================================

-- 1. 教材基本信息表
CREATE TABLE `wc_textbook` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `textbook_name` varchar(200) NOT NULL COMMENT '教材名称',
  `version` varchar(50) DEFAULT NULL COMMENT '版本',
  `volume` varchar(20) DEFAULT NULL COMMENT '册别',
  `grade` varchar(20) NOT NULL COMMENT '年级',
  `publisher` varchar(100) DEFAULT NULL COMMENT '出版社',
  `sort_order` int DEFAULT 0 COMMENT '排序号',
  `status` tinyint DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `remark` text COMMENT '备注',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_grade` (`grade`),
  KEY `idx_version` (`version`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='教材基本信息表';

-- 2. 教材明细信息表
CREATE TABLE `wc_textbook_detail` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `textbook_id` varchar(32) NOT NULL COMMENT '教材ID',
  `parent_id` varchar(32) DEFAULT NULL COMMENT '父级ID',
  `name` varchar(200) NOT NULL COMMENT '名称',
  `type` varchar(20) NOT NULL COMMENT '类型：unit-单元，chapter-章节，lesson-课文',
  `content` text COMMENT '内容',
  `sort_order` int DEFAULT 0 COMMENT '排序号',
  `status` tinyint DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `remark` text COMMENT '备注',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_textbook_id` (`textbook_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_textbook_detail_textbook` FOREIGN KEY (`textbook_id`) REFERENCES `wc_textbook` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='教材明细信息表';

-- 3. 单词信息表
CREATE TABLE `wc_word` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `word_name` varchar(100) NOT NULL COMMENT '单词名称',
  `phonetic` varchar(100) DEFAULT NULL COMMENT '音标',
  `audio_file` varchar(500) DEFAULT NULL COMMENT '读音音频文件',
  `translation` text COMMENT '译义',
  `textbook_id` varchar(32) DEFAULT NULL COMMENT '所属教材ID',
  `unit_id` varchar(32) DEFAULT NULL COMMENT '所属单元ID',
  `chapter_id` varchar(32) DEFAULT NULL COMMENT '所属章节ID',
  `word_category` varchar(50) DEFAULT NULL COMMENT '单词分类',
  `difficulty_level` varchar(20) DEFAULT NULL COMMENT '难易程度',
  `example_sentence` text COMMENT '例句',
  `sort_order` int DEFAULT 0 COMMENT '排序号',
  `status` tinyint DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `remark` text COMMENT '备注',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_word_name` (`word_name`),
  KEY `idx_textbook_id` (`textbook_id`),
  KEY `idx_unit_id` (`unit_id`),
  KEY `idx_chapter_id` (`chapter_id`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_word_textbook` FOREIGN KEY (`textbook_id`) REFERENCES `wc_textbook` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_word_unit` FOREIGN KEY (`unit_id`) REFERENCES `wc_textbook_detail` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_word_chapter` FOREIGN KEY (`chapter_id`) REFERENCES `wc_textbook_detail` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='单词信息表';

-- 4. 错词信息表
CREATE TABLE `wc_wrong_word` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `account_id` varchar(32) NOT NULL COMMENT '账号ID',
  `student_id` varchar(32) NOT NULL COMMENT '学生ID',
  `word_id` varchar(32) NOT NULL COMMENT '单词ID',
  `word_name` varchar(100) NOT NULL COMMENT '单词名称',
  `dictation_count` int DEFAULT 0 COMMENT '听写次数',
  `wrong_count` int DEFAULT 0 COMMENT '错误次数',
  `mastery_status` varchar(20) DEFAULT 'not_mastered' COMMENT '掌握状态：not_mastered-未掌握，mastered-已掌握',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account_student_word` (`account_id`, `student_id`, `word_id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_student_id` (`student_id`),
  KEY `idx_word_id` (`word_id`),
  KEY `idx_mastery_status` (`mastery_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='错词信息表';

-- 5. 任务信息表
CREATE TABLE `wc_task` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `account_id` varchar(32) NOT NULL COMMENT '账号ID',
  `student_id` varchar(32) NOT NULL COMMENT '学生ID',
  `task_name` varchar(200) NOT NULL COMMENT '任务名称',
  `word_count` int DEFAULT 0 COMMENT '单词数量',
  `wrong_count` int DEFAULT 0 COMMENT '错误数量',
  `correct_count` int DEFAULT 0 COMMENT '正确数量',
  `wrong_rate` decimal(5,2) DEFAULT 0.00 COMMENT '错误率',
  `correct_rate` decimal(5,2) DEFAULT 0.00 COMMENT '正确率',
  `homework_photo` varchar(500) DEFAULT NULL COMMENT '作业照片',
  `task_type` varchar(20) NOT NULL COMMENT '任务类型：dictation-听写单词，translation-翻译汉语，recite_en-背诵英文，recite_cn-背诵汉语',
  `status` varchar(20) DEFAULT 'pending' COMMENT '状态：pending-待做，done-已做，corrected-已批改',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
  `correct_time` datetime DEFAULT NULL COMMENT '批改时间',
  `remark` text COMMENT '备注',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_student_id` (`student_id`),
  KEY `idx_task_type` (`task_type`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务信息表';

-- 6. 任务明细信息表
CREATE TABLE `wc_task_detail` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `account_id` varchar(32) NOT NULL COMMENT '账号ID',
  `student_id` varchar(32) NOT NULL COMMENT '学生ID',
  `task_id` varchar(32) NOT NULL COMMENT '任务ID',
  `word_id` varchar(32) NOT NULL COMMENT '单词ID',
  `word_name` varchar(100) NOT NULL COMMENT '单词名称',
  `student_answer` text COMMENT '学生写的内容',
  `is_correct` tinyint DEFAULT 0 COMMENT '是否正确：1-正确，0-错误',
  `correction_remark` text COMMENT '批改备注',
  `sort_order` int DEFAULT 0 COMMENT '排序号',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_word_id` (`word_id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_student_id` (`student_id`),
  CONSTRAINT `fk_task_detail_task` FOREIGN KEY (`task_id`) REFERENCES `wc_task` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_task_detail_word` FOREIGN KEY (`word_id`) REFERENCES `wc_word` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务明细信息表';

-- 7. 学生信息表
CREATE TABLE `wc_student` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `account_id` varchar(32) NOT NULL COMMENT '账号ID',
  `student_name` varchar(50) NOT NULL COMMENT '学生姓名',
  `grade` varchar(20) DEFAULT NULL COMMENT '年级',
  `school` varchar(100) DEFAULT NULL COMMENT '学校',
  `status` tinyint DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `remark` text COMMENT '备注',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_student_name` (`student_name`),
  KEY `idx_grade` (`grade`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='学生信息表';

-- 8. 账号信息表
CREATE TABLE `wc_account` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `phone` varchar(20) NOT NULL COMMENT '手机号',
  `password` varchar(100) DEFAULT NULL COMMENT '密码',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `gender` varchar(10) DEFAULT NULL COMMENT '性别',
  `avatar` varchar(500) DEFAULT NULL COMMENT '头像附件',
  `status` tinyint DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `remark` text COMMENT '备注',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_phone` (`phone`),
  KEY `idx_nickname` (`nickname`),
  KEY `idx_email` (`email`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='账号信息表';

-- 9. 微信用户信息表
CREATE TABLE `wc_wechat_user` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `wechat_nickname` varchar(100) DEFAULT NULL COMMENT '微信昵称',
  `openid` varchar(100) NOT NULL COMMENT '微信openid',
  `account_id` varchar(32) DEFAULT NULL COMMENT '关联的账号ID',
  `status` tinyint DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `remark` text COMMENT '备注',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_openid` (`openid`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_wechat_user_account` FOREIGN KEY (`account_id`) REFERENCES `wc_account` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信用户信息表';

-- =============================================
-- 插入初始数据
-- =============================================

-- 插入教材基本信息
INSERT INTO `wc_textbook` (`id`, `textbook_name`, `version`, `volume`, `grade`, `publisher`, `sort_order`, `status`, `remark`) VALUES
('TB001', '人教版英语', 'PEP', '上册', '三年级', '人民教育出版社', 1, 1, '人教版三年级上册英语教材'),
('TB002', '人教版英语', 'PEP', '下册', '三年级', '人民教育出版社', 2, 1, '人教版三年级下册英语教材'),
('TB003', '人教版英语', 'PEP', '上册', '四年级', '人民教育出版社', 3, 1, '人教版四年级上册英语教材'),
('TB004', '人教版英语', 'PEP', '下册', '四年级', '人民教育出版社', 4, 1, '人教版四年级下册英语教材'),
('TB005', '人教版英语', 'PEP', '上册', '五年级', '人民教育出版社', 5, 1, '人教版五年级上册英语教材');

-- 插入教材明细信息（三年级上册）
INSERT INTO `wc_textbook_detail` (`id`, `textbook_id`, `parent_id`, `name`, `type`, `content`, `sort_order`, `status`) VALUES
-- 单元1
('TD001', 'TB001', NULL, 'Unit 1 Hello!', 'unit', '第一单元：问候语', 1, 1),
('TD002', 'TB001', 'TD001', 'Part A Let\'s talk', 'chapter', 'A部分：对话', 1, 1),
('TD003', 'TB001', 'TD001', 'Part B Let\'s learn', 'chapter', 'B部分：学习', 2, 1),
('TD004', 'TB001', 'TD002', 'Lesson 1', 'lesson', '第一课：Hello, I\'m...', 1, 1),
('TD005', 'TB001', 'TD003', 'Lesson 2', 'lesson', '第二课：What\'s your name?', 2, 1),

-- 单元2
('TD006', 'TB001', NULL, 'Unit 2 Colours', 'unit', '第二单元：颜色', 2, 1),
('TD007', 'TB001', 'TD006', 'Part A Let\'s talk', 'chapter', 'A部分：对话', 1, 1),
('TD008', 'TB001', 'TD006', 'Part B Let\'s learn', 'chapter', 'B部分：学习', 2, 1),
('TD009', 'TB001', 'TD007', 'Lesson 1', 'lesson', '第一课：What colour is it?', 1, 1),
('TD010', 'TB001', 'TD008', 'Lesson 2', 'lesson', '第二课：I like red', 2, 1);

-- 插入单词信息
INSERT INTO `wc_word` (`id`, `word_name`, `phonetic`, `translation`, `textbook_id`, `unit_id`, `chapter_id`, `word_category`, `difficulty_level`, `example_sentence`, `sort_order`, `status`) VALUES
('W001', 'hello', '/həˈləʊ/', '你好', 'TB001', 'TD001', 'TD002', 'greeting', 'easy', 'Hello, I\'m Mike.', 1, 1),
('W002', 'hi', '/haɪ/', '嗨', 'TB001', 'TD001', 'TD002', 'greeting', 'easy', 'Hi, Sarah!', 2, 1),
('W003', 'name', '/neɪm/', '名字', 'TB001', 'TD001', 'TD003', 'noun', 'easy', 'What\'s your name?', 3, 1),
('W004', 'red', '/red/', '红色', 'TB001', 'TD006', 'TD007', 'colour', 'easy', 'I like red.', 4, 1),
('W005', 'blue', '/bluː/', '蓝色', 'TB001', 'TD006', 'TD007', 'colour', 'easy', 'The sky is blue.', 5, 1),
('W006', 'yellow', '/ˈjeləʊ/', '黄色', 'TB001', 'TD006', 'TD008', 'colour', 'easy', 'The sun is yellow.', 6, 1),
('W007', 'green', '/ɡriːn/', '绿色', 'TB001', 'TD006', 'TD008', 'colour', 'easy', 'The grass is green.', 7, 1);

-- 插入账号信息
INSERT INTO `wc_account` (`id`, `nickname`, `phone`, `password`, `email`, `gender`, `status`) VALUES
('ACC001', '张老师', '***********', 'e10adc3949ba59abbe56e057f20f883e', '<EMAIL>', '女', 1),
('ACC002', '李老师', '***********', 'e10adc3949ba59abbe56e057f20f883e', '<EMAIL>', '男', 1);

-- 插入学生信息
INSERT INTO `wc_student` (`id`, `account_id`, `student_name`, `grade`, `school`, `status`) VALUES
('STU001', 'ACC001', '小明', '三年级', '实验小学', 1),
('STU002', 'ACC001', '小红', '三年级', '实验小学', 1),
('STU003', 'ACC002', '小华', '四年级', '第一小学', 1);

-- 插入任务信息
INSERT INTO `wc_task` (`id`, `account_id`, `student_id`, `task_name`, `word_count`, `wrong_count`, `correct_count`, `wrong_rate`, `correct_rate`, `task_type`, `status`, `create_time`) VALUES
('TASK001', 'ACC001', 'STU001', 'Unit 1 听写任务', 3, 1, 2, 33.33, 66.67, 'dictation', 'corrected', '2025-01-15 10:00:00'),
('TASK002', 'ACC001', 'STU002', 'Unit 2 听写任务', 4, 0, 4, 0.00, 100.00, 'dictation', 'corrected', '2025-01-15 14:00:00');

-- 插入任务明细信息
INSERT INTO `wc_task_detail` (`id`, `account_id`, `student_id`, `task_id`, `word_id`, `word_name`, `student_answer`, `is_correct`, `correction_remark`, `sort_order`) VALUES
('TD001', 'ACC001', 'STU001', 'TASK001', 'W001', 'hello', 'hello', 1, '正确', 1),
('TD002', 'ACC001', 'STU001', 'TASK001', 'W002', 'hi', 'hi', 1, '正确', 2),
('TD003', 'ACC001', 'STU001', 'TASK001', 'W003', 'name', 'neme', 0, '拼写错误，应该是name', 3);

-- 插入错词信息
INSERT INTO `wc_wrong_word` (`id`, `account_id`, `student_id`, `word_id`, `word_name`, `dictation_count`, `wrong_count`, `mastery_status`) VALUES
('WW001', 'ACC001', 'STU001', 'W003', 'name', 1, 1, 'not_mastered'); 