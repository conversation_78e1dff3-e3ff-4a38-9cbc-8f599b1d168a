<#assign deviceTypeName=StaticFun.getDEVICE_TYPE(task.deviceType)>
<#assign planChinese=CornFun.toChinese(task.plan)>
{
  "评估方案名称": "${(task.taskName)!}",
  "评估方案代码": "${(task.taskCode)!}",
  "评估方案描述": "${(task.description!)!}",
  "调度计划": "${(planChinese)!}",
  "设备类型": "${deviceTypeName}",
  "期望完成时间(秒)": "${(task.expireTime)!}",
  "评估项列表": [
    <#list task.defItemList as defItem>
    {
      "评估项名称": "${(defItem.itemName)!}",
      "评估项代码": "${(defItem.itemCode)!}",
      "定义默认权重": "${(defItem.weight!)!}",
      "配置权重": "${(defItem.configWeight!)!}",
      "评估项描述": "${(defItem.description!)!}",
      "评估用例列表": [
        <#list defItem.defCaseList as defCase>
        {
          "评估用例名称": "${(defCase.caseName)!}",
          "评估用例代码": "${(defCase.caseCode)!}",
          "定义默认权重": "${(defCase.weight!)!}",
          "配置权重": "${(defCase.configWeight!)!}",
          "评估用例描述": "${(defCase.description!)!}",
          "评估指令发送列表": [
            <#list defCase.defCaseInstructs as defCaseInstruct>
            {
              "设备指令": "${(defCaseInstruct.deviceInstructName)!}",
              "数据文件路径": "${(defCaseInstruct.filePath!)!}",
              "指令内容": "${(defCaseInstruct.instructContext!)!}"
            },
            </#list>
          ]
        },
        </#list>
      ]
    },
      </#list>

    
  ]
}