server:
  port: 8080
  tomcat:
    connection-timeout: 600000  # 连接超时时间（毫秒）
    max-connections: 10000     # 最大连接数
    accept-count: 100          # 等待队列长度
    keep-alive-timeout: 600     # keep-alive 超时时间（秒）
    max-swallow-size: -1
    # 增加JVM参数配置
    jvm-options:
      - "-XX:ReservedCodeCacheSize=128m"  # 增加CodeCache大小到128MB
      - "-XX:InitialCodeCacheSize=32m"    # 初始CodeCache大小
      - "-XX:MaxDirectMemorySize=512m"    # 增加直接内存大小
      - "-XX:+UseG1GC"                   # 使用G1垃圾收集器优化内存管理
  error:
    include-exception: true
    include-stacktrace: ALWAYS
    include-message: ALWAYS
  servlet:
    context-path: /wordCompassApi
  compression:
    enabled: true
    min-response-size: 1024
    mime-types: application/javascript,application/json,application/xml,text/html,text/xml,text/plain,text/css,image/*

management:
  endpoints:
    web:
      exposure:
        include: metrics,httptrace

spring:
  # flyway配置
  flyway:
    # 是否启用flyway
    enabled: true
    # 编码格式，默认UTF-8
    encoding: UTF-8
    # 迁移sql脚本文件存放路径，官方默认db/migration
    locations: classpath:flyway/sql/mysql
    # 迁移sql脚本文件名称的前缀，默认V
    sql-migration-prefix: V
    # 迁移sql脚本文件名称的分隔符，默认2个下划线__
    sql-migration-separator: __
    # 避免带${}sql执行失败
    placeholder-prefix: '#('
    placeholder-suffix: )
    # 迁移sql脚本文件名称的后缀
    sql-migration-suffixes: .sql
    # 迁移时是否进行校验，默认true
    validate-on-migrate: true
    # 当迁移发现数据库非空且存在没有元数据的表时，自动执行基准迁移，新建schema_version表
    baseline-on-migrate: true
    # 是否关闭要清除已有库下的表功能,生产环境必须为true,否则会删库，非常重要！！！
    clean-disabled: true
  servlet:
    multipart:
      max-file-size: 2000MB
      max-request-size: 2000MB
  mail:
    host: smtp.163.com
    username: <EMAIL>
    password: ??
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
  ## quartz定时任务,采用数据库方式
  quartz:
    job-store-type: jdbc
    initialize-schema: embedded
    #定时任务启动开关，true-开  false-关
    auto-startup: true
    #延迟1秒启动定时任务
    startup-delay: 1s
    #启动时更新己存在的Job
    overwrite-existing-jobs: true
    properties:
      org:
        quartz:
          scheduler:
            instanceName: MyScheduler
            instanceId: AUTO
          jobStore:
            class: org.springframework.scheduling.quartz.LocalDataSourceJobStore
            driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate
            tablePrefix: QRTZ_
            isClustered: true
            misfireThreshold: 12000
            clusterCheckinInterval: 15000
          threadPool:
            class: org.quartz.simpl.SimpleThreadPool
            threadCount: 10
            threadPriority: 5
            threadsInheritContextClassLoaderOfInitializingThread: true
  #json 时间戳统一转换
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  jpa:
    open-in-view: false
  aop:
    proxy-target-class: true
  #配置freemarker
  freemarker:
    # 设置模板后缀名
    suffix: .ftl
    # 设置文档类型
    content-type: text/html
    # 设置页面编码格式
    charset: UTF-8
    # 设置页面缓存
    cache: false
    prefer-file-system-access: false
    # 设置ftl文件路径
    template-loader-path:
      - classpath:/templates
    template_update_delay: 0
  # 设置静态文件路径，js,css等
  mvc:
    static-path-pattern: /**
    #Spring Boot 2.6+后映射匹配的默认策略已从AntPathMatcher更改为PathPatternParser,需要手动指定为ant-path-matcher
    pathmatch:
      matching-strategy: ant_path_matcher
  resource:
    static-locations: classpath:/static/,classpath:/public/
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  datasource:
    druid:
      stat-view-servlet:
        enabled: true
        loginUsername: admin
        loginPassword: 123456
        allow:
      web-stat-filter:
        enabled: true
    dynamic:
      druid: # 全局druid参数，绝大部分值和默认保持一致。(现已支持的参数如下,不清楚含义不要乱设置)
        # 连接池的配置信息
        # 初始化大小，最小，最大
        initial-size: 5
        min-idle: 5
        maxActive: 1000
        # 配置获取连接等待超时的时间
        maxWait: 60000
        # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        timeBetweenEvictionRunsMillis: 60000
        # 配置一个连接在池中最小生存的时间，单位是毫秒
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        useGlobalDataSourceStat: true
        # 打开PSCache，并且指定每个连接上PSCache的大小
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
        filters: stat,wall,slf4j
        # 打开mergeSql功能；慢SQL记录
        stat:
          merge-sql: true
          slow-sql-millis: 5000
      datasource:
        master:
          url: ******************************************************************************************************************************************************************************
          username: root
          password: yf560117@YF
          driver-class-name: com.mysql.jdbc.Driver
  #redis 配置
  redis:
    database: 0
    host: 127.0.0.1
    port: 6379
    password: ''
  task:
    scheduling:
      pool:
        size: 10
      thread-name-prefix: wordCompass-scheduling-

#mybatis plus 设置
mybatis-plus:
  mapper-locations: classpath*:org/jeecg/modules/**/xml/*Mapper.xml
  global-config:
    # 关闭MP3.0自带的banner
    banner: false
    db-config:
      #主键类型  0:"数据库ID自增",1:"该类型为未设置主键类型", 2:"用户输入ID",3:"全局唯一ID (数字类型唯一ID)", 4:"全局唯一ID UUID",5:"字符串全局唯一ID (idWorker 的字符串表示)";
      id-type: ASSIGN_ID
      # 默认数据库表下划线命名
      table-underline: true
  configuration:
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    # log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 返回类型为Map,显示null对应的字段
    call-setters-on-nulls: true
#jeecg专用配置
minidao:
  base-package: org.jeecg.modules.jmreport.*,org.jeecg.modules.drag.*
jeecg:
  # 平台上线安全配置
  firewall:
    # 数据源安全 (开启后，Online报表和图表的数据源为必填)
    dataSourceSafe: false
    # 低代码模式（dev:开发模式，prod:发布模式——关闭所有在线开发配置能力）
    lowCodeMode: dev
  # 签名密钥串(前后端要一致，正式发布请自行修改)
  signatureSecret: dd05f1c54d63749eda95f9fa6d49v442a
  #签名拦截接口
  signUrls: /sys/dict/getDictItems/*,/sys/dict/loadDict/*,/sys/dict/loadDictOrderByValue/*,/sys/dict/loadDictItem/*,/sys/dict/loadTreeData,/sys/api/queryTableDictItemsByCode,/sys/api/queryFilterTableDictInfo,/sys/api/queryTableDictByKeys,/sys/api/translateDictFromTable,/sys/api/translateDictFromTableByKeys
  # 本地：local、Minio：minio、阿里云：alioss
  uploadType: local
  # 前端访问地址
  domainUrl:
    pc: http://localhost:3100
    app: http://localhost:8051
  path:
    #文件上传根目录 设置
    upload: /opt/module/app/wordcompass/uploadFile/
    #文件上传临时目录 设置
    uploadTmp: /opt/module/app/wordcompass/uploadFile/tmp/
    uploadAvatar: /opt/module/app/wordcompass/avatarFile/


  # 图片访问路径
  image-path: http://**************/wordCompassApi/word/api/annex/loadPic?picPath=

  # 静态资源访问路径
  static-path: http://**************/wordCompassApi/sys/common/static/


  shiro:
    excludeUrls: /test/jeecgDemo/demo3,/test/jeecgDemo/redisDemo/**,/jmreport/bigscreen2/**

  # 在线预览文件服务器地址配置
  file-view-domain: http://fileview.jeecg.com

  #分布式锁配置
  redisson:
    address: 127.0.0.1:6379
    password:
    type: STANDALONE
    enabled: true
#cas单点登录
cas:
  prefixUrl: http://cas.example.org:8443/cas
#Mybatis输出sql日志
logging:
  level:
    org.flywaydb: debug
    org.jeecg.modules.system.mapper: info
    org.jeecg.common.aspect: debug
    org.jeecg.modules: debug
#swagger
knife4j:
  #开启增强配置
  enable: true
  #开启生产环境屏蔽
  production: false
  basic:
    enable: false
    username: api
    password: api123456

execute:
  case: 10

wechat:
  miniprogram:
    appid: wxa84e0ed078e5421b
    secret: 2a7509ae250ee3ff9ca5bcc6a592af44
    # appid: wx2a813c41f46684d2
    # secret: f6991be73eed9870fc721a5e195f8ae8

aliyun:
  bailian:
    secretKey: sk-731524088297435091f188cbe5082c43
    model-name: qwen-vl-max
  prompt-parseImageAutoCorrect: |
    # 你现在是一个英语老师，你的任务是批改学生听写的单词
    # 【作业列表】:
       {taskDetailList}
    # 【任务】:需要解析图片上的学生回答，根据解析的内容，来批改【作业列表】
    # 【要求】:
       1、【作业列表】中的数据为 单词听写任务列表，上传的图片为 学生听写的内容。
       2、 解析  上传的图片中 学生写的单词。
       3、 根据 【作业列表】的内容，对学生写的单词进行批改，student_answer 字段 填写学生写的内容，is_correct 字段 为 学生听写是否正确。
       4、 将【作业列表】  student_answer 、is_correct 字段，都为字符串类型，内容需要补充完整。
    # 【输出格式】:
       1、 请你以JSON数据 格式输出，不要输出```json```代码段，直接输出JSON数组字符串