package org.jeecg.modules.word.mapper;

import org.jeecg.modules.word.entity.WcStudent;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * @Description: 学生信息
 * @Author: wordCompass
 * @Date: 2025-01-15
 * @Version: V1.0
 */
public interface WcStudentMapper extends BaseMapper<WcStudent> {
    
    /**
     * 根据账号ID查询学生列表
     * @param accountId 账号ID
     * @return 学生列表
     */
    List<WcStudent> selectByAccountId(@Param("accountId") String accountId);
    
    /**
     * 根据年级查询学生列表
     * @param grade 年级
     * @return 学生列表
     */
    List<WcStudent> selectByGrade(@Param("grade") String grade);
    
} 