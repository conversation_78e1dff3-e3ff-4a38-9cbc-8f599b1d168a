package org.jeecg.modules.common;

import com.aspose.slides.*;

public class PptFormattingController implements IHtmlFormattingController {
    @Override
    public void writeDocumentStart(IHtmlGenerator generator, IPresentation presentation) {
    }

    @Override
    public void writeDocumentEnd(IHtmlGenerator generator, IPresentation presentation) {
    }

    @Override
    public void writeSlideStart(IHtmlGenerator generator, ISlide slide) {
        generator.addHtml(String.format(SlideHeader, generator.getSlideIndex() + 1));
    }

    @Override
    public void writeSlideEnd(IHtmlGenerator generator, ISlide slide) {
        generator.addHtml(SlideFooter);
    }

    @Override
    public void writeShapeStart(IHtmlGenerator generator, IShape shape) {
    }

    @Override
    public void writeShapeEnd(IHtmlGenerator generator, IShape shape) {
    }

    private final String SlideHeader = "<div class=\"slide\" name=\"slide\" id=\"slide%d\">";
    private final String SlideFooter = "</div>";
}
