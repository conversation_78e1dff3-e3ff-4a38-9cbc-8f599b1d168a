package org.jeecg.modules.word.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.system.base.entity.JeecgEntity;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * @Description: 微信用户信息
 * @Author: wordCompass
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="WcWechatUser对象", description="微信用户信息")
@TableName("wc_wechat_user")
public class WcWechatUser extends JeecgEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 微信昵称
     */
    @Excel(name = "微信昵称", width = 30)
    @ApiModelProperty(value = "微信昵称")
    private String wechatNickname;

    /**
     * 微信OpenID
     */
    @Excel(name = "OpenID", width = 30)
    @ApiModelProperty(value = "微信OpenID")
    private String openid;

    /**
     * 微信UnionID
     */
    @ApiModelProperty(value = "微信UnionID")
    private String unionid;

    /**
     * 关联的账号ID
     */
    @Excel(name = "关联账号ID", width = 20)
    @ApiModelProperty(value = "关联的账号ID")
    private String accountId;

    /**
     * 微信头像
     */
    @ApiModelProperty(value = "微信头像")
    private String wechatAvatar;

    /**
     * 性别(0-未知,1-男,2-女)
     */
    @Excel(name = "性别", width = 10, dicCode = "wechat_gender")
    @ApiModelProperty(value = "性别")
    private Integer gender;

    /**
     * 国家
     */
    @Excel(name = "国家", width = 15)
    @ApiModelProperty(value = "国家")
    private String country;

    /**
     * 省份
     */
    @Excel(name = "省份", width = 15)
    @ApiModelProperty(value = "省份")
    private String province;

    /**
     * 城市
     */
    @Excel(name = "城市", width = 15)
    @ApiModelProperty(value = "城市")
    private String city;

    /**
     * 语言
     */
    @ApiModelProperty(value = "语言")
    private String language;

    /**
     * 最后登录时间
     */
    @Excel(name = "最后登录时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最后登录时间")
    private Date lastLoginTime;

    /**
     * 状态(0-禁用,1-启用)
     */
    @Excel(name = "状态", width = 10, dicCode = "valid_status")
    @ApiModelProperty(value = "状态")
    private Integer status;

    /**
     * 关注状态(0-未关注,1-已关注)
     */
    @Excel(name = "关注状态", width = 10, dicCode = "subscribe_status")
    @ApiModelProperty(value = "关注状态")
    private Integer subscribeStatus;

    /**
     * 关注时间
     */
    @Excel(name = "关注时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "关注时间")
    private Date subscribeTime;

    /**
     * 备注
     */
    @Excel(name = "备注", width = 30)
    @ApiModelProperty(value = "备注")
    private String remark;
} 