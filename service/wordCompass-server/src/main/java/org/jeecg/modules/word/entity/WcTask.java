package org.jeecg.modules.word.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.system.base.entity.JeecgEntity;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * @Description: 任务信息
 * @Author: wordCompass
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="WcTask对象", description="任务信息")
@TableName("wc_task")
public class WcTask extends JeecgEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 账号ID
     */
    @Excel(name = "账号ID", width = 20)
    @ApiModelProperty(value = "账号ID")
    private String accountId;

    /**
     * 学生ID
     */
    @Excel(name = "学生ID", width = 20)
    @ApiModelProperty(value = "学生ID")
    private String studentId;

    /**
     * 任务名称
     */
    @Excel(name = "任务名称", width = 30)
    @ApiModelProperty(value = "任务名称")
    private String taskName;

    /**
     * 任务描述
     */
    @Excel(name = "任务描述", width = 50)
    @ApiModelProperty(value = "任务描述")
    private String taskDescription;

    /**
     * 单词数量
     */
    @Excel(name = "单词数量", width = 10)
    @ApiModelProperty(value = "单词数量")
    private Integer wordCount;

    /**
     * 错误数量
     */
    @Excel(name = "错误数量", width = 10)
    @ApiModelProperty(value = "错误数量")
    private Integer errorCount;

    /**
     * 正确数量
     */
    @Excel(name = "正确数量", width = 10)
    @ApiModelProperty(value = "正确数量")
    private Integer correctCount;

    /**
     * 错误率
     */
    @Excel(name = "错误率", width = 10)
    @ApiModelProperty(value = "错误率")
    private Double errorRate;

    /**
     * 正确率
     */
    @Excel(name = "正确率", width = 10)
    @ApiModelProperty(value = "正确率")
    private Double correctRate;

    /**
     * 作业照片附件
     */
    @ApiModelProperty(value = "作业照片附件")
    private String homeworkPhoto;

    /**
     * 完成时间
     */
    @Excel(name = "完成时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "完成时间")
    private Date finishTime;

    /**
     * 批改时间
     */
    @Excel(name = "批改时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "批改时间")
    private Date correctionTime;

    /**
     * 任务类型(dictation-听写单词,translation-翻译汉语,recite_english-背诵英文,recite_chinese-背诵汉语)
     */
    @Excel(name = "任务类型", width = 15, dicCode = "task_type")
    @ApiModelProperty(value = "任务类型")
    private String taskType;

    /**
     * 状态(pending-待做,finished-已做,corrected-已批改)
     */
    @Excel(name = "状态", width = 10, dicCode = "task_status")
    @ApiModelProperty(value = "状态")
    private String status;

    /**
     * 所属教材ID
     */
    @Excel(name = "所属教材ID", width = 20)
    @ApiModelProperty(value = "所属教材ID")
    private String textbookId;

    /**
     * 所属单元ID
     */
    @Excel(name = "所属单元ID", width = 20)
    @ApiModelProperty(value = "所属单元ID")
    private String unitId;

    /**
     * 所属章节ID
     */
    @Excel(name = "所属章节ID", width = 20)
    @ApiModelProperty(value = "所属章节ID")
    private String chapterId;

    /**
     * 备注
     */
    @Excel(name = "备注", width = 30)
    @ApiModelProperty(value = "备注")
    private String remark;
} 