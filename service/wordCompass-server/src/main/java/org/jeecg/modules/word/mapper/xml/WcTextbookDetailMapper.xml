<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.word.mapper.WcTextbookDetailMapper">

    <!-- 根据教材ID查询明细列表 -->
    <select id="selectByTextbookId" resultType="org.jeecg.modules.word.entity.WcTextbookDetail">
        SELECT * FROM wc_textbook_detail 
        WHERE textbook_id = #{textbookId} 
        AND del_flag = 0
        ORDER BY sort_order ASC, create_time ASC
    </select>
    
    <!-- 根据父级ID查询子明细列表 -->
    <select id="selectByParentId" resultType="org.jeecg.modules.word.entity.WcTextbookDetail">
        SELECT * FROM wc_textbook_detail 
        WHERE parent_id = #{parentId} 
        AND del_flag = 0
        ORDER BY sort_order ASC, create_time ASC
    </select>
    
    <!-- 查询树形结构数据 -->
    <select id="selectTreeData" resultType="org.jeecg.modules.word.entity.WcTextbookDetail">
        SELECT * FROM wc_textbook_detail 
        WHERE textbook_id = #{textbookId} 
        AND del_flag = 0
        ORDER BY level ASC, sort_order ASC, create_time ASC
    </select>

</mapper>
