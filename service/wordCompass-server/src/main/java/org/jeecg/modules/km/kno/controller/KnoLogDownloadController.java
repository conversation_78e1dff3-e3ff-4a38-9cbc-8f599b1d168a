package org.jeecg.modules.km.kno.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.km.kno.entity.KnoLogDownload;
import org.jeecg.modules.km.kno.service.KnoLogDownloadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.jeecg.modules.km.kno.vo.KnoLogDownloadVo;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;


@Slf4j
@Api(tags="知识下载")
@RestController
@RequestMapping("/kno/knoLogDownload")
public class KnoLogDownloadController {
	@Autowired
	private KnoLogDownloadService knoLogDownloadService;

	@ApiOperation("知识下载-分页列表查询")
	@PostMapping(value = "/list")
	public Result<IPage<KnoLogDownloadVo>> queryPageList(@RequestBody KnoLogDownloadVo knoLogDownload,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		Page<KnoLogDownloadVo> page = new Page<KnoLogDownloadVo>(pageNo, pageSize);
		IPage<KnoLogDownloadVo> pageList = knoLogDownloadService.findKnoLogDownloadByPage(page, knoLogDownload);
       return Result.OK(pageList);
	}



	@ApiOperation("知识下载-分页列表查询")
	@PostMapping(value = "/listmap")
	public Result<IPage<KnoLogDownload>> listmap(@RequestBody Map<String, Object> paramMap) {

        Object pageNo = paramMap.get("pageNo");
        Object pageSize = paramMap.get("pageSize");
        if (pageNo == null) pageNo = "1";
        if (pageSize == null) pageSize = "10";
        QueryWrapper<KnoLogDownload> queryWrapper = new QueryWrapper<>();
        if (paramMap.get("code") != null) {
            queryWrapper.like("CODE", paramMap.get("code"));
        }
        queryWrapper.orderByDesc("CREATE_DATE");
     Page<KnoLogDownload> page = new Page<KnoLogDownload>(Long.getLong(pageNo.toString()), Long.getLong(pageSize.toString()));
	 IPage<KnoLogDownload> pageList = knoLogDownloadService.findKnoLogDownloads(page,queryWrapper);
       return Result.OK(pageList);
	}



	@ApiOperation("知识下载-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody KnoLogDownload knoLogDownload) {
		knoLogDownloadService.saveOrUpdateKnoLogDownload(knoLogDownload);
		return Result.OK("添加成功！");
	}

	@ApiOperation("知识下载-编辑")
	@PostMapping(value = "/edit")
	public Result<String> edit(@RequestBody KnoLogDownload knoLogDownload) {
		knoLogDownloadService.saveOrUpdateKnoLogDownload(knoLogDownload);
		return Result.OK("编辑成功!");
	}

	@ApiOperation("知识下载-通过id删除")
	@PostMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		knoLogDownloadService.deleteKnoLogDownload(id);
		return Result.OK("删除成功!");
	}

	@ApiOperation("知识下载-批量删除")
	@PostMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.knoLogDownloadService.deleteKnoLogDownloadByIds(ids);
		return Result.OK("批量删除成功！");
	}


	@ApiOperation("知识下载-通过id查询")
	@PostMapping(value = "/queryById")
	public Result<KnoLogDownload> queryById(@RequestParam(name="id",required=true) String id) {
		KnoLogDownload knoLogDownload = knoLogDownloadService.findKnoLogDownloadById(id);
		return Result.OK(knoLogDownload);
	}

}
