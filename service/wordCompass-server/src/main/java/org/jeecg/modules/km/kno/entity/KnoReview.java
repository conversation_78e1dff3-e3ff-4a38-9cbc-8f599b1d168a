package org.jeecg.modules.km.kno.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecg.modules.common.AppConstant;
import org.springframework.format.annotation.DateTimeFormat;



@Data
@TableName("kno_review")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel("知识审核")
public class KnoReview {

	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键ID")
	private String id;

    @ApiModelProperty("知识ID")
	private String knowledgeId;

    @ApiModelProperty("审核状态：0-待审核，1-审核通过，2-审核不通过")
	@Dict(dictClass = AppConstant.REVIEW_STATUS.class)
	private Integer reviewStatus;

    @ApiModelProperty("审核意见")
	private Object reviewComment;

    @ApiModelProperty("审核人名称")
	private String reviewerName;

	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("审核时间")
	private Date reviewTime;

	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
	private Date createTime;

	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("变更时间")
	private Date updateTime;

}
