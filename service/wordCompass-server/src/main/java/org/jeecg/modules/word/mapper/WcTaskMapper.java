package org.jeecg.modules.word.mapper;

import org.jeecg.modules.word.entity.WcTask;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * @Description: 任务信息
 * @Author: wordCompass
 * @Date: 2025-01-15
 * @Version: V1.0
 */
public interface WcTaskMapper extends BaseMapper<WcTask> {
    
    /**
     * 根据账号ID查询任务列表
     * @param accountId 账号ID
     * @return 任务列表
     */
    List<WcTask> selectByAccountId(@Param("accountId") String accountId);
    
    /**
     * 根据学生ID查询任务列表
     * @param studentId 学生ID
     * @return 任务列表
     */
    List<WcTask> selectByStudentId(@Param("studentId") String studentId);
    
    /**
     * 根据任务状态查询任务列表
     * @param status 任务状态
     * @return 任务列表
     */
    List<WcTask> selectByStatus(@Param("status") String status);
    
    /**
     * 根据任务类型查询任务列表
     * @param taskType 任务类型
     * @return 任务列表
     */
    List<WcTask> selectByTaskType(@Param("taskType") String taskType);
    
} 