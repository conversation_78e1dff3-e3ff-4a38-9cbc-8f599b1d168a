package org.jeecg.modules.word.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.system.base.entity.JeecgEntity;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * @Description: 任务明细信息
 * @Author: wordCompass
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="WcTaskDetail对象", description="任务明细信息")
@TableName("wc_task_detail")
public class WcTaskDetail extends JeecgEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 账号ID
     */
    @Excel(name = "账号ID", width = 20)
    @ApiModelProperty(value = "账号ID")
    private String accountId;

    /**
     * 学生ID
     */
    @Excel(name = "学生ID", width = 20)
    @ApiModelProperty(value = "学生ID")
    private String studentId;

    /**
     * 任务ID
     */
    @Excel(name = "任务ID", width = 20)
    @ApiModelProperty(value = "任务ID")
    private String taskId;

    /**
     * 单词ID
     */
    @Excel(name = "单词ID", width = 20)
    @ApiModelProperty(value = "单词ID")
    private String wordId;

    /**
     * 单词名称(冗余字段，便于查询)
     */
    @Excel(name = "单词名称", width = 20)
    @ApiModelProperty(value = "单词名称")
    private String wordName;

    /**
     * 单词中文译义
     */
    @Excel(name = "单词译义", width = 30)
    @ApiModelProperty(value = "单词中文译义")
    private String wordTranslation;

    /**
     * 学生写的内容
     */
    @Excel(name = "学生答案", width = 30)
    @ApiModelProperty(value = "学生写的内容")
    private String studentAnswer;

    /**
     * 是否正确(0-错误,1-正确)
     */
    @Excel(name = "是否正确", width = 10, dicCode = "correct_status")
    @ApiModelProperty(value = "是否正确")
    private Integer isCorrect;

    /**
     * 批改备注
     */
    @Excel(name = "批改备注", width = 30)
    @ApiModelProperty(value = "批改备注")
    private String correctionRemark;

    /**
     * 排序号
     */
    @Excel(name = "排序号", width = 10)
    @ApiModelProperty(value = "排序号")
    private Integer sortOrder;

    /**
     * 得分
     */
    @Excel(name = "得分", width = 10)
    @ApiModelProperty(value = "得分")
    private Double score;

    /**
     * 备注
     */
    @Excel(name = "备注", width = 30)
    @ApiModelProperty(value = "备注")
    private String remark;
} 