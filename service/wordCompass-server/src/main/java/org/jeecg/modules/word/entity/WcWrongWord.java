package org.jeecg.modules.word.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.system.base.entity.JeecgEntity;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * @Description: 错词信息
 * @Author: wordCompass
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="WcWrongWord对象", description="错词信息")
@TableName("wc_wrong_word")
public class WcWrongWord extends JeecgEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 账号ID
     */
    @Excel(name = "账号ID", width = 20)
    @ApiModelProperty(value = "账号ID")
    private String accountId;

    /**
     * 学生ID
     */
    @Excel(name = "学生ID", width = 20)
    @ApiModelProperty(value = "学生ID")
    private String studentId;

    /**
     * 单词ID
     */
    @Excel(name = "单词ID", width = 20)
    @ApiModelProperty(value = "单词ID")
    private String wordId;

    /**
     * 单词名称(冗余字段，便于查询)
     */
    @Excel(name = "单词名称", width = 20)
    @ApiModelProperty(value = "单词名称")
    private String wordName;

    /**
     * 听写次数
     */
    @Excel(name = "听写次数", width = 10)
    @ApiModelProperty(value = "听写次数")
    private Integer dictationCount;

    /**
     * 错误次数
     */
    @Excel(name = "错误次数", width = 10)
    @ApiModelProperty(value = "错误次数")
    private Integer errorCount;

    /**
     * 正确次数
     */
    @Excel(name = "正确次数", width = 10)
    @ApiModelProperty(value = "正确次数")
    private Integer correctCount;

    /**
     * 错误率
     */
    @Excel(name = "错误率", width = 10)
    @ApiModelProperty(value = "错误率")
    private Double errorRate;

    /**
     * 最后听写时间
     */
    @Excel(name = "最后听写时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最后听写时间")
    private Date lastDictationTime;

    /**
     * 掌握状态(0-未掌握,1-已掌握)
     */
    @Excel(name = "掌握状态", width = 10, dicCode = "master_status")
    @ApiModelProperty(value = "掌握状态")
    private Integer masterStatus;

    /**
     * 备注
     */
    @Excel(name = "备注", width = 30)
    @ApiModelProperty(value = "备注")
    private String remark;
} 