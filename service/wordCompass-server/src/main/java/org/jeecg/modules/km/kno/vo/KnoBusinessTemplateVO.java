package org.jeecg.modules.km.kno.vo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

@Data
@ApiModel("知识业务模板VO")
public class KnoBusinessTemplateVO {

    @ApiModelProperty("主键ID")
	private String id;

    @ApiModelProperty("模板名称")
	private String name;

    @ApiModelProperty("模板描述")
	private String description;

    @ApiModelProperty("版本号")
	private Integer version;

    @ApiModelProperty("原始模板ID")
	private String originId;

    @ApiModelProperty("是否为最新版本 1-是 0-否")
	private Integer isLatest;

    @ApiModelProperty("状态：0-禁用，1-启用")
	private Integer status;

    @ApiModelProperty("创建者ID")
	private String creatorId;

    @ApiModelProperty("创建者名称")
	private String creatorName;

    @ApiModelProperty("更新者ID")
	private String updaterId;

    @ApiModelProperty("更新者名称")
	private String updaterName;

	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
	private Date createTime;

	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("更新时间")
	private Date updateTime;

    @ApiModelProperty("关联的业务类型列表")
    private List<KnoBusinessTemplateTypeVO> businessTypes;

} 