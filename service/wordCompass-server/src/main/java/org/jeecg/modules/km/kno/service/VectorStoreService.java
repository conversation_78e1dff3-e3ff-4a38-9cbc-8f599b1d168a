package org.jeecg.modules.km.kno.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 向量存储服务类
 * 用于调用向量服务API
 * yf add 2025-07-13
 */
@Slf4j
@Service
public class VectorStoreService {

    @Value("${vector.service.base-url}")
    private String vectorServiceBaseUrl;

    @Value("${vector.service.connect-timeout:30000}")
    private int connectTimeout;

    @Value("${vector.service.read-timeout:300000}")
    private int readTimeout;

    private RestTemplate vectorRestTemplate;

    /**
     * 初始化专门用于向量服务的RestTemplate
     * 设置更长的超时时间以适应向量处理的长时间操作
     */
    @PostConstruct
    public void initVectorRestTemplate() {
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        // 从配置文件读取超时时间
        requestFactory.setConnectTimeout(connectTimeout);
        requestFactory.setReadTimeout(readTimeout);
        
        vectorRestTemplate = new RestTemplate(requestFactory);
        // 解决乱码问题
        vectorRestTemplate.getMessageConverters().set(1, new StringHttpMessageConverter(StandardCharsets.UTF_8));
        
        log.info("向量服务RestTemplate初始化完成，连接超时: {}ms，读取超时: {}ms", connectTimeout, readTimeout);
    }

    /**
     * 根据知识库ID同步API
     * @param knowledgeId 知识库ID
     * @return 同步结果
     */
    public Map<String, Object> syncDocumentsByKnowledgeId(String knowledgeId) {
        try {
            log.info("开始同步知识库文档，知识库ID: {}", knowledgeId);
            
            String url = vectorServiceBaseUrl + "/sync-documents-by-knowledge-id?knowledgeId=" + knowledgeId;
            
            ResponseEntity<Map> response = vectorRestTemplate.exchange(
                url, 
                HttpMethod.POST, 
                null, 
                Map.class
            );
            
            Map<String, Object> result = response.getBody();
            if (result == null) {
                result = new HashMap<>();
                result.put("success", false);
                result.put("message", "向量服务返回空响应");
            }
            
            log.info("知识库文档同步完成，知识库ID: {}, 结果: {}", knowledgeId, result);
            return result;
            
        } catch (Exception e) {
            log.error("同步知识库文档失败，知识库ID: {}", knowledgeId, e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            
            // 根据异常类型提供更具体的错误信息
            if (e.getMessage().contains("Read timed out")) {
                errorResult.put("message", "同步操作超时，请稍后重试或联系管理员");
            } else if (e.getMessage().contains("Connection refused")) {
                errorResult.put("message", "无法连接到向量服务，请检查服务是否启动");
            } else {
                errorResult.put("message", "同步失败: " + e.getMessage());
            }
            
            return errorResult;
        }
    }

    /**
     * 同步所有文档数据到向量库
     * @return 同步结果
     */
    public String manualSyncAllDocuments() {
        try {
            log.info("开始手动同步所有文档到向量库");
            
            String url = vectorServiceBaseUrl + "/manual-sync";
            
            ResponseEntity<String> response = vectorRestTemplate.exchange(
                url, 
                HttpMethod.GET, 
                null, 
                String.class
            );
            
            String result = response.getBody();
            if (result == null) {
                result = "向量服务返回空响应";
            }
            
            log.info("手动同步所有文档完成，结果: {}", result);
            return result;
            
        } catch (Exception e) {
            log.error("手动同步所有文档失败", e);
            
            // 根据异常类型提供更具体的错误信息
            if (e.getMessage().contains("Read timed out")) {
                return "同步失败: 操作超时，请稍后重试或联系管理员";
            } else if (e.getMessage().contains("Connection refused")) {
                return "同步失败: 无法连接到向量服务，请检查服务是否启动";
            } else {
                return "同步失败: " + e.getMessage();
            }
        }
    }
}
