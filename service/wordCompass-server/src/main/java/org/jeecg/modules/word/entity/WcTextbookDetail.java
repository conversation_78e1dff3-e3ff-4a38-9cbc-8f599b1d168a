package org.jeecg.modules.word.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.system.base.entity.JeecgEntity;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * @Description: 教材明细信息(单元、章节)
 * @Author: wordCompass
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="WcTextbookDetail对象", description="教材明细信息")
@TableName("wc_textbook_detail")
public class WcTextbookDetail extends JeecgEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 教材ID
     */
    @Excel(name = "教材ID", width = 20)
    @ApiModelProperty(value = "教材ID")
    private String textbookId;

    /**
     * 父级ID
     */
    @ApiModelProperty(value = "父级ID")
    private String parentId;

    /**
     * 名称
     */
    @Excel(name = "名称", width = 30)
    @ApiModelProperty(value = "名称")
    private String detailName;

    /**
     * 内容
     */
    @Excel(name = "内容", width = 50)
    @ApiModelProperty(value = "内容")
    private String content;

    /**
     * 排序号
     */
    @Excel(name = "排序号", width = 10)
    @ApiModelProperty(value = "排序号")
    private Integer sortOrder;

    /**
     * 类型(unit-单元,chapter-章节,lesson-课文)
     */
    @Excel(name = "类型", width = 15, dicCode = "textbook_detail_type")
    @ApiModelProperty(value = "类型")
    private String detailType;

    /**
     * 层级
     */
    @ApiModelProperty(value = "层级")
    private Integer level;

    /**
     * 是否有子节点(0-否,1-是)
     */
    @ApiModelProperty(value = "是否有子节点")
    private Integer hasChild;

    /**
     * 状态(0-禁用,1-启用)
     */
    @Excel(name = "状态", width = 10, dicCode = "valid_status")
    @ApiModelProperty(value = "状态")
    private Integer status;

    /**
     * 备注
     */
    @Excel(name = "备注", width = 30)
    @ApiModelProperty(value = "备注")
    private String remark;
} 