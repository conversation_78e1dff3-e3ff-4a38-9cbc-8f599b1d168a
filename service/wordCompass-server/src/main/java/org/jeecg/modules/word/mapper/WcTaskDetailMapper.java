package org.jeecg.modules.word.mapper;

import org.jeecg.modules.word.entity.WcTaskDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * @Description: 任务明细信息
 * @Author: wordCompass
 * @Date: 2025-01-15
 * @Version: V1.0
 */
public interface WcTaskDetailMapper extends BaseMapper<WcTaskDetail> {
    
    /**
     * 根据任务ID查询明细列表
     * @param taskId 任务ID
     * @return 明细列表
     */
    List<WcTaskDetail> selectByTaskId(@Param("taskId") String taskId);
    
    /**
     * 根据是否正确查询明细列表
     * @param taskId 任务ID
     * @param isCorrect 是否正确
     * @return 明细列表
     */
    List<WcTaskDetail> selectByCorrectStatus(@Param("taskId") String taskId, @Param("isCorrect") Integer isCorrect);
    
} 