package org.jeecg.modules.word.service;

import com.alibaba.fastjson.JSONObject;

import java.util.Map;

/**
 * @Description: 微信登录服务接口
 * @Author: wordCompass
 * @Date: 2025-01-15
 * @Version: V1.0
 */
public interface WechatLoginService {

    /**
     * 发送短信验证码
     * @param phone 手机号
     * @return 发送结果
     */
    Map<String, Object> sendSmsCode(String phone) throws Exception;

    /**
     * 身份认证登录
     * @param phone 手机号
     * @param smsCode 短信验证码
     * @param appid 小程序ID
     * @param code 微信临时登录凭证
     * @return 认证结果
     */
    JSONObject authLogin(String phone, String smsCode, String appid, String code) throws Exception;

    /**
     * 自动登录
     * @param appid 小程序ID
     * @param code 微信临时登录凭证
     * @return 登录结果
     */
    JSONObject autoLogin(String appid, String code) throws Exception;

    /**
     * 微信用户注销(解绑)
     * @param appid 小程序ID
     * @param code 微信临时登录凭证
     * @return 注销结果
     */
    boolean deleteBindWeixin(String appid, String code) throws Exception;

    /**
     * 调用微信接口获取用户信息
     * @param appid 小程序ID
     * @param code 微信临时登录凭证
     * @return 微信用户信息
     */
    JSONObject getWechatUserInfo(String appid, String code) throws Exception;

    /**
     * 验证短信验证码
     * @param phone 手机号
     * @param smsCode 验证码
     * @return 验证结果
     */
    boolean verifySmsCode(String phone, String smsCode);

    /**
     * 生成访问令牌
     * @param userId 用户ID
     * @param username 用户名
     * @return 令牌信息
     */
    JSONObject generateAccessToken(String userId, String username) throws Exception;
}