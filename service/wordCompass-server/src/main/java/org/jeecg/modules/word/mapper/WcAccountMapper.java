package org.jeecg.modules.word.mapper;

import org.jeecg.modules.word.entity.WcAccount;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * @Description: 账号信息
 * @Author: wordCompass
 * @Date: 2025-01-15
 * @Version: V1.0
 */
public interface WcAccountMapper extends BaseMapper<WcAccount> {
    
    /**
     * 根据手机号查询账号
     * @param mobile 手机号
     * @return 账号信息
     */
    WcAccount selectByMobile(@Param("mobile") String mobile);
    
    /**
     * 根据邮箱查询账号
     * @param email 邮箱
     * @return 账号信息
     */
    WcAccount selectByEmail(@Param("email") String email);
    
    /**
     * 根据账号类型查询账号列表
     * @param accountType 账号类型
     * @return 账号列表
     */
    List<WcAccount> selectByAccountType(@Param("accountType") Integer accountType);
    
} 