package org.jeecg.modules.word.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.word.entity.WcWechatUser;

/**
 * @Description: 微信用户信息
 * @Author: wordCompass
 * @Date: 2025-01-15
 * @Version: V1.0
 */
public interface WcWechatUserService extends IService<WcWechatUser> {

    /**
     * 检查OpenID是否存在
     * @param openid OpenID
     * @return 是否存在
     */
    boolean checkOpenidExists(String openid);

    /**
     * 根据OpenID查询微信用户
     * @param openid OpenID
     * @return 微信用户信息
     */
    WcWechatUser getByOpenid(String openid);

    /**
     * 根据UnionID查询微信用户
     * @param unionid UnionID
     * @return 微信用户信息
     */
    WcWechatUser getByUnionid(String unionid);

    /**
     * 根据关联用户ID查询微信用户
     * @param accountId 关联用户ID
     * @return 微信用户信息
     */
    WcWechatUser getByAccountId(String accountId);
}