package org.jeecg.modules.km.kno.api;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.km.kno.service.AnalysisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Description: 数据分析-api
 * @Author: yf
 * @Date:   2025-04-17
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "数据分析-api")
@RestController
@RequestMapping("/kno/api/analysis")
public class AnalysisApi {


    @Autowired
    private AnalysisService analysisService;


    
    

    /**
     * 获取知识总数、今日新增数量及百分比
     * creater: yf
     * date: 2025-04-18
     */
    @ApiOperation(value = "获取知识总数、今日新增数量及百分比")
    @GetMapping("/getKnoBaseTotalNum")
    public Result<?> getKnoBaseTotalNum() {
        // 1. 统计KnoBase状态不为0的总数
        int total = analysisService.countKnoBaseStatusNotZero();
        // 2. 统计今天新增且status不为0的数量
        int today = analysisService.countKnoBaseTodayStatusNotZero();
        // 3. 百分比
        double percent = total == 0 ? 0 : Math.round((today * 100.0 / total) * 100) / 100.0;
        java.util.Map<String, Object> result = new java.util.HashMap<>();
        result.put("totalNum", total);
        result.put("todayNum", today);
        result.put("percentNum", percent);
        return Result.OK(result);
    }

    /**
     * 获取知识总数、本月新增数量及百分比
     * creater: yf
     * date: 2025-04-18
     */
    @ApiOperation(value = "获取知识总数、本月新增数量及百分比")
    @GetMapping("/getKnoBaseCurMonthAddNum")
    public Result<?> getKnoBaseCurMonthAddNum() {
        // 1. 统计KnoBase状态不为0的总数
        int total = analysisService.countKnoBaseStatusNotZero();
        // 2. 统计本月新增且status不为0的数量
        int month = analysisService.countKnoBaseCurMonthStatusNotZero();
        // 3. 百分比
        double percent = total == 0 ? 0 : Math.round((month * 100.0 / total) * 100) / 100.0;
        java.util.Map<String, Object> result = new java.util.HashMap<>();
        result.put("totalNum", total);
        result.put("monthNum", month);
        result.put("percentNum", percent);
        return Result.OK(result);
    }

    /**
     * 获取当前用户知识总数、本月新增数量及百分比
     * creater: yf
     * date: 2025-04-18
     */
    @ApiOperation(value = "获取当前用户知识总数、本月新增数量及百分比")
    @GetMapping("/getKnoBaseMyMonthAddNum")
    public Result<?> getKnoBaseMyMonthAddNum() {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String username = loginUser.getUsername();
        // 1. 统计我创建的KnoBase状态不为0的总数
        int total = analysisService.countKnoBaseMyStatusNotZero(username);
        // 2. 统计本月我新增且status不为0的数量
        int month = analysisService.countKnoBaseMyCurMonthStatusNotZero(username);
        // 3. 百分比
        double percent = total == 0 ? 0 : Math.round((month * 100.0 / total) * 100) / 100.0;
        java.util.Map<String, Object> result = new java.util.HashMap<>();
        result.put("totalNum", total);
        result.put("monthNum", month);
        result.put("percentNum", percent);
        return Result.OK(result);
    }

    /**
     * 获取知识总浏览次数、本月新增访问数量及百分比
     * creater: yf
     * date: 2025-04-18
     */
    @ApiOperation(value = "获取知识总浏览次数、本月新增访问数量及百分比")
    @GetMapping("/getKnoBaseMonthNumView")
    public Result<?> getKnoBaseMonthNumView() {
        // 1. 统计KnoBase状态不为0的viewCount总和
        int totalView = analysisService.sumViewCountStatusNotZero();
        // 2. 统计本月新增访问数量
        int monthAccess = analysisService.countCurMonthAccess();
        // 3. 百分比
        double percent = totalView == 0 ? 0 : Math.round((monthAccess * 100.0 / totalView) * 100) / 100.0;
        java.util.Map<String, Object> result = new java.util.HashMap<>();
        result.put("totalView", totalView);
        result.put("monthAccess", monthAccess);
        result.put("percentNum", percent);
        return Result.OK(result);
    }
}
