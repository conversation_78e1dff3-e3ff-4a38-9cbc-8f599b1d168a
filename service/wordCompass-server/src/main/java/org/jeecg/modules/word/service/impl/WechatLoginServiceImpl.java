package org.jeecg.modules.word.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.PasswordUtil;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.message.entity.SysMessage;
import org.jeecg.modules.message.service.ISysMessageService;
import org.jeecg.modules.system.entity.SysUser;
// import org.jeecg.modules.system.service.ISysUserService; // 暂时注释，需要确认系统用户服务
import org.jeecg.modules.word.entity.WcAccount;
import org.jeecg.modules.word.entity.WcWechatUser;
import org.jeecg.modules.word.service.WcAccountService;
import org.jeecg.modules.word.service.WcWechatUserService;
import org.jeecg.modules.word.service.WechatLoginService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @Description: 微信登录服务实现类
 * @Author: yf add ********
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Service
@Slf4j
public class WechatLoginServiceImpl implements WechatLoginService {

    @Autowired
    private WcWechatUserService wcWechatUserService;
    
    @Autowired
    private WcAccountService wcAccountService;

    @Autowired
    private ISysMessageService sysMessageService;
    
    // @Autowired
    // private ISysUserService sysUserService; // 暂时注释，需要确认系统用户服务
    
    @Autowired
    private RedisUtil redisUtil;
    
    @Autowired
    private RestTemplate restTemplate;
    
    // 微信小程序配置
    @Value("${wechat.miniprogram.appid:}")
    private String defaultAppId;
    
    @Value("${wechat.miniprogram.secret:}")
    private String defaultAppSecret;
    
    // 短信验证码缓存前缀
    private static final String SMS_CODE_PREFIX = "sms_code:";
    
    // 短信验证码有效期（分钟）
    private static final int SMS_CODE_EXPIRE_MINUTES = 5;
    
    // 微信API地址
    private static final String WECHAT_API_URL = "https://api.weixin.qq.com/sns/jscode2session";

    @Override
    public Map<String, Object> sendSmsCode(String phone) throws Exception {
        log.info("开始发送短信验证码，手机号: {}", phone);
        
        // 生成6位随机验证码
        String smsCode = generateSmsCode();

        // 记录到 系统消息表 sys_sms 中 作为历史记录查看
        try{
            SysMessage sysMessage = new SysMessage();
            sysMessage.setEsTitle("登录验证码,手机号:"+phone+"的验证码为："+smsCode);
            String content = String.format("您的登录验证码为：%s，请勿将验证码告知他人。", smsCode);
            sysMessage.setEsContent(content);
            sysMessage.setEsParam("");
            sysMessage.setEsType("sms");
            sysMessage.setEsReceiver(phone);
            sysMessage.setEsSendStatus("1");
            sysMessage.setEsSendNum(1);
            sysMessage.setEsSendTime(new Date());
            sysMessageService.save(sysMessage);
        }catch(Exception e){
            log.error("记录短信验证码失败", e);
        }

        // 存储到Redis，设置5分钟过期
        String cacheKey = SMS_CODE_PREFIX + phone;
        redisUtil.set(cacheKey, smsCode, SMS_CODE_EXPIRE_MINUTES * 60);
        
        log.info("短信验证码已生成并缓存，手机号: {}, 验证码: {}", phone, smsCode);
        
        // TODO: 调用短信网关接口发送短信（暂时预留）
        // sendSmsToGateway(phone, smsCode);
        
        Map<String, Object> result = new HashMap<>();
        result.put("phone", phone);
        result.put("smsCode", smsCode); // 开发环境返回验证码，生产环境应该移除
        result.put("expireTime", SMS_CODE_EXPIRE_MINUTES);
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public JSONObject authLogin(String phone, String smsCode, String appid, String code) throws Exception {
        log.info("开始身份认证登录，手机号: {}, appid: {}", phone, appid);
        
        // 1. 验证短信验证码
        if (!verifySmsCode(phone, smsCode)) {
            throw new RuntimeException("验证码错误或已过期");
        }
        
        // 2. 调用微信接口获取用户信息
        JSONObject wechatInfo = getWechatUserInfo(appid, code);
        String openid = wechatInfo.getString("openid");
        String sessionKey = wechatInfo.getString("session_key");
        String unionid = wechatInfo.getString("unionid");
        
        if (oConvertUtils.isEmpty(openid)) {
            throw new RuntimeException("获取微信用户信息失败");
        }
        
        // 3. 根据openid查询微信用户信息
        WcWechatUser wechatUser = wcWechatUserService.getByOpenid(openid);
        
        WcAccount account;
        if (wechatUser != null) {
            // 微信用户存在，获取关联的用户信息
            log.info("微信用户已存在，openid: {}", openid);
            account = wcAccountService.getById(wechatUser.getAccountId());
            if (account == null) {
                throw new RuntimeException("用户信息不存在");
            }
        } else {
            // 微信用户不存在，根据手机号查询用户信息
            log.info("微信用户不存在，根据手机号查询用户信息: {}", phone);
            
            QueryWrapper<WcAccount> accountQuery = new QueryWrapper<>();
            accountQuery.eq("mobile", phone);
            account = wcAccountService.getOne(accountQuery);
            
            if (account != null) {
                // 用户信息存在，创建微信用户信息并绑定
                log.info("用户信息存在，创建微信用户绑定，用户ID: {}", account.getId());
                wechatUser = createWechatUser(openid, unionid, sessionKey, account.getId());
            } else {
                // 用户信息不存在，创建完整的用户体系
                log.info("用户信息不存在，创建新用户体系");
                account = createCompleteUserSystem(phone, openid, unionid, sessionKey);
            }
        }
        
        // 4. 更新最后登录时间
        account.setLastLoginTime(new Date());
        wcAccountService.updateById(account);
        
        if (wechatUser != null) {
            wechatUser.setLastLoginTime(new Date());
            wcWechatUserService.updateById(wechatUser);
        }
        
        // 5. 生成访问令牌
        return generateAccessToken(account.getId(), account.getSysUsername());
    }

    @Override
    public JSONObject autoLogin(String appid, String code) throws Exception {
        log.info("开始自动登录，appid: {}", appid);
        
        // 1. 调用微信接口获取用户信息
        JSONObject wechatInfo = getWechatUserInfo(appid, code);
        String openid = wechatInfo.getString("openid");
        
        if (oConvertUtils.isEmpty(openid)) {
            throw new RuntimeException("获取微信用户信息失败");
        }
        
        // 2. 根据openid查询微信用户信息
        WcWechatUser wechatUser = wcWechatUserService.getByOpenid(openid);
        if (wechatUser == null) {
            throw new RuntimeException("用户未绑定，请先进行身份认证");
        }
        
        // 3. 获取关联的用户信息
        WcAccount account = wcAccountService.getById(wechatUser.getAccountId());
        if (account == null) {
            throw new RuntimeException("用户信息不存在");
        }
        
        // 4. 更新最后登录时间
        account.setLastLoginTime(new Date());
        wcAccountService.updateById(account);
        
        wechatUser.setLastLoginTime(new Date());
        wcWechatUserService.updateById(wechatUser);
        
        // 5. 生成访问令牌
        return generateAccessToken(account.getId(), account.getSysUsername());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteBindWeixin(String appid, String code) throws Exception {
        log.info("开始微信用户注销，appid: {}", appid);
        
        // 1. 调用微信接口获取用户信息
        JSONObject wechatInfo = getWechatUserInfo(appid, code);
        String openid = wechatInfo.getString("openid");
        
        if (oConvertUtils.isEmpty(openid)) {
            log.warn("获取微信用户信息失败，视为注销成功");
            return true;
        }
        
        // 2. 根据openid查询微信用户信息
        WcWechatUser wechatUser = wcWechatUserService.getByOpenid(openid);
        if (wechatUser == null) {
            log.info("微信用户不存在，视为注销成功，openid: {}", openid);
            return true;
        }
        
        // 3. 删除微信用户信息
        wcWechatUserService.removeById(wechatUser.getId());
        log.info("微信用户注销成功，openid: {}", openid);
        
        return true;
    }

    @Override
    public JSONObject getWechatUserInfo(String appid, String code) throws Exception {
        log.info("调用微信接口获取用户信息，appid: {}", appid);
        
        // 使用配置的appid和secret，如果参数中没有提供
        String actualAppId = oConvertUtils.isEmpty(appid) ? defaultAppId : appid;
        String appSecret = defaultAppSecret; // 实际项目中可能需要根据appid动态获取secret
        
        if (oConvertUtils.isEmpty(actualAppId) || oConvertUtils.isEmpty(appSecret)) {
            throw new RuntimeException("微信小程序配置不完整");
        }
        
        // 构建请求URL
        String url = String.format("%s?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code",
                WECHAT_API_URL, actualAppId, appSecret, code);
        
        try {
            // 发送HTTP请求
            HttpHeaders headers = new HttpHeaders();
            headers.set("User-Agent", "WordCompass/1.0");
            HttpEntity<String> entity = new HttpEntity<>(headers);
            
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);
            String responseBody = response.getBody();
            
            log.info("微信接口响应: {}", responseBody);
            
            JSONObject result = JSONObject.parseObject(responseBody);
            
            // 检查是否有错误
            if (result.containsKey("errcode") && result.getInteger("errcode") != 0) {
                String errmsg = result.getString("errmsg");
                throw new RuntimeException("微信接口调用失败: " + errmsg);
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("调用微信接口失败", e);
            throw new RuntimeException("调用微信接口失败: " + e.getMessage());
        }
    }

    @Override
    public boolean verifySmsCode(String phone, String smsCode) {
        String cacheKey = SMS_CODE_PREFIX + phone;
        String cachedCode = (String) redisUtil.get(cacheKey);
        
        if (oConvertUtils.isEmpty(cachedCode)) {
            log.warn("验证码不存在或已过期，手机号: {}", phone);
            return false;
        }
        
        boolean isValid = cachedCode.equals(smsCode);
        if (isValid) {
            // 验证成功后删除验证码
            redisUtil.del(cacheKey);
            log.info("验证码验证成功，手机号: {}", phone);
        } else {
            log.warn("验证码验证失败，手机号: {}, 输入: {}, 期望: {}", phone, smsCode, cachedCode);
        }
        
        return isValid;
    }

    @Override
    public JSONObject generateAccessToken(String userId, String username) throws Exception {
        // TODO: 实现JWT令牌生成逻辑
        // 这里简化处理，实际项目中应该使用JWT或其他安全的令牌机制
        
        String accessToken = UUID.randomUUID().toString().replace("-", "");
        
        // 将令牌信息存储到Redis，设置过期时间
        String tokenKey = "access_token:" + accessToken;
        Map<String, Object> tokenInfo = new HashMap<>();
        tokenInfo.put("userId", userId);
        tokenInfo.put("username", username);
        tokenInfo.put("createTime", System.currentTimeMillis());
        
        redisUtil.set(tokenKey, JSONObject.toJSONString(tokenInfo), 7 * 24 * 60 * 60); // 7天过期
        
        // 获取用户详细信息
        WcAccount account = wcAccountService.getById(userId);
        
        JSONObject result = new JSONObject();
        result.put("accessToken", accessToken);
        result.put("tokenType", "Bearer");
        result.put("expiresIn", 7 * 24 * 60 * 60); // 7天
        result.put("userId", userId);
        result.put("username", username);
        result.put("nickname", account.getNickname());
        result.put("mobile", account.getMobile());
        result.put("avatar", account.getAvatar());
        
        return result;
    }
    
    /**
     * 生成6位随机验证码
     */
    private String generateSmsCode() {
        Random random = new Random();
        return String.format("%06d", random.nextInt(1000000));
    }
    
    /**
     * 创建微信用户信息
     */
    private WcWechatUser createWechatUser(String openid, String unionid, String sessionKey, String accountId) {
        WcWechatUser wechatUser = new WcWechatUser();
        wechatUser.setOpenid(openid);
        wechatUser.setUnionid(unionid);
        wechatUser.setAccountId(accountId);
        wechatUser.setStatus(1);
        wechatUser.setCreateTime(new Date());
        wechatUser.setLastLoginTime(new Date());
        
        wcWechatUserService.save(wechatUser);
        return wechatUser;
    }
    
    /**
     * 创建完整的用户体系（SysUser + WcAccount + WcWechatUser）
     */
    @Transactional(rollbackFor = Exception.class)
    public WcAccount createCompleteUserSystem(String phone, String openid, String unionid, String sessionKey) {
        // 1. 创建系统账号 - 暂时跳过，需要确认系统用户服务
        // SysUser sysUser = new SysUser();
        // sysUser.setUsername("wx_" + System.currentTimeMillis()); // 生成唯一用户名
        // sysUser.setRealname("微信用户");
        // sysUser.setPhone(phone);
        // sysUser.setStatus(1);
        // sysUser.setDelFlag(0);
        // sysUser.setCreateTime(new Date());
        
        // sysUserService.save(sysUser);
        
        // 2. 创建用户信息
         WcAccount account = new WcAccount();
         String username = "wx_" + System.currentTimeMillis(); // 生成唯一用户名
         //account.setSysUsername("");
         account.setNickname(username);
         account.setMobile(phone);
         account.setGender(0); // 未知性别
         account.setCreateTime(new Date());
         account.setUpdateTime(new Date());
         account.setCreateBy("system");
         account.setUpdateBy("system");
         account.setStatus(1);//启用
         account.setAccountType(1);// 普通用户

         // 设置密码（默认密码为123456）
         String salt = oConvertUtils.randomGen(8);
         account.setSalt(salt);
         String passwordEncode = PasswordUtil.encrypt(account.getMobile(), "123456", salt);
         account.setPassword(passwordEncode);

         wcAccountService.save(account);
        
        // 3. 创建微信用户信息
        WcWechatUser wechatUser = new WcWechatUser();
        wechatUser.setAccountId(account.getId());
        wechatUser.setOpenid(openid);
        wechatUser.setUnionid(unionid);
        wechatUser.setSessionKey(sessionKey);
        wechatUser.setWechatNickname(username);
        wechatUser.setCreateTime(new Date());
        wechatUser.setUpdateTime(new Date());
        wechatUser.setCreateBy("system");
        wechatUser.setUpdateBy("system");
        wechatUser.setStatus(1);//启用
        wcWechatUserService.save(wechatUser);
        
        return account;
    }
}