package org.jeecg.modules.word.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.word.entity.WcTextbook;
import org.jeecg.modules.word.entity.WcTextbookDetail;

import java.util.List;

/**
 * @Description: 教材基本信息
 * @Author: wordCompass
 * @Date: 2025-01-15
 * @Version: V1.0
 */
public interface IWcTextbookService extends IService<WcTextbook> {

    /**
     * 根据年级查询教材列表
     */
    List<WcTextbook> getTextbooksByGrade(String grade);

    /**
     * 根据年级和版本查询教材列表
     */
    List<WcTextbook> getTextbooksByGradeAndVersion(String grade, String version);

    /**
     * 获取教材的树形结构
     */
    List<WcTextbookDetail> getTextbookTree(String textbookId);

    /**
     * 获取教材明细列表
     */
    List<WcTextbookDetail> getTextbookDetails(String textbookId, String type);

    /**
     * 保存教材明细
     */
    boolean saveTextbookDetail(WcTextbookDetail detail);

    /**
     * 更新教材明细
     */
    boolean updateTextbookDetail(WcTextbookDetail detail);

    /**
     * 删除教材明细
     */
    boolean deleteTextbookDetail(String detailId);

    /**
     * 获取教材明细详情
     */
    WcTextbookDetail getTextbookDetailById(String detailId);
} 