package org.jeecg.modules.km.kno.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.jeecg.modules.km.kno.entity.KnoBase;
import org.jeecg.modules.km.kno.mapper.KnoBaseMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 数据分析服务
 * creater: yf
 * date: 2025-04-18
 */
@Service
public class AnalysisService {


    @Autowired
    private KnoBaseService knoBaseService;

    @Autowired
	private KnoCategoryService knoCategoryService;

    @Autowired
    private KnoBaseMapper knoBaseMapper;

    @Autowired
    private org.jeecg.modules.km.kno.service.KnoLogAccessService knoLogAccessService;
    @Autowired
    private org.jeecg.modules.km.kno.mapper.KnoLogAccessMapper knoLogAccessMapper;

    @Autowired
	private KnoDeptService knoDeptService;




    /**
     * 统计KnoBase状态不为0的总数
     */
    public int countKnoBaseStatusNotZero() {
        QueryWrapper<KnoBase> wrapper = new QueryWrapper<>();
        //document_type = '1'
        wrapper.eq("document_type", "1");
        wrapper.ne("status", 0);
        wrapper = getUserDataQueryRange(wrapper);//添加 数据查询范围 总公司 查全部  分公司 查询当前用户分公司所有部门的id
        return knoBaseMapper.selectCount(wrapper).intValue();
    }

    /**
     * 统计今天新增且status不为0的KnoBase数量
     */
    public int countKnoBaseTodayStatusNotZero() {
        QueryWrapper<KnoBase> wrapper = new QueryWrapper<>();
        wrapper.eq("document_type", "1");
        wrapper.ne("status", 0);
        wrapper = getUserDataQueryRange(wrapper);//添加 数据查询范围 总公司 查全部  分公司 查询当前用户分公司所有部门的id
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        wrapper.apply("DATE(create_time) = {0}", today);
        return knoBaseMapper.selectCount(wrapper).intValue();
    }

    /**
     * 统计本月新增且status不为0的KnoBase数量
     */
    public int countKnoBaseCurMonthStatusNotZero() {
        QueryWrapper<KnoBase> wrapper = new QueryWrapper<>();
        wrapper.eq("document_type", "1");
        wrapper.ne("status", 0);
        wrapper = getUserDataQueryRange(wrapper);//添加 数据查询范围 总公司 查全部  分公司 查询当前用户分公司所有部门的id
        String month = java.time.LocalDate.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM"));
        wrapper.apply("DATE_FORMAT(create_time, '%Y-%m') = {0}", month);
        return knoBaseMapper.selectCount(wrapper).intValue();
    }

    /**
     * 统计指定用户创建的KnoBase状态不为0的总数
     */
    public int countKnoBaseMyStatusNotZero(String username) {
        QueryWrapper<KnoBase> wrapper = new QueryWrapper<>();
        wrapper.eq("document_type", "1");
        wrapper.ne("status", 0);
        wrapper.eq("creator_name", username);
        wrapper = getUserDataQueryRange(wrapper);//添加 数据查询范围 总公司 查全部  分公司 查询当前用户分公司所有部门的id
        return knoBaseMapper.selectCount(wrapper).intValue();
    }

    /**
     * 统计指定用户本月新增且status不为0的KnoBase数量
     */
    public int countKnoBaseMyCurMonthStatusNotZero(String username) {
        QueryWrapper<KnoBase> wrapper = new QueryWrapper<>();
        wrapper.eq("document_type", "1");
        wrapper.ne("status", 0);
        wrapper.eq("creator_name", username);
        wrapper = getUserDataQueryRange(wrapper);//添加 数据查询范围 总公司 查全部  分公司 查询当前用户分公司所有部门的id
        String month = java.time.LocalDate.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM"));
        wrapper.apply("DATE_FORMAT(create_time, '%Y-%m') = {0}", month);
        return knoBaseMapper.selectCount(wrapper).intValue();
    }

    /**
     * 统计KnoBase状态不为0的viewCount总和
     */
    public int sumViewCountStatusNotZero() {
        List<String> childDeptIds = knoDeptService.getUserDataQueryRange();
        Integer sum = knoBaseMapper.sumViewCountStatusNotZero(childDeptIds);
        return sum == null ? 0 : sum;
    }

    /**
     * 统计本月新增访问数量
     */
    public int countCurMonthAccess() {
        Integer count = knoLogAccessMapper.countCurMonthAccess();
        return count == null ? 0 : count;
    }



    //添加 数据查询范围 总公司 查全部  分公司 查询当前用户分公司所有部门的id
    private  QueryWrapper<KnoBase> getUserDataQueryRange(QueryWrapper<KnoBase> wrapper){
        String deptId = knoDeptService.getCurrentOrganId();
        //分公司
        if(knoDeptService.isNotRootOrgan(deptId)){
            List<String> childDeptIds = knoDeptService.getDeptAndSubDeptIds(deptId);
            wrapper.in("organ_id", childDeptIds);
        }
        return wrapper;
    }



}
