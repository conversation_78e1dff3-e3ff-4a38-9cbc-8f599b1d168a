package org.jeecg.modules.word.mapper;

import org.jeecg.modules.word.entity.WcWord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * @Description: 单词信息
 * @Author: wordCompass
 * @Date: 2025-01-15
 * @Version: V1.0
 */
public interface WcWordMapper extends BaseMapper<WcWord> {
    
    /**
     * 根据教材ID查询单词列表
     * @param textbookId 教材ID
     * @return 单词列表
     */
    List<WcWord> selectByTextbookId(@Param("textbookId") String textbookId);
    
    /**
     * 根据单元ID查询单词列表
     * @param unitId 单元ID
     * @return 单词列表
     */
    List<WcWord> selectByUnitId(@Param("unitId") String unitId);
    
    /**
     * 根据章节ID查询单词列表
     * @param chapterId 章节ID
     * @return 单词列表
     */
    List<WcWord> selectByChapterId(@Param("chapterId") String chapterId);
    
    /**
     * 根据难易程度查询单词列表
     * @param difficultyLevel 难易程度
     * @return 单词列表
     */
    List<WcWord> selectByDifficultyLevel(@Param("difficultyLevel") Integer difficultyLevel);
    
    /**
     * 根据单词名称模糊查询
     * @param wordName 单词名称
     * @return 单词列表
     */
    List<WcWord> selectByWordNameLike(@Param("wordName") String wordName);
    
} 