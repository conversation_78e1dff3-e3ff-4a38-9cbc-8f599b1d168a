<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.km.kno.mapper.KnoBusinessTypeMapper">

    <!-- 根据父ID查询子业务类型列表 -->
    <select id="selectByParentId" resultType="org.jeecg.modules.km.kno.entity.KnoBusinessType">
        SELECT * FROM kno_business_type 
        WHERE parent_id = #{parentId} 
        ORDER BY sort ASC, create_time ASC
    </select>

    <!-- 查询所有启用的业务类型 -->
    <select id="selectEnabledTypes" resultType="org.jeecg.modules.km.kno.entity.KnoBusinessType">
        SELECT * FROM kno_business_type 
        WHERE status = 1 
        ORDER BY sort ASC, create_time ASC
    </select>

    <!-- 根据层级查询业务类型 -->
    <select id="selectByLevel" resultType="org.jeecg.modules.km.kno.entity.KnoBusinessType">
        SELECT * FROM kno_business_type 
        WHERE level = #{level} AND status = 1
        ORDER BY sort ASC, create_time ASC
    </select>

    <!-- 查询树形结构数据 -->
    <select id="selectTreeSelectModelByParentId" resultType="org.jeecg.modules.system.model.TreeSelectModel">
        SELECT 
            id as "key",
            name as "title",
            id as "value",
            parent_id as "parentId",
            CASE 
                WHEN has_child = '1' THEN 0 
                ELSE 1 
            END as "isLeaf"
        FROM kno_business_type kbt
        WHERE parent_id = #{parentId} AND status = 1
        ORDER BY sort ASC, create_time ASC
    </select>

</mapper> 