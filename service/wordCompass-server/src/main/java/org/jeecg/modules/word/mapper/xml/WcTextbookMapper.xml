<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.word.mapper.WcTextbookMapper">

    <!-- 根据年级查询教材列表 -->
    <select id="selectByGrade" resultType="org.jeecg.modules.word.entity.WcTextbook">
        SELECT * FROM wc_textbook 
        WHERE grade = #{grade} 
        AND del_flag = 0
        ORDER BY sort_order ASC, create_time ASC
    </select>
    
    <!-- 根据年级和版本查询教材列表 -->
    <select id="selectByGradeAndVersion" resultType="org.jeecg.modules.word.entity.WcTextbook">
        SELECT * FROM wc_textbook 
        WHERE grade = #{grade} 
        AND version = #{version}
        AND del_flag = 0
        ORDER BY sort_order ASC, create_time ASC
    </select>

</mapper>
