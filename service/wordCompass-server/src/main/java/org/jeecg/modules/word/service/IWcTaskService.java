package org.jeecg.modules.word.service;

import org.jeecg.modules.word.entity.WcTask;
import org.jeecg.modules.word.vo.WcTaskVO;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 任务信息
 * @Author: wordCompass
 * @Date: 2025-01-15
 * @Version: V1.0
 */
public interface IWcTaskService extends IService<WcTask> {
    
    /**
     * 创建任务
     * @param taskVO 任务VO
     * @return 创建结果
     */
    boolean createTask(WcTaskVO taskVO);
    
    /**
     * 批改任务
     * @param taskId 任务ID
     * @param taskVO 任务VO（包含批改结果）
     * @return 批改结果
     */
    boolean correctTask(String taskId, WcTaskVO taskVO);
    
    /**
     * 根据学生ID查询任务列表
     * @param studentId 学生ID
     * @return 任务列表
     */
    List<WcTask> getTasksByStudentId(String studentId);
    
    /**
     * 根据任务状态查询任务列表
     * @param accountId 账号ID
     * @param status 任务状态
     * @return 任务列表
     */
    List<WcTask> getTasksByStatus(String accountId, String status);
    
    /**
     * 完成任务
     * @param taskId 任务ID
     * @param taskVO 任务VO（包含学生答案）
     * @return 完成结果
     */
    boolean finishTask(String taskId, WcTaskVO taskVO);
    
} 