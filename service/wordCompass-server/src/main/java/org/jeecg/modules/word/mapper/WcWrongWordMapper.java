package org.jeecg.modules.word.mapper;

import org.jeecg.modules.word.entity.WcWrongWord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * @Description: 错词信息
 * @Author: wordCompass
 * @Date: 2025-01-15
 * @Version: V1.0
 */
public interface WcWrongWordMapper extends BaseMapper<WcWrongWord> {
    
    /**
     * 根据账号ID和学生ID查询错词列表
     * @param accountId 账号ID
     * @param studentId 学生ID
     * @return 错词列表
     */
    List<WcWrongWord> selectByAccountAndStudent(@Param("accountId") String accountId, @Param("studentId") String studentId);
    
    /**
     * 根据掌握状态查询错词列表
     * @param accountId 账号ID
     * @param studentId 学生ID
     * @param masterStatus 掌握状态
     * @return 错词列表
     */
    List<WcWrongWord> selectByMasterStatus(@Param("accountId") String accountId, @Param("studentId") String studentId, @Param("masterStatus") Integer masterStatus);
    
} 