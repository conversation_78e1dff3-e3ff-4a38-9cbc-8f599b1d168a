package org.jeecg.modules.km.kno.vo;

import java.io.Serializable;
import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

@Data
@ApiModel("知识业务模板类型关联VO")
public class KnoBusinessTemplateTypeVO {

    @ApiModelProperty("主键ID")
	private String id;

    @ApiModelProperty("业务模板ID")
	private String templateId;

    @ApiModelProperty("业务类型ID")
	private String businessTypeId;

    @ApiModelProperty("业务类型名称")
	private String businessTypeName;

    @ApiModelProperty("排序号")
	private Integer sort;

    @ApiModelProperty("状态：0-禁用，1-启用")
	private Integer status;

	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("创建时间")
	private Date createTime;

	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("更新时间")
	private Date updateTime;



    private String fragmentId;//知识片段ID

} 