package org.jeecg.modules.km.kno.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.common.SignTools;
import org.jeecg.modules.km.kno.entity.*;
import org.jeecg.modules.km.kno.mapper.*;
import org.jeecg.modules.km.kno.vo.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class MyKnowledgeService {

    @Autowired
    private KnoBaseMapper knoBaseMapper;

    @Autowired
    private KnoFavoriteMapper knoFavoriteMapper;

    @Autowired
    private KnoCommentMapper knoCommentMapper;

    @Autowired
    private KnoLogAccessMapper knoLogAccessMapper;

    @Autowired
    private KnoLogDownloadMapper knoLogDownloadMapper;

    @Autowired
    private KnoTagRelationMapper knoTagRelationMapper;

    @Autowired
    private KnoBaseDetailMapper knoBaseDetailMapper;

    @Autowired
    private KnoTagMapper knoTagMapper;


    public IPage<KnoBaseVo> getMyCreatedList(Page<KnoBaseVo> page, String username, String queryKeyword, String sortBy, String sourceType) {
        QueryWrapper<KnoBase> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("creator_name", username);
                //.eq("status", 0);

        if (sourceType != null && !sourceType.isEmpty()) {
            queryWrapper.eq("source_type", sourceType);
        }

        if (queryKeyword != null && !queryKeyword.isEmpty()) {
            queryWrapper.and(wrapper -> wrapper
                    .like("title", queryKeyword)
                    .or()
                    .like("content", queryKeyword)
            );
        }

        if ("latest".equals(sortBy)) {
            queryWrapper.orderByDesc("create_time");
        } else if ("popular".equals(sortBy)) {
            queryWrapper.orderByDesc("view_count");
        }

        Page<KnoBase> knoBasePage = new Page<>(page.getCurrent(), page.getSize());
        IPage<KnoBase> knoBaseIPage = knoBaseMapper.selectPage(knoBasePage, queryWrapper);
        return knoBaseIPage.convert(this::convertToKnoBaseVo);
    }


    public IPage<KnoBaseVo> getMyPublishedList(Page<KnoBaseVo> page, String username) {
        QueryWrapper<KnoBase> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("creator_name", username)
                .in("status", 1, 2);
        Page<KnoBase> knoBasePage = new Page<>(page.getCurrent(), page.getSize());
        IPage<KnoBase> knoBaseIPage = knoBaseMapper.selectPage(knoBasePage, queryWrapper);
        return knoBaseIPage.convert(this::convertToKnoBaseVo);
    }


    public IPage<KnoFavoriteVo> getMyFavoritesList(Page<KnoFavoriteVo> page, String userId) {
        QueryWrapper<KnoFavorite> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        Page<KnoFavorite> knoFavoritePage = new Page<>(page.getCurrent(), page.getSize());
        IPage<KnoFavorite> knoFavoriteIPage = knoFavoriteMapper.selectPage(knoFavoritePage, queryWrapper);
        return knoFavoriteIPage.convert(this::convertToKnoFavoriteVo);
    }


    public IPage<KnoCommentVo> getMyCommentsList(Page<KnoCommentVo> page, String userId) {
        QueryWrapper<KnoComment> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        Page<KnoComment> knoCommentPage = new Page<>(page.getCurrent(), page.getSize());
        IPage<KnoComment> knoCommentIPage = knoCommentMapper.selectPage(knoCommentPage, queryWrapper);
        return knoCommentIPage.convert(this::convertToKnoCommentVo);
    }


    public IPage<KnoLogAccessVo> getMyVisitedList(Page<KnoLogAccessVo> page, String userId) {
        QueryWrapper<KnoLogAccess> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId);
        Page<KnoLogAccess> knoLogAccessPage = new Page<>(page.getCurrent(), page.getSize());
        IPage<KnoLogAccess> knoLogAccessIPage = knoLogAccessMapper.selectPage(knoLogAccessPage, queryWrapper);
        return knoLogAccessIPage.convert(this::convertToKnoLogAccessVo);
    }


    public IPage<KnoLogDownloadVo> getMyDownloadsList(Page<KnoLogDownloadVo> page, String userId) {

        IPage<KnoLogDownloadVo>  pageList = knoLogDownloadMapper.selectUniqueDownloadsByUser(page, userId);
        return pageList.convert(this::convertToKnoLogDownloadVo);
    }


    @Transactional(rollbackFor = Exception.class)
    public void favorite(String knowledgeId, String userId) {
        // 检查是否已收藏
        QueryWrapper<KnoFavorite> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("knowledge_id", knowledgeId)
                   .eq("user_id", userId);
        KnoFavorite existingFavorite = knoFavoriteMapper.selectOne(queryWrapper);

        if (existingFavorite != null) {
            // 已收藏，更新收藏时间
            existingFavorite.setCreateTime(new Date());
            knoFavoriteMapper.updateById(existingFavorite);
        } else {
            // 未收藏，创建新记录
            KnoFavorite newFavorite = new KnoFavorite();
            newFavorite.setKnowledgeId(knowledgeId);
            newFavorite.setUserId(userId);
            newFavorite.setCreateTime(new Date());
            knoFavoriteMapper.insert(newFavorite);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void unfavorite(String knowledgeId, String userId) {
        QueryWrapper<KnoFavorite> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("knowledge_id", knowledgeId)
                   .eq("user_id", userId);
        knoFavoriteMapper.delete(queryWrapper);
    }

    public boolean checkFavorite(String knowledgeId, String userId) {
        QueryWrapper<KnoFavorite> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("knowledge_id", knowledgeId)
                   .eq("user_id", userId);
        return knoFavoriteMapper.selectCount(queryWrapper) > 0;
    }

    @Transactional(rollbackFor = Exception.class)
    public void addComment(KnoComment comment) {
        comment.setCreateTime(new Date());
        comment.setParentId("0");//一级评论
        knoCommentMapper.insert(comment);
    }

    @Transactional(rollbackFor = Exception.class)
    public void replyComment(KnoComment comment) {
        // 检查父评论是否存在
        KnoComment parentComment = knoCommentMapper.selectById(comment.getParentId());
        if (parentComment == null) {
            throw new RuntimeException("父评论不存在");
        }
        comment.setCreateTime(new Date());
        knoCommentMapper.insert(comment);
    }


    public void deleteComment(String id, String userId) {
        // 检查评论是否存在
        KnoComment comment = knoCommentMapper.selectById(id);
        if (comment == null) {
            throw new RuntimeException("评论不存在");
        }
        // 检查评论是否属于当前用户
        if (!comment.getUserId().equals(userId)) {
            throw new RuntimeException("您无权删除此评论");
        }
        //判断是否存在子评论
        QueryWrapper<KnoComment> childWrapper = new QueryWrapper<>();
        childWrapper.eq("parent_id", id);
        List<KnoComment> childList = knoCommentMapper.selectList(childWrapper);
        if (childList != null && !childList.isEmpty()) {
            //删除子评论
            for (KnoComment child : childList) {
                knoCommentMapper.deleteById(child.getId());
            }
        }
        knoCommentMapper.deleteById(id);
    }


    public void updateComment(KnoComment comment) {
        knoCommentMapper.updateById(comment);
    }


    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        // 检查知识状态
        KnoBase knoBase = knoBaseMapper.selectById(id);
        if (knoBase == null) {
            throw new RuntimeException("知识不存在");
        }
        if (knoBase.getStatus() != 0) {
            throw new RuntimeException("只有草稿状态的知识可以删除");
        }

        // 删除知识详情
        QueryWrapper<KnoBaseDetail> detailQueryWrapper = new QueryWrapper<>();
        detailQueryWrapper.eq("knowledge_id", id);
        knoBaseDetailMapper.delete(detailQueryWrapper);

        // 删除标签关联
        QueryWrapper<KnoTagRelation> tagQueryWrapper = new QueryWrapper<>();
        tagQueryWrapper.eq("knowledge_id", id);
        knoTagRelationMapper.delete(tagQueryWrapper);

        // 删除收藏记录
        QueryWrapper<KnoFavorite> favoriteQueryWrapper = new QueryWrapper<>();
        favoriteQueryWrapper.eq("knowledge_id", id);
        knoFavoriteMapper.delete(favoriteQueryWrapper);

        // 删除评论
        QueryWrapper<KnoComment> commentQueryWrapper = new QueryWrapper<>();
        commentQueryWrapper.eq("knowledge_id", id);
        knoCommentMapper.delete(commentQueryWrapper);

        // 删除访问记录
        QueryWrapper<KnoLogAccess> accessQueryWrapper = new QueryWrapper<>();
        accessQueryWrapper.eq("knowledge_id", id);
        knoLogAccessMapper.delete(accessQueryWrapper);

        // 删除下载记录
        QueryWrapper<KnoLogDownload> downloadQueryWrapper = new QueryWrapper<>();
        downloadQueryWrapper.eq("knowledge_id", id);
        knoLogDownloadMapper.delete(downloadQueryWrapper);

        // 删除知识
        knoBaseMapper.deleteById(id);
    }


    /**
     * 发布知识
     * created by yf
     * date 2025-04-29
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void publish(String id) {
        // 检查知识状态
        KnoBase knoBase = knoBaseMapper.selectById(id);
        if (knoBase == null) {
            throw new RuntimeException("知识不存在");
        }
        if (knoBase.getStatus() != 0) {
            throw new RuntimeException("只有草稿状态的知识可以发布");
        }

        // 检查必填字段
        if (knoBase.getTitle() == null || knoBase.getTitle().isEmpty()) {
            throw new RuntimeException("标题不能为空");
        }
        if (knoBase.getContent() == null || knoBase.getContent().isEmpty()) {
            throw new RuntimeException("内容不能为空");
        }
        if (knoBase.getMainFilePath() == null || knoBase.getMainFilePath().isEmpty()) {
            throw new RuntimeException("主文件不能为空");
        }

        // 检查知识详情
        QueryWrapper<KnoBaseDetail> detailQueryWrapper = new QueryWrapper<>();
        detailQueryWrapper.eq("knowledge_id", id);
        List<KnoBaseDetail> detailList = knoBaseDetailMapper.selectList(detailQueryWrapper);
        if (detailList == null || detailList.isEmpty()) {
            throw new RuntimeException("请添加知识详情");
        }

        for (KnoBaseDetail detail : detailList) {
            if (detail.getTitle() == null || detail.getTitle().isEmpty()) {
                throw new RuntimeException("详情标题不能为空");
            }
            if (detail.getContent() == null || detail.getContent().toString().isEmpty()) {
                throw new RuntimeException("详情内容不能为空");
            }
            if (detail.getSingleImageFilePath() == null || detail.getSingleImageFilePath().isEmpty()) {
                throw new RuntimeException("详情图片不能为空");
            }
        }

        // 更新知识状态为已发布
        knoBase.setStatus(1);
        knoBase.setUpdateTime(new Date());
        knoBaseMapper.updateById(knoBase);

        // 更新 knoBaseDetail 的状态为已发布
        for (KnoBaseDetail detail : detailList) {
            detail.setStatus(1);
            detail.setUpdateTime(new Date());
            knoBaseDetailMapper.updateById(detail);
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public void addAccessLog(String knowledgeId, String userId, String ip) {

        //更新knobase 的 viewCount字段
        KnoBase knoBase = knoBaseMapper.selectById(knowledgeId);
        knoBase.setViewCount(knoBase.getViewCount() + 1);
        knoBaseMapper.updateById(knoBase);

        //插入访问记录
        KnoLogAccess logAccess = new KnoLogAccess();
        logAccess.setKnowledgeId(knowledgeId);
        logAccess.setUserId(userId);
        logAccess.setIpAddress(ip);
        logAccess.setAccessTime(new Date());
        knoLogAccessMapper.insert(logAccess);
    }

    @Transactional(rollbackFor = Exception.class)
    public void addDownloadLog(String knowledgeId, String userId, String ip) {
        KnoLogDownload logDownload = new KnoLogDownload();
        logDownload.setKnowledgeId(knowledgeId);
        logDownload.setUserId(userId);
        logDownload.setIpAddress(ip);
        logDownload.setAccessTime(new Date());
        knoLogDownloadMapper.insert(logDownload);
    }

    private KnoBaseVo convertToKnoBaseVo(KnoBase knoBase) {
        KnoBaseVo vo = new KnoBaseVo();
        // 复制基本属性
        org.springframework.beans.BeanUtils.copyProperties(knoBase, vo);

        // 查询并设置知识详情
        QueryWrapper<KnoBaseDetail> detailQueryWrapper = new QueryWrapper<>();
        detailQueryWrapper.eq("knowledge_id", knoBase.getId());
        List<KnoBaseDetail> detailList = knoBaseDetailMapper.selectList(detailQueryWrapper);
        if (detailList != null && !detailList.isEmpty()) {
            List<KnoBaseDetailVo> detailVoList = new ArrayList<>();
            for (KnoBaseDetail detail : detailList) {
                KnoBaseDetailVo detailVo = new KnoBaseDetailVo();
                // 复制detail 到 detailVo
                BeanUtils.copyProperties(detail, detailVo);
                //对图片路径进行签名 yf add 20250618
                String singleImageFilePath = detail.getSingleImageFilePath();
                if(singleImageFilePath != null && !singleImageFilePath.isEmpty()){
                    String singleImageFilePathSingned = SignTools.generateSignedUrl(singleImageFilePath);
                    //detail.setSingleImageFilePath(singleImageFilePathSingned);  
                    detailVo.setSingleImageFilePathForView(singleImageFilePathSingned);
                }
                
                detailVoList.add(detailVo);
            }
            vo.setDetailList(detailVoList);
        }

        // 查询并设置标签
        QueryWrapper<KnoTagRelation> tagQueryWrapper = new QueryWrapper<>();
        tagQueryWrapper.eq("knowledge_id", knoBase.getId());
        List<KnoTagRelation> tagRelations = knoTagRelationMapper.selectList(tagQueryWrapper);
        vo.setKnoTagRelationList(tagRelations);

        // 查询knoTagList
        if (tagRelations != null && !tagRelations.isEmpty()) {
            List<String> tagIds = tagRelations.stream().map(KnoTagRelation::getTagId).collect(Collectors.toList());
            if (!tagIds.isEmpty()) {
                List<KnoTag> tagList = knoTagMapper.selectBatchIds(tagIds);
                vo.setKnoTagList(tagList);
            }
        }

        return vo;
    }

    private KnoFavoriteVo convertToKnoFavoriteVo(KnoFavorite knoFavorite) {
        KnoFavoriteVo vo = new KnoFavoriteVo();
        // 复制基本属性
        vo.setId(knoFavorite.getId());
        vo.setUserId(knoFavorite.getUserId());
        vo.setKnowledgeId(knoFavorite.getKnowledgeId());
        vo.setCreateTime(knoFavorite.getCreateTime());

        // 查询并设置知识信息
        KnoBase knoBase = knoBaseMapper.selectById(knoFavorite.getKnowledgeId());
        if (knoBase != null) {
            vo.setKnoBase(convertToKnoBaseVo(knoBase));
        }

        return vo;
    }

    private KnoCommentVo convertToKnoCommentVo(KnoComment knoComment) {
        KnoCommentVo vo = new KnoCommentVo();
        // 复制基本属性
        vo.setId(knoComment.getId());
        vo.setUserId(knoComment.getUserId());
        vo.setKnowledgeId(knoComment.getKnowledgeId());
        vo.setContent(knoComment.getContent().toString());
        vo.setCreateTime(knoComment.getCreateTime());

        // 查询并设置知识信息
        KnoBase knoBase = knoBaseMapper.selectById(knoComment.getKnowledgeId());
        if (knoBase != null) {
            vo.setKnoBase(convertToKnoBaseVo(knoBase));
        }

        return vo;
    }

    private KnoLogAccessVo convertToKnoLogAccessVo(KnoLogAccess knoLogAccess) {
        KnoLogAccessVo vo = new KnoLogAccessVo();
        // 复制基本属性
        vo.setId(knoLogAccess.getId());
        vo.setUserId(knoLogAccess.getUserId());
        vo.setKnowledgeId(knoLogAccess.getKnowledgeId());
        vo.setCreateTime(knoLogAccess.getAccessTime());

        // 查询并设置知识信息
        KnoBase knoBase = knoBaseMapper.selectById(knoLogAccess.getKnowledgeId());
        if (knoBase != null) {
            vo.setKnoBase(convertToKnoBaseVo(knoBase));
        }

        return vo;
    }

    private KnoLogDownloadVo convertToKnoLogDownloadVo(KnoLogDownloadVo knoLogDownload) {
        KnoLogDownloadVo vo = new KnoLogDownloadVo();
        // 复制基本属性
        org.springframework.beans.BeanUtils.copyProperties(knoLogDownload, vo);

        // 查询并设置知识信息
        KnoBase knoBase = knoBaseMapper.selectById(knoLogDownload.getKnowledgeId());
        if (knoBase != null) {
            vo.setKnoBase(convertToKnoBaseVo(knoBase));
        }

        return vo;
    }
} 