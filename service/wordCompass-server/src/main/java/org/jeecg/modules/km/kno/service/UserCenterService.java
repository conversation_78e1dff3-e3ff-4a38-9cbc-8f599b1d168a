package org.jeecg.modules.km.kno.service;

import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.PasswordUtil;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 用户中心服务
 */
@Service
public class UserCenterService {

    @Autowired
    private ISysUserService sysUserService;


    /**
     * 获取用户信息
     */
    public Result<SysUser> getUserInfo(String userId) {
        try {
            if (!StringUtils.hasText(userId)) {
                return Result.error("用户ID不能为空");
            }

            SysUser user = sysUserService.getById(userId);
            if (user == null) {
                return Result.error("用户不存在");
            }

            // 敏感信息处理
            user.setPassword(null);
            return Result.OK("获取成功", user);
        } catch (Exception e) {
            return Result.error("获取用户信息失败: " + e.getMessage());
        }
    }

    /**
     * 更新用户信息
     */
    public Result<SysUser> updateUserInfo(SysUser sysUser) {
        try {
            // 参数验证
            if (sysUser == null || sysUser.getId() == null) {
                return Result.error("用户信息不能为空");
            }

            // 获取现有用户信息
            SysUser existingUser = sysUserService.getById(sysUser.getId());
            if (existingUser == null) {
                return Result.error("用户不存在");
            }

            // 更新指定字段
            if (StringUtils.hasText(sysUser.getRealname())) {
                existingUser.setRealname(sysUser.getRealname());
            }
            if (sysUser.getBirthday() != null) {
                existingUser.setBirthday(sysUser.getBirthday());
            }
            if (sysUser.getSex() != null) {
                existingUser.setSex(sysUser.getSex());
            }
            if (StringUtils.hasText(sysUser.getEmail())) {
                existingUser.setEmail(sysUser.getEmail());
            }
            if (StringUtils.hasText(sysUser.getPhone())) {
                existingUser.setPhone(sysUser.getPhone());
            }

            // 执行更新
            boolean success = sysUserService.updateById(existingUser);
            if (success) {
                return Result.OK("更新成功", existingUser);
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 更新用户头像
     */
    public Result<String> updateUserAvatar(String userId, String avatar) {
        try {
            // 参数验证
            if (!StringUtils.hasText(userId) || !StringUtils.hasText(avatar)) {
                return Result.error("参数不能为空");
            }

            // 获取用户信息
            SysUser user = sysUserService.getById(userId);
            if (user == null) {
                return Result.error("用户不存在");
            }

            // 更新头像
            user.setAvatar(avatar);
            boolean success = sysUserService.updateById(user);
            if (success) {
                return Result.OK("头像更新成功", avatar);
            } else {
                return Result.error("头像更新失败");
            }
        } catch (Exception e) {
            return Result.error("头像更新失败: " + e.getMessage());
        }
    }

    /**
     * 修改密码
     */
    public Result<String> changePassword(String userId, String oldPassword, String newPassword) {
        try {
            // 参数验证
            if (!StringUtils.hasText(userId) || !StringUtils.hasText(oldPassword) || !StringUtils.hasText(newPassword)) {
                return Result.error("参数不能为空");
            }

            // 获取用户信息
            SysUser user = sysUserService.getById(userId);
            if (user == null) {
                return Result.error("用户不存在");
            }

            // 验证旧密码
            String userpassword = PasswordUtil.encrypt(user.getUsername(), oldPassword, user.getSalt());
            if (!userpassword.equals(user.getPassword())) {
                return Result.error("旧密码错误");
            }

            // 更新密码
            String salt = oConvertUtils.randomGen(8);
            user.setSalt(salt);
            String passwordEncode = PasswordUtil.encrypt(user.getUsername(), newPassword, salt);
            user.setPassword(passwordEncode);
            boolean success = sysUserService.updateById(user);
            if (success) {
                return Result.OK("密码修改成功");
            } else {
                return Result.error("密码修改失败");
            }
        } catch (Exception e) {
            return Result.error("密码修改失败: " + e.getMessage());
        }
    }
} 