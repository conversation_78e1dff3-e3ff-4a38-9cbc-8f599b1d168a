package org.jeecg.modules.word.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.word.entity.WcWord;
import org.jeecg.modules.word.service.IWcWordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.multipart.MultipartFile;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: 单词信息管理
 * @Author: wordCompass
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Api(tags = "单词信息管理")
@RestController
@RequestMapping("/word/wcword")
@Slf4j
public class WcWordController extends JeecgController<WcWord, IWcWordService> {

    @Autowired
    private IWcWordService wcWordService;

    /**
     * 分页列表查询
     */
    @ApiOperation(value = "单词信息-分页列表查询", notes = "单词信息-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(WcWord wcWord,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<WcWord> queryWrapper = QueryGenerator.initQueryWrapper(wcWord, req.getParameterMap());
        queryWrapper.orderByAsc("sort_order").orderByDesc("create_time");
        Page<WcWord> page = new Page<WcWord>(pageNo, pageSize);
        IPage<WcWord> pageList = wcWordService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     */
    @ApiOperation(value = "单词信息-添加", notes = "单词信息-添加")
    @PostMapping(value = "/add")
    @AutoLog(value = "单词信息-添加")
    public Result<?> add(@RequestBody WcWord wcWord) {
        wcWordService.save(wcWord);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     */
    @ApiOperation(value = "单词信息-编辑", notes = "单词信息-编辑")
    @PutMapping(value = "/edit")
    @AutoLog(value = "单词信息-编辑")
    public Result<?> edit(@RequestBody WcWord wcWord) {
        wcWordService.updateById(wcWord);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     */
    @ApiOperation(value = "单词信息-通过id删除", notes = "单词信息-通过id删除")
    @DeleteMapping(value = "/delete")
    @AutoLog(value = "单词信息-删除")
    public Result<?> delete(@RequestParam(name = "id") String id) {
        wcWordService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     */
    @ApiOperation(value = "单词信息-批量删除", notes = "单词信息-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    @AutoLog(value = "单词信息-批量删除")
    public Result<?> deleteBatch(@RequestParam(name = "ids") String ids) {
        this.wcWordService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     */
    @ApiOperation(value = "单词信息-通过id查询", notes = "单词信息-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id") String id) {
        WcWord wcWord = wcWordService.getById(id);
        if (wcWord == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(wcWord);
    }

    /**
     * 导出excel
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, WcWord wcWord) {
        return super.exportXls(request, wcWord, WcWord.class, "单词信息");
    }

    /**
     * 通过excel导入数据
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, WcWord.class);
    }

    /**
     * Excel批量导入单词
     */
    @ApiOperation(value = "Excel批量导入单词", notes = "Excel批量导入单词")
    @PostMapping(value = "/importWordsFromExcel")
    @AutoLog(value = "Excel批量导入单词")
    public Result<?> importWordsFromExcel(@RequestParam("file") MultipartFile file,
                                          @RequestParam("textbookId") String textbookId) {
        try {
            String result = wcWordService.importWordsFromExcel(file, textbookId);
            return Result.OK(result);
        } catch (Exception e) {
            log.error("Excel导入单词失败", e);
            return Result.error("导入失败：" + e.getMessage());
        }
    }

    /**
     * 文本批量导入单词
     */
    @ApiOperation(value = "文本批量导入单词", notes = "文本批量导入单词")
    @PostMapping(value = "/importWordsFromText")
    @AutoLog(value = "文本批量导入单词")
    public Result<?> importWordsFromText(@RequestParam("content") String content,
                                         @RequestParam("textbookId") String textbookId) {
        try {
            String result = wcWordService.importWordsFromText(content, textbookId);
            return Result.OK(result);
        } catch (Exception e) {
            log.error("文本导入单词失败", e);
            return Result.error("导入失败：" + e.getMessage());
        }
    }

    /**
     * 根据教材ID查询单词列表
     */
    @ApiOperation(value = "根据教材ID查询单词列表", notes = "根据教材ID查询单词列表")
    @GetMapping(value = "/listByTextbookId")
    public Result<?> listByTextbookId(@RequestParam(name = "textbookId") String textbookId) {
        List<WcWord> list = wcWordService.getWordsByTextbookId(textbookId);
        return Result.OK(list);
    }

    /**
     * 根据单元ID查询单词列表
     */
    @ApiOperation(value = "根据单元ID查询单词列表", notes = "根据单元ID查询单词列表")
    @GetMapping(value = "/listByUnitId")
    public Result<?> listByUnitId(@RequestParam(name = "unitId") String unitId) {
        List<WcWord> list = wcWordService.getWordsByUnitId(unitId);
        return Result.OK(list);
    }

    /**
     * 根据章节ID查询单词列表
     */
    @ApiOperation(value = "根据章节ID查询单词列表", notes = "根据章节ID查询单词列表")
    @GetMapping(value = "/listByChapterId")
    public Result<?> listByChapterId(@RequestParam(name = "chapterId") String chapterId) {
        List<WcWord> list = wcWordService.getWordsByChapterId(chapterId);
        return Result.OK(list);
    }

    /**
     * 根据单词名称模糊查询
     */
    @ApiOperation(value = "根据单词名称模糊查询", notes = "根据单词名称模糊查询")
    @GetMapping(value = "/searchByName")
    public Result<?> searchByName(@RequestParam(name = "wordName") String wordName) {
        List<WcWord> list = wcWordService.searchWordsByName(wordName);
        return Result.OK(list);
    }

} 