package org.jeecg.modules.km.kno.service;

import org.jeecg.common.system.query.QueryGenerator;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.jeecg.modules.km.kno.entity.KnoReview;
import org.jeecg.modules.km.kno.mapper.KnoReviewMapper;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import cn.hutool.core.util.StrUtil;

import org.jeecg.common.system.query.QueryGenerator;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.Map;
import java.util.List;

@Service
//知识审核
public class KnoReviewService {

    @Autowired
    private KnoReviewMapper knoReviewMapper;


	// 添加知识审核
	public KnoReview saveOrUpdateKnoReview(KnoReview knoReview) {
	    if(StrUtil.isEmpty(knoReview.getId())){
	    	  knoReviewMapper.insert(knoReview);
	    }else{
	          knoReviewMapper.updateById(knoReview);
	    }
		return knoReview;
	}



	// 删除知识审核
	public void deleteKnoReview(String id) {
		 knoReviewMapper.deleteById(id);
	}


	// 删除知识审核
	public void deleteKnoReviewByIds(String ids) {
	    List<String> idsList = StrUtil.split(ids, ',');
	    for(String id : idsList){
	    	 deleteKnoReview(id);
	    }
	}



	// 查询知识审核
	public KnoReview findKnoReviewById(String id) {
		KnoReview knoReview = knoReviewMapper.selectById(id);
		return knoReview;
	}


	// 分页查询知识审核
	public IPage<KnoReview> findKnoReviewByPage(KnoReview knoReview, Integer pageNo,
	    Integer pageSize,Map<String, String[]> parameterMap) {
			QueryWrapper<KnoReview> queryWrapper = QueryGenerator.initQueryWrapper(knoReview,parameterMap);
        	Page<KnoReview> page = new Page<KnoReview>(pageNo, pageSize);
        	IPage<KnoReview> pageList = knoReviewMapper.selectPage(page, queryWrapper);
            return pageList;
	}



    // 分页查询知识审核 Controller 构建条件
    public IPage<KnoReview> findKnoReviews(Page<KnoReview> page, QueryWrapper<KnoReview> queryWrapper) {
        return knoReviewMapper.selectPage(page, queryWrapper);
    }
}
