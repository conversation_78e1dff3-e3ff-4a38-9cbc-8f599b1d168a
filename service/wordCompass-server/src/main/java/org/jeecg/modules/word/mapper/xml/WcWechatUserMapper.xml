<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.word.mapper.WcWechatUserMapper">

    <!-- 根据OpenID查询微信用户 -->
    <select id="selectByOpenid" resultType="org.jeecg.modules.word.entity.WcWechatUser">
        SELECT * FROM wc_wechat_user 
        WHERE openid = #{openid} 
        AND del_flag = 0
    </select>
    
    <!-- 根据UnionID查询微信用户 -->
    <select id="selectByUnionid" resultType="org.jeecg.modules.word.entity.WcWechatUser">
        SELECT * FROM wc_wechat_user 
        WHERE unionid = #{unionid} 
        AND del_flag = 0
    </select>
    
    <!-- 根据关联账号ID查询微信用户列表 -->
    <select id="selectByAccountId" resultType="org.jeecg.modules.word.entity.WcWechatUser">
        SELECT * FROM wc_wechat_user 
        WHERE account_id = #{accountId} 
        AND del_flag = 0
        ORDER BY create_time DESC
    </select>

</mapper>
