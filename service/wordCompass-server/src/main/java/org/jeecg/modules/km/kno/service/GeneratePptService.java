package org.jeecg.modules.km.kno.service;

import com.aspose.slides.*;
import org.jeecg.modules.km.kno.entity.PptTemplate;
import org.jeecg.modules.km.kno.entity.PptTemplateLayout;
import org.jeecg.modules.km.kno.mapper.PptTemplateLayoutMapper;
import org.jeecg.modules.km.kno.mapper.PptTemplateMapper;
import org.jeecg.modules.km.kno.vo.KnoBaseAiSynopsisVo;
import org.jeecg.modules.microsoft.AsposeUtils;
import org.jeecg.modules.microsoft.MicrosoftConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

// 生成PPT服务
// 1. 根据PPT模板生成PPT
// 2. 根据PPT模板生成PPT的布局
// 3. 根据PPT模板生成PPT的样式
// 4. 根据PPT模板生成PPT的动画
// 5. 根据PPT模板生成PPT的图片
// 6. 根据PPT模板生成PPT的文字
/**
 * 生成PPT服务
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-30
 */
@Service
public class GeneratePptService {

    @Autowired
    private PptTemplateMapper pptTemplateMapper;

    @Autowired
    private PptTemplateLayoutMapper pptTemplateLayoutMapper;

    @Value("${jeecg.path.upload}")
    private String uploadPath;

    @Value("${jeecg.path.uploadTmp}")
    private String uploadTmpPath;





    /**
     * 基于PPT模板生成新的PPT文件
     * 主要是 替换 占位符 {{title}} {{content}} 和 随机布局
     * 
     * @param pptTemplate PPT模板信息
     * @param synopsisList 大纲数据列表
     * @param knowledgeId 知识ID
     * @param username 用户名
     * @return 生成的PPT文件路径
     */
    public String generatePptFromTemplate(PptTemplate pptTemplate, List<KnoBaseAiSynopsisVo> synopsisList, String knowledgeId, String username) {
        try {
            // 加载Aspose许可证
            AsposeUtils.loadLicense(MicrosoftConstants.PPTX_TO_OTHER);
            
            // 获取模板文件路径
            String templateFilePath = uploadPath + pptTemplate.getFilePath();
            File templateFile = new File(templateFilePath);
            if (!templateFile.exists()) {
                throw new RuntimeException("PPT模板文件不存在: " + templateFilePath);
            }
            
            // 加载模板PPT
            Presentation templatePresentation = new Presentation(templateFilePath);
            
            // 创建新的PPT
            Presentation newPresentation = new Presentation();
            // 设置幻灯片大小为宽屏
            newPresentation.getSlideSize().setSize(SlideSizeType.Widescreen, SlideSizeScaleType.Maximize);
            // 删除默认的第一页
            newPresentation.getSlides().remove(newPresentation.getSlides().get_Item(0));
            
            // 获取模板的布局信息
            List<PptTemplateLayout> layoutList = pptTemplateLayoutMapper.selectByTemplateId(pptTemplate.getId());
            if (layoutList == null || layoutList.isEmpty()) {
                throw new RuntimeException("PPT模板布局信息不存在");
            }
            
            // 按布局类型分组，支持同类型多个布局
            Map<String, List<PptTemplateLayout>> layoutTypeMap = new HashMap<>();
            for (PptTemplateLayout layout : layoutList) {
                layoutTypeMap.computeIfAbsent(layout.getLayoutType(), k -> new ArrayList<>()).add(layout);
            }
            java.util.Random random = new java.util.Random();
            // 遍历大纲数据，为每个大纲项生成对应的PPT页面
            for (KnoBaseAiSynopsisVo synopsis : synopsisList) {
                String layoutType = synopsis.getType();
                List<PptTemplateLayout> layouts = layoutTypeMap.get(layoutType);
                PptTemplateLayout layout = null;
                if (layouts != null && !layouts.isEmpty()) {
                    // 随机选取一个布局
                    layout = layouts.get(random.nextInt(layouts.size()));
                } else {
                    // 如果没有找到对应的布局类型，使用默认的内容页布局
                    List<PptTemplateLayout> defaultLayouts = layoutTypeMap.get("content");
                    if (defaultLayouts != null && !defaultLayouts.isEmpty()) {
                        layout = defaultLayouts.get(random.nextInt(defaultLayouts.size()));
                    } else {
                        // 如果连内容页布局都没有，跳过这个大纲项
                        continue;
                    }
                }
                // 获取模板中对应页面的索引（pageIndex从1开始，但数组索引从0开始）
                int templatePageIndex = layout.getPageIndex() - 1;
                if (templatePageIndex < 0 || templatePageIndex >= templatePresentation.getSlides().size()) {
                    // 页面索引超出范围，跳过
                    continue;
                }
                // 从模板中复制对应的页面
                ISlide templateSlide = templatePresentation.getSlides().get_Item(templatePageIndex);
                ISlide newSlide = newPresentation.getSlides().addClone(templateSlide);
                // 替换页面中的占位符文本
                replaceSlideText(newSlide, synopsis);
            }
            // 生成新的PPT文件路径
            String timeStamp = new java.text.SimpleDateFormat("yyyyMMddHHmmssSSS").format(new java.util.Date());
            int randomNum = (int) (Math.random() * 10000);
            String fileName = knowledgeId + "_" + timeStamp + "_" + randomNum + ".pptx";
            String fileFullPath = uploadTmpPath + fileName;
            // 保存新的PPT文件
            newPresentation.save(fileFullPath, SaveFormat.Pptx);
            // 释放资源
            templatePresentation.dispose();
            newPresentation.dispose();
            return fileName;
        } catch (Exception e) {
            throw new RuntimeException("生成PPT文件失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 替换幻灯片中的占位符文本
     * 
     * @param slide 幻灯片对象
     * @param synopsis 大纲数据
     */
    private void replaceSlideText(ISlide slide, KnoBaseAiSynopsisVo synopsis) {
        try {
            // 目录类型特殊处理
            boolean isDirectory = "directory".equals(synopsis.getType());
            List<String> directoryItems = null;
            if (isDirectory) {
                directoryItems = splitDirectoryContent(synopsis.getContent());
            }
            // 遍历幻灯片中的所有形状
            for (IShape shape : slide.getShapes()) {
                if (shape instanceof IAutoShape) { // 只处理自动形状
                    IAutoShape autoShape = (IAutoShape) shape;
                    ITextFrame textFrame = autoShape.getTextFrame();
                    if (textFrame != null) {
                        String text = textFrame.getText();
                        String newText = text;
                        // 目录类型：依次替换{{content1}}、{{content2}}等  序号类型：依次替换{{orderNum1}}、{{orderNum2}}等
                        if (isDirectory && directoryItems != null) {
                            for (int i = 0; i < 20; i++) { // 最多支持20个目录项
                                String placeholder = "{{content" + (i + 1) + "}}";
                                if (newText.contains(placeholder)) {
                                    String value = i < directoryItems.size() ? directoryItems.get(i) : "";
                                    newText = newText.replace(placeholder, value);
                                }
                                // 序号类型：依次替换{{orderNum1}}、{{orderNum2}}等
                                String orderNumPlaceholder = "{{orderNum" + (i + 1) + "}}";
                                if (newText.contains(orderNumPlaceholder)) {
                                    String value = i < directoryItems.size() ? String.valueOf(i + 1) : "";
                                    newText = newText.replace(orderNumPlaceholder, value);
                                }
                            }
                        }
                        // 兼容原有整体内容替换
                        if (text.contains("{{content}}") || text.contains("{{CONTENT}}")) {
                            String content = synopsis.getContent() != null ? synopsis.getContent() : "";
                            if (isDirectory) {
                                // 目录类型整体内容，合并为多行
                                content = String.join("\n", directoryItems);
                            } else {
                                content = content.replace("\\n", "\n");
                            }
                            newText = newText.replace("{{content}}", content)
                                             .replace("{{CONTENT}}", content);
                        }
                        // 替换标题占位符
                        if (text.contains("{{title}}") || text.contains("{{TITLE}}")) {
                            newText = newText.replace("{{title}}", synopsis.getTitle() != null ? synopsis.getTitle() : "")
                                             .replace("{{TITLE}}", synopsis.getTitle() != null ? synopsis.getTitle() : "");
                        }
                        // 如果文本发生了变化，更新文本框
                        if (!text.equals(newText)) {
                            textFrame.setText(newText);
                        }
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("替换幻灯片文本时出错: " + e.getMessage());
        }
    }
    
    /**
     * 拆分目录内容为多条数据
     * 支持- 、\n、逗号、分号、顿号、竖线等分隔
     */
    private List<String> splitDirectoryContent(String content) {
        List<String> result = new ArrayList<>();
        if (content == null || content.trim().isEmpty()) return result;
        // 优先按\n分割
        String[] lines = content.split("-");
        for (String line : lines) {
            String l = line.trim();
            // 把 \\n 替换为空
            l = l.replace("\\n", "");
            if (l.startsWith("- ")) {
                l = l.substring(2).trim();
            }
            if(!l.isEmpty()){
                result.add(l);
            }
            
        }
        return result;
    }


}
