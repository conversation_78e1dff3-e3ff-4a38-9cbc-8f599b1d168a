package org.jeecg.modules.word.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.word.entity.WcTask;
import org.jeecg.modules.word.entity.WcTaskDetail;
import org.jeecg.modules.word.service.WcTaskService;
import org.jeecg.modules.word.vo.WcTaskVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: 任务管理
 * @Author: wordCompass
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "任务管理")
@RestController
@RequestMapping("/word/wctask")
public class WcTaskController extends JeecgController<WcTask, WcTaskService> {

    @Autowired
    private WcTaskService wcTaskService;

    /**
     * 分页列表查询
     */
    @AutoLog(value = "任务管理-分页列表查询")
    @ApiOperation(value = "任务管理-分页列表查询", notes = "任务管理-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<WcTask>> queryPageList(WcTask wcTask,
                                              @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                              @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                              HttpServletRequest req) {
        QueryWrapper<WcTask> queryWrapper = QueryGenerator.initQueryWrapper(wcTask, req.getParameterMap());
        Page<WcTask> page = new Page<>(pageNo, pageSize);
        IPage<WcTask> pageList = wcTaskService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     */
    @AutoLog(value = "任务管理-添加")
    @ApiOperation(value = "任务管理-添加", notes = "任务管理-添加")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody WcTaskVO wcTaskVO) {
        wcTaskService.saveTaskWithDetails(wcTaskVO);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     */
    @AutoLog(value = "任务管理-编辑")
    @ApiOperation(value = "任务管理-编辑", notes = "任务管理-编辑")
    @PostMapping(value = "/edit")
    public Result<String> edit(@RequestBody WcTaskVO wcTaskVO) {
        wcTaskService.updateTaskWithDetails(wcTaskVO);
        return Result.OK("编辑成功！");
    }

    /**
     * 通过id删除
     */
    @AutoLog(value = "任务管理-通过id删除")
    @ApiOperation(value = "任务管理-通过id删除", notes = "任务管理-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        wcTaskService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     */
    @AutoLog(value = "任务管理-批量删除")
    @ApiOperation(value = "任务管理-批量删除", notes = "任务管理-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.wcTaskService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功！");
    }

    /**
     * 通过id查询
     */
    @AutoLog(value = "任务管理-通过id查询")
    @ApiOperation(value = "任务管理-通过id查询", notes = "任务管理-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<WcTaskVO> queryById(@RequestParam(name = "id", required = true) String id) {
        WcTaskVO wcTaskVO = wcTaskService.getTaskVOById(id);
        return Result.OK(wcTaskVO);
    }

    /**
     * 根据用户ID查询任务列表
     */
    @AutoLog(value = "任务管理-根据用户ID查询任务列表")
    @ApiOperation(value = "任务管理-根据用户ID查询任务列表", notes = "任务管理-根据用户ID查询任务列表")
    @GetMapping(value = "/listByAccountId")
    public Result<List<WcTask>> listByAccountId(@RequestParam(name = "accountId", required = true) String accountId) {
        List<WcTask> list = wcTaskService.getTasksByAccountId(accountId);
        return Result.OK(list);
    }

    /**
     * 根据学生ID查询任务列表
     */
    @AutoLog(value = "任务管理-根据学生ID查询任务列表")
    @ApiOperation(value = "任务管理-根据学生ID查询任务列表", notes = "任务管理-根据学生ID查询任务列表")
    @GetMapping(value = "/listByStudentId")
    public Result<List<WcTask>> listByStudentId(@RequestParam(name = "studentId", required = true) String studentId) {
        List<WcTask> list = wcTaskService.getTasksByStudentId(studentId);
        return Result.OK(list);
    }

    /**
     * 根据状态查询任务列表
     */
    @AutoLog(value = "任务管理-根据状态查询任务列表")
    @ApiOperation(value = "任务管理-根据状态查询任务列表", notes = "任务管理-根据状态查询任务列表")
    @GetMapping(value = "/listByStatus")
    public Result<List<WcTask>> listByStatus(@RequestParam(name = "status", required = true) String status) {
        List<WcTask> list = wcTaskService.getTasksByStatus(status);
        return Result.OK(list);
    }

    /**
     * 根据任务类型查询任务列表
     */
    @AutoLog(value = "任务管理-根据任务类型查询任务列表")
    @ApiOperation(value = "任务管理-根据任务类型查询任务列表", notes = "任务管理-根据任务类型查询任务列表")
    @GetMapping(value = "/listByTaskType")
    public Result<List<WcTask>> listByTaskType(@RequestParam(name = "taskType", required = true) String taskType) {
        List<WcTask> list = wcTaskService.getTasksByTaskType(taskType);
        return Result.OK(list);
    }

    /**
     * 根据任务ID查询任务明细
     */
    @AutoLog(value = "任务管理-根据任务ID查询任务明细")
    @ApiOperation(value = "任务管理-根据任务ID查询任务明细", notes = "任务管理-根据任务ID查询任务明细")
    @GetMapping(value = "/detailsByTaskId")
    public Result<List<WcTaskDetail>> detailsByTaskId(@RequestParam(name = "taskId", required = true) String taskId) {
        List<WcTaskDetail> list = wcTaskService.getTaskDetailsByTaskId(taskId);
        return Result.OK(list);
    }

    /**
     * 批改任务
     */
    @AutoLog(value = "任务管理-批改任务")
    @ApiOperation(value = "任务管理-批改任务", notes = "任务管理-批改任务")
    @PostMapping(value = "/correctTask")
    public Result<String> correctTask(@RequestBody WcTaskVO wcTaskVO) {
        wcTaskService.correctTask(wcTaskVO);
        return Result.OK("批改成功！");
    }

    /**
     * 上传作业照片
     */
    @AutoLog(value = "任务管理-上传作业照片")
    @ApiOperation(value = "任务管理-上传作业照片", notes = "任务管理-上传作业照片")
    @PostMapping(value = "/uploadHomeworkPhoto")
    public Result<String> uploadHomeworkPhoto(@RequestParam("file") MultipartFile file, @RequestParam("taskId") String taskId) {
        if (file.isEmpty()) {
            return Result.error("上传文件为空");
        }
        
        try {
            // 获取任务信息
            WcTask wcTask = wcTaskService.getById(taskId);
            if (wcTask == null) {
                return Result.error("任务不存在");
            }
            
            // TODO: 实现文件上传逻辑，保存文件并返回文件路径
            String filePath = "上传的文件路径";
            
            // 更新任务的作业照片字段
            wcTask.setHomeworkPhoto(filePath);
            wcTask.setStatus("finished"); // 更新状态为已完成
            wcTask.setFinishTime(new java.util.Date()); // 设置完成时间
            wcTaskService.updateById(wcTask);
            
            return Result.OK("上传成功", filePath);
        } catch (Exception e) {
            log.error("上传作业照片失败", e);
            return Result.error("上传失败: " + e.getMessage());
        }
    }
}