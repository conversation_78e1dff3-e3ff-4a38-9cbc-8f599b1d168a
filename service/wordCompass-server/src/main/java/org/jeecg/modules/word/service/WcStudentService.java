package org.jeecg.modules.word.service;

import org.jeecg.modules.word.entity.WcStudent;
import org.jeecg.modules.word.mapper.WcStudentMapper;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import java.util.List;

/**
 * @Description: 学生信息
 * @Author: wordCompass
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Slf4j
@Service
public class WcStudentService extends ServiceImpl<WcStudentMapper, WcStudent> {

    @Autowired
    private WcStudentMapper wcStudentMapper;
    
    /**
     * 根据账号ID查询学生列表
     * @param accountId 账号ID
     * @return 学生列表
     */
    public List<WcStudent> getStudentsByAccountId(String accountId) {
        return wcStudentMapper.selectByAccountId(accountId);
    }
    
    /**
     * 根据年级查询学生列表
     * @param grade 年级
     * @return 学生列表
     */
    public List<WcStudent> getStudentsByGrade(String grade) {
        return wcStudentMapper.selectByGrade(grade);
    }
    
    /**
     * 更新学生状态
     * @param id 学生ID
     * @param status 状态值
     * @return 是否更新成功
     */
    public boolean updateStatus(String id, Integer status) {
        WcStudent student = this.getById(id);
        if (student == null) {
            return false;
        }
        student.setStatus(status);
        return this.updateById(student);
    }
    
    /**
     * 更新学生头像
     * @param id 学生ID
     * @param avatar 头像URL
     * @return 是否更新成功
     */
    public boolean updateAvatar(String id, String avatar) {
        WcStudent student = this.getById(id);
        if (student == null) {
            return false;
        }
        student.setAvatar(avatar);
        return this.updateById(student);
    }
    
    /**
     * 检查学生是否存在
     * @param accountId 账号ID
     * @param studentName 学生姓名
     * @return 是否存在
     */
    public boolean checkStudentExists(String accountId, String studentName) {
        QueryWrapper<WcStudent> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("account_id", accountId)
                    .eq("student_name", studentName);
        return this.count(queryWrapper) > 0;
    }
    
    /**
     * 检查学生是否存在（排除自身）
     * @param accountId 账号ID
     * @param studentName 学生姓名
     * @param id 学生ID（排除）
     * @return 是否存在
     */
    public boolean checkStudentExistsExcludeSelf(String accountId, String studentName, String id) {
        QueryWrapper<WcStudent> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("account_id", accountId)
                    .eq("student_name", studentName)
                    .ne("id", id);
        return this.count(queryWrapper) > 0;
    }
}