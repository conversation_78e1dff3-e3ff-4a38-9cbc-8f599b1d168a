package org.jeecg.modules.word.mapper;

import org.jeecg.modules.word.entity.WcTextbookDetail;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * @Description: 教材明细信息
 * @Author: wordCompass
 * @Date: 2025-01-15
 * @Version: V1.0
 */
public interface WcTextbookDetailMapper extends BaseMapper<WcTextbookDetail> {
    
    /**
     * 根据教材ID查询明细列表
     * @param textbookId 教材ID
     * @return 明细列表
     */
    List<WcTextbookDetail> selectByTextbookId(@Param("textbookId") String textbookId);
    
    /**
     * 根据父级ID查询子明细列表
     * @param parentId 父级ID
     * @return 子明细列表
     */
    List<WcTextbookDetail> selectByParentId(@Param("parentId") String parentId);
    
    /**
     * 查询树形结构数据
     * @param textbookId 教材ID
     * @return 树形结构数据
     */
    List<WcTextbookDetail> selectTreeData(@Param("textbookId") String textbookId);
    
} 