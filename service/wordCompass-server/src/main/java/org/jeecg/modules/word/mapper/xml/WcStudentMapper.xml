<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.word.mapper.WcStudentMapper">

    <!-- 根据账号ID查询学生列表 -->
    <select id="selectByAccountId" resultType="org.jeecg.modules.word.entity.WcStudent">
        SELECT * FROM wc_student 
        WHERE account_id = #{accountId} 
        AND del_flag = 0
        ORDER BY create_time DESC
    </select>
    
    <!-- 根据年级查询学生列表 -->
    <select id="selectByGrade" resultType="org.jeecg.modules.word.entity.WcStudent">
        SELECT * FROM wc_student 
        WHERE grade = #{grade} 
        AND del_flag = 0
        ORDER BY student_name ASC
    </select>

</mapper>
