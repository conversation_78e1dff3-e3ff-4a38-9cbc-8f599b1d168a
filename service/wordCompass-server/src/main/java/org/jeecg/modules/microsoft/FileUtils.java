package org.jeecg.modules.microsoft;


import cn.hutool.core.io.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.http.entity.ContentType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Calendar;
import java.util.Date;
import java.util.Objects;


/**
 * 文件上传工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class FileUtils extends FileUtil {

    public static MultipartFile fileToMultipartFile(File file) {
        FileInputStream is = null;
        MultipartFile multipartFile = null;
        try {
            is = new FileInputStream(file);
            multipartFile = new MockMultipartFile(file.getName(), file.getName(), ContentType.APPLICATION_OCTET_STREAM.toString(), is);
        } catch (Exception e) {
            log.error("file to  multipartFile is error", e);

        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    log.error("fileToMultipartFile close file is error");

                }
            }
        }
        return multipartFile;
    }


    /**
     * 获取文件前缀
     * <p>
     * 例如: a.txt, 返回: a
     *
     * @param file 文件
     * @return 前缀（不含".")
     */
    public static String getFilePrefix(MultipartFile file) {
        if (null == file) {
            return StringUtils.EMPTY;
        }
        String fileName = Objects.requireNonNull(file.getOriginalFilename());
        int separatorIndex = fileName.lastIndexOf(".");
        if (separatorIndex < 0) {
            return "";
        }
        return fileName.substring(0, separatorIndex).toLowerCase();
    }


}