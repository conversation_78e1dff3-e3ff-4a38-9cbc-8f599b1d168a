package org.jeecg.modules.km.kno.vo;

/**
 * 知识下载记录VO，包含下载记录及关联知识基础信息
 * creater: yf
 */
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class KnoLogDownloadVo implements Serializable {
    // 下载记录ID
    private String id;
    // 知识ID
    private String knowledgeId;
    // 用户ID
    private String userId;
    // IP地址
    private String ipAddress;
    // 用户代理
    private String userAgent;
    // 访问时间
    private Date accessTime;

    // 关联知识基础表字段
    // 知识基础ID
    private String baseId;
    // 知识标题
    private String baseTitle;
    // 知识内容
    private String baseContent;
    // 识别知识内容
    private String baseOcrContent;
    // 主文档路径
    private String baseMainFilePath;
    // 所属类别ID
    private String baseCategoryId;
    // 所属类别name
    private String baseCategoryName;
    // 关键词，多个用逗号分隔
    private String baseKeywords;
    // 所属部门名称
    private String baseOrganName;
    // 所属部门ID
    private String baseOrganId;

    // 所属部门ID列表，多个用逗号分隔
    private List<String> baseOrganIds;

    // 查看次数
    private Integer baseViewCount;
    // 状态：0-草稿，1-发布，2-归档
    private Integer baseStatus;
    // 来源：1-手动创建，2-导入,3-AI创建
    private Integer baseSourceType;
    // 创建者名称
    private String baseCreatorName;
    // 最后更新者名称
    private String baseUpdaterName;
    // 创建时间
    private Date baseCreateTime;
    // 变更时间
    private Date baseUpdateTime;

    private Date createTime;
    private KnoBaseVo knoBase;
} 