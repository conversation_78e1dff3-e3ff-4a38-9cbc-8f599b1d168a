package org.jeecg.modules.km.kno.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.km.kno.entity.KnoBase;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.jeecg.modules.km.kno.vo.KnoBaseVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

 //知识基础
public interface KnoBaseMapper extends BaseMapper<KnoBase> {

    List<KnoBaseVo> selectLatestWithDetails(int limit);

    List<KnoBaseVo> selectLatestWithDetailsPage(Page<KnoBaseVo> page);

    List<KnoBaseVo> selectLatestWithDetailsPageByDeptIds(Page<KnoBaseVo> page, @Param("knoBaseVo") KnoBaseVo knoBaseVo, @Param("deptIds") List<String> deptIds);


    //查询documentType 为1的 最新知识
    List<KnoBaseVo> selectLatestWithDetailsPage2(Page<KnoBaseVo> page);

    List<KnoBaseVo> selectHotWithDetailsPage(Page<KnoBaseVo> page);

    //查询documentType 为1的 热门知识
    List<KnoBaseVo> selectHotWithDetailsPage2(Page<KnoBaseVo> page);


    List<KnoBaseVo> selectHotWithDetailsPageByDeptIds(Page<KnoBaseVo> page, @Param("knoBaseVo") KnoBaseVo knoBaseVo, @Param("deptIds") List<String> deptIds);

    List<KnoBaseVo> searchKnoBasePage(Page<KnoBaseVo> page, @Param("knoBaseVo") KnoBaseVo knoBaseVo);

    List<KnoBaseVo> searchKnoBasePageByDeptIds(Page<KnoBaseVo> page, @Param("knoBaseVo") KnoBaseVo knoBaseVo, @Param("deptIds") List<String> deptIds);

    Integer sumViewCountStatusNotZero(@Param("deptIds") List<String> deptIds);

    /**
     * 批量更新originId下所有版本的isLatest
     */
    int updateIsLatestByOriginId(String originId, int isLatest);
}
