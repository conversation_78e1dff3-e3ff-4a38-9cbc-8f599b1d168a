package org.jeecg.modules.word.service;

import org.jeecg.modules.word.entity.WcWord;
import com.baomidou.mybatisplus.extension.service.IService;
import org.springframework.web.multipart.MultipartFile;
import java.util.List;

/**
 * @Description: 单词信息
 * @Author: wordCompass
 * @Date: 2025-01-15
 * @Version: V1.0
 */
public interface IWcWordService extends IService<WcWord> {
    
    /**
     * 根据教材ID查询单词列表
     * @param textbookId 教材ID
     * @return 单词列表
     */
    List<WcWord> getWordsByTextbookId(String textbookId);
    
    /**
     * 根据单元ID查询单词列表
     * @param unitId 单元ID
     * @return 单词列表
     */
    List<WcWord> getWordsByUnitId(String unitId);
    
    /**
     * 根据章节ID查询单词列表
     * @param chapterId 章节ID
     * @return 单词列表
     */
    List<WcWord> getWordsByChapterId(String chapterId);
    
    /**
     * 批量导入单词(Excel)
     * @param file Excel文件
     * @param textbookId 教材ID
     * @return 导入结果
     */
    String importWordsFromExcel(MultipartFile file, String textbookId);
    
    /**
     * 批量导入单词(文本识别)
     * @param content 文本内容
     * @param textbookId 教材ID
     * @return 导入结果
     */
    String importWordsFromText(String content, String textbookId);
    
    /**
     * 根据单词名称模糊查询
     * @param wordName 单词名称
     * @return 单词列表
     */
    List<WcWord> searchWordsByName(String wordName);
    
} 