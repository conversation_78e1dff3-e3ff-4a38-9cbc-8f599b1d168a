package org.jeecg.modules.word.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.system.base.entity.JeecgEntity;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * @Description: 教材基本信息
 * @Author: wordCompass
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="WcTextbook对象", description="教材基本信息")
@TableName("wc_textbook")
public class WcTextbook extends JeecgEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 教材名称
     */
    @Excel(name = "教材名称", width = 20)
    @ApiModelProperty(value = "教材名称")
    private String textbookName;

    /**
     * 版本
     */
    @Excel(name = "版本", width = 15)
    @ApiModelProperty(value = "版本")
    private String version;

    /**
     * 册别
     */
    @Excel(name = "册别", width = 15)
    @ApiModelProperty(value = "册别")
    private String volume;

    /**
     * 年级
     */
    @Excel(name = "年级", width = 15)
    @ApiModelProperty(value = "年级")
    private String grade;

    /**
     * 出版社
     */
    @Excel(name = "出版社", width = 20)
    @ApiModelProperty(value = "出版社")
    private String publisher;

    /**
     * 排序号
     */
    @ApiModelProperty(value = "排序号")
    private Integer sortOrder;

    /**
     * 状态(0-禁用,1-启用)
     */
    @Excel(name = "状态", width = 10, dicCode = "valid_status")
    @ApiModelProperty(value = "状态")
    private Integer status;

    /**
     * 备注
     */
    @Excel(name = "备注", width = 30)
    @ApiModelProperty(value = "备注")
    private String remark;
} 