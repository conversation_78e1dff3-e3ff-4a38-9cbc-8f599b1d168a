package org.jeecg.modules.word.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.word.entity.WcWechatUser;
import org.jeecg.modules.word.service.WcWechatUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * @Description: 微信用户信息管理
 * @Author: wordCompass
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Api(tags = "微信用户信息管理")
@RestController
@RequestMapping("/word/wcwechatuser")
@Slf4j
public class WcWechatUserController {

    @Autowired
    private WcWechatUserService wcWechatUserService;

    /**
     * 分页列表查询
     */
    @ApiOperation(value = "微信用户信息-分页列表查询", notes = "微信用户信息-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(WcWechatUser wcWechatUser,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<WcWechatUser> queryWrapper = QueryGenerator.initQueryWrapper(wcWechatUser, req.getParameterMap());
        queryWrapper.orderByDesc("create_time");
        Page<WcWechatUser> page = new Page<WcWechatUser>(pageNo, pageSize);
        IPage<WcWechatUser> pageList = wcWechatUserService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     */
    @ApiOperation(value = "微信用户信息-添加", notes = "微信用户信息-添加")
    @PostMapping(value = "/add")
    @AutoLog(value = "微信用户信息-添加")
    public Result<?> add(@RequestBody WcWechatUser wcWechatUser) {
        // 检查OpenID是否已存在
        boolean exists = wcWechatUserService.checkOpenidExists(wcWechatUser.getOpenid());
        if (exists) {
            return Result.error("OpenID已存在，不能重复添加");
        }
        
        // 设置状态为启用
        if (wcWechatUser.getStatus() == null) {
            wcWechatUser.setStatus(1);
        }
        
        wcWechatUserService.save(wcWechatUser);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     */
    @ApiOperation(value = "微信用户信息-编辑", notes = "微信用户信息-编辑")
    @PostMapping(value = "/edit")
    @AutoLog(value = "微信用户信息-编辑")
    public Result<?> edit(@RequestBody WcWechatUser wcWechatUser) {
        wcWechatUserService.updateById(wcWechatUser);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     */
    @ApiOperation(value = "微信用户信息-通过id删除", notes = "微信用户信息-通过id删除")
    @DeleteMapping(value = "/delete")
    @AutoLog(value = "微信用户信息-删除")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        wcWechatUserService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     */
    @ApiOperation(value = "微信用户信息-批量删除", notes = "微信用户信息-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    @AutoLog(value = "微信用户信息-批量删除")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.wcWechatUserService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     */
    @ApiOperation(value = "微信用户信息-通过id查询", notes = "微信用户信息-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        WcWechatUser wcWechatUser = wcWechatUserService.getById(id);
        if (wcWechatUser == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(wcWechatUser);
    }

    /**
     * 修改状态
     */
    @ApiOperation(value = "微信用户信息-修改状态", notes = "微信用户信息-修改状态")
    @PostMapping(value = "/changeStatus")
    @AutoLog(value = "微信用户信息-修改状态")
    public Result<?> changeStatus(@RequestParam(name = "id", required = true) String id,
                                  @RequestParam(name = "status", required = true) Integer status) {
        WcWechatUser wcWechatUser = new WcWechatUser();
        wcWechatUser.setId(id);
        wcWechatUser.setStatus(status);
        wcWechatUserService.updateById(wcWechatUser);
        return Result.OK("状态修改成功!");
    }

    /**
     * 检查OpenID是否存在
     */
    @ApiOperation(value = "微信用户信息-检查OpenID是否存在", notes = "微信用户信息-检查OpenID是否存在")
    @GetMapping(value = "/checkOpenidExists")
    public Result<?> checkOpenidExists(@RequestParam(name = "openid", required = true) String openid) {
        boolean exists = wcWechatUserService.checkOpenidExists(openid);
        Map<String, Object> result = new HashMap<>();
        result.put("exists", exists);
        return Result.OK(result);
    }
}