package org.jeecg.modules.word.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.word.entity.WcStudent;
import org.jeecg.modules.word.service.WcStudentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * @Description: 学生信息管理
 * @Author: wordCompass
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Api(tags = "学生信息管理")
@RestController
@RequestMapping("/word/wcstudent")
@Slf4j
public class WcStudentController {

    @Autowired
    private WcStudentService wcStudentService;
    
    @Value(value = "${jeecg.path.upload}")
    private String uploadpath;

    @Value(value = "${jeecg.uploadType}")
    private String uploadType;

    /**
     * 分页列表查询
     */
    @ApiOperation(value = "学生信息-分页列表查询", notes = "学生信息-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(WcStudent wcStudent,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<WcStudent> queryWrapper = QueryGenerator.initQueryWrapper(wcStudent, req.getParameterMap());
        queryWrapper.orderByDesc("create_time");
        Page<WcStudent> page = new Page<WcStudent>(pageNo, pageSize);
        IPage<WcStudent> pageList = wcStudentService.page(page, queryWrapper);
        return Result.OK(pageList);
    }
    
    /**
     * 添加
     */
    @AutoLog(value = "学生信息-添加")
    @ApiOperation(value = "学生信息-添加", notes = "学生信息-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody WcStudent wcStudent) {
        wcStudentService.save(wcStudent);
        return Result.OK("添加成功！");
    }
    
    /**
     * 编辑
     */
    @AutoLog(value = "学生信息-编辑")
    @ApiOperation(value = "学生信息-编辑", notes = "学生信息-编辑")
    @PostMapping(value = "/edit")
    public Result<?> edit(@RequestBody WcStudent wcStudent) {
        wcStudentService.updateById(wcStudent);
        return Result.OK("编辑成功!");
    }
    
    /**
     * 通过id删除
     */
    @AutoLog(value = "学生信息-通过id删除")
    @ApiOperation(value = "学生信息-通过id删除", notes = "学生信息-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        wcStudentService.removeById(id);
        return Result.OK("删除成功!");
    }
    
    /**
     * 批量删除
     */
    @AutoLog(value = "学生信息-批量删除")
    @ApiOperation(value = "学生信息-批量删除", notes = "学生信息-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<?> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.wcStudentService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功！");
    }
    
    /**
     * 通过id查询
     */
    @ApiOperation(value = "学生信息-通过id查询", notes = "学生信息-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id", required = true) String id) {
        WcStudent wcStudent = wcStudentService.getById(id);
        if (wcStudent == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(wcStudent);
    }
    
    /**
     * 修改学生状态
     */
    @ApiOperation(value = "修改学生状态", notes = "修改学生状态")
    @PostMapping(value = "/changeStatus")
    public Result<?> changeStatus(@RequestParam(name = "id", required = true) String id,
                                 @RequestParam(name = "status", required = true) Integer status) {
        boolean result = wcStudentService.updateStatus(id, status);
        if (result) {
            return Result.OK("状态修改成功");
        } else {
            return Result.error("状态修改失败");
        }
    }
    
    /**
     * 根据账号ID查询学生列表
     */
    @ApiOperation(value = "根据账号ID查询学生列表", notes = "根据账号ID查询学生列表")
    @GetMapping(value = "/listByAccountId")
    public Result<?> listByAccountId(@RequestParam(name = "accountId", required = false) String accountId) {
        // 如果 accountId 为空，则返回空数据
        if (oConvertUtils.isEmpty(accountId)) {
            return Result.OK(Collections.emptyList());
        }

        List<WcStudent> list = wcStudentService.getStudentsByAccountId(accountId);
        return Result.OK(list);
    }
    
    /**
     * 根据年级查询学生列表
     */
    @ApiOperation(value = "根据年级查询学生列表", notes = "根据年级查询学生列表")
    @GetMapping(value = "/listByGrade")
    public Result<?> listByGrade(@RequestParam(name = "grade", required = true) String grade) {
        List<WcStudent> list = wcStudentService.getStudentsByGrade(grade);
        return Result.OK(list);
    }
    
    /**
     * 上传头像
     */
    @ApiOperation(value = "上传头像", notes = "上传头像")
    @PostMapping(value = "/uploadAvatar")
    public Result<?> uploadAvatar(HttpServletRequest request) {
        Result<?> result = new Result<>();
        String bizPath = "avatar";
        
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        MultipartFile file = multipartRequest.getFile("file");
        if (file == null) {
            return Result.error("上传文件为空");
        }
        
        // 获取文件名
        String orgName = file.getOriginalFilename();
        orgName = orgName.substring(0, orgName.lastIndexOf(".")) + "_" + System.currentTimeMillis() + orgName.substring(orgName.lastIndexOf("."));
        
        String fileUrl = "";
        try {
            if ("local".equals(uploadType)) {
                fileUrl = org.jeecg.common.util.CommonUtils.uploadLocal(file, bizPath, uploadpath);
            } else {
                // 其他存储方式，如minio、阿里云等，可以根据需要扩展
                return Result.error("未配置支持的文件存储类型");
            }
        } catch (Exception e) {
            log.error("文件上传失败", e);
            return Result.error("文件上传失败: " + e.getMessage());
        }
        
        result.setMessage(fileUrl);
        result.setSuccess(true);
        return result;
    }
}