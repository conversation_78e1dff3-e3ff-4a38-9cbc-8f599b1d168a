package org.jeecg.modules.word.mapper;

import org.jeecg.modules.word.entity.WcTextbook;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * @Description: 教材基本信息
 * @Author: wordCompass
 * @Date: 2025-01-15
 * @Version: V1.0
 */
public interface WcTextbookMapper extends BaseMapper<WcTextbook> {
    
    /**
     * 根据年级查询教材列表
     * @param grade 年级
     * @return 教材列表
     */
    List<WcTextbook> selectByGrade(@Param("grade") String grade);
    
    /**
     * 根据年级和版本查询教材列表
     * @param grade 年级
     * @param version 版本
     * @return 教材列表
     */
    List<WcTextbook> selectByGradeAndVersion(@Param("grade") String grade, @Param("version") String version);
    
} 