package org.jeecg.modules.km.kno.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.km.kno.entity.KnoBase;
import org.jeecg.modules.km.kno.service.KnoBaseCollectService;
import org.jeecg.modules.km.kno.vo.KnoBaseVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

@RestController
@RequestMapping("/kno/api/collect")
@Api(tags = "知识汇集-版本管理")
public class KnoBaseCollectController {
    @Autowired
    private KnoBaseCollectService collectService;

    @ApiOperation("判断整个文档是否已存在")
    @GetMapping("/existsWholeDoc")
    public Result<Boolean> existsWholeDoc(@RequestParam String title) {
        return Result.OK(collectService.existsWholeDoc(title));
    }

    @ApiOperation("判断文档片段是否已存在")
    @GetMapping("/existsFragment")
    public Result<Boolean> existsFragment(@RequestParam String title, @RequestParam Integer documentLength) {
        return Result.OK(collectService.existsFragment(title, documentLength));
    }

    @ApiOperation("导入知识（多版本）")
    @PostMapping("/import")
    public Result<KnoBase> importKnowledge(@RequestBody KnoBaseVo data) {
        // 获取当前用户信息，这里需要根据实际情况获取
        // 获取当前用户
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String username = loginUser.getUsername();
        Date now = new Date();
        return Result.OK(collectService.importKnowledge(data, username, now));
    }

    @ApiOperation("发布/保存草稿（多版本）")
    @PostMapping("/saveOrUpdateForPublish")
    public Result<KnoBaseVo> saveOrUpdateForPublish(@RequestBody KnoBaseVo data, @RequestParam boolean publish) {
        // 获取当前用户信息，这里需要根据实际情况获取
        // 获取当前用户
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String username = loginUser.getUsername();
        Date now = new Date();
        return Result.OK(collectService.saveOrUpdateForPublish(data, publish, username, now));
    }
} 