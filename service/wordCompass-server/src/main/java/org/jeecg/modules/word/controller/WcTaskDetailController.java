package org.jeecg.modules.word.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.word.entity.WcTaskDetail;
import org.jeecg.modules.word.service.WcTaskDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: 任务明细控制器
 * @Author: jeecg-boot
 * @Date:   2023-08-18
 * @Version: V1.0
 */
@Slf4j
@Api(tags="任务明细管理")
@RestController
@RequestMapping("/word/wctaskdetail")
public class WcTaskDetailController extends JeecgController<WcTaskDetail, WcTaskDetailService> {
	@Autowired
	private WcTaskDetailService wcTaskDetailService;
	
	/**
	 * 分页列表查询
	 *
	 * @param wcTaskDetail
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	@ApiOperation(value="任务明细-分页列表查询", notes="任务明细-分页列表查询")
	@GetMapping(value = "/list")
	public Result<?> queryPageList(WcTaskDetail wcTaskDetail,
							   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
							   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
							   HttpServletRequest req) {
		QueryWrapper<WcTaskDetail> queryWrapper = QueryGenerator.initQueryWrapper(wcTaskDetail, req.getParameterMap());
		Page<WcTaskDetail> page = new Page<WcTaskDetail>(pageNo, pageSize);
		IPage<WcTaskDetail> pageList = wcTaskDetailService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 * 添加
	 *
	 * @param wcTaskDetail
	 * @return
	 */
	@ApiOperation(value="任务明细-添加", notes="任务明细-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody WcTaskDetail wcTaskDetail) {
		wcTaskDetailService.save(wcTaskDetail);
		return Result.OK("添加成功！");
	}
	
	/**
	 * 编辑
	 *
	 * @param wcTaskDetail
	 * @return
	 */
	@ApiOperation(value="任务明细-编辑", notes="任务明细-编辑")
	@PostMapping(value = "/edit")
	public Result<?> edit(@RequestBody WcTaskDetail wcTaskDetail) {
		wcTaskDetailService.updateById(wcTaskDetail);
		return Result.OK("编辑成功!");
	}
	
	/**
	 * 通过id删除
	 *
	 * @param id
	 * @return
	 */
	@ApiOperation(value="任务明细-通过id删除", notes="任务明细-通过id删除")
	@DeleteMapping(value = "/delete")
	public Result<?> delete(@RequestParam(name="id",required=true) String id) {
		wcTaskDetailService.removeById(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 * 批量删除
	 *
	 * @param ids
	 * @return
	 */
	@ApiOperation(value="任务明细-批量删除", notes="任务明细-批量删除")
	@DeleteMapping(value = "/deleteBatch")
	public Result<?> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.wcTaskDetailService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功！");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	@ApiOperation(value="任务明细-通过id查询", notes="任务明细-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<?> queryById(@RequestParam(name="id",required=true) String id) {
		WcTaskDetail wcTaskDetail = wcTaskDetailService.getById(id);
		return Result.OK(wcTaskDetail);
	}
	
	/**
	 * 根据任务ID查询任务明细列表
	 *
	 * @param taskId
	 * @return
	 */
	@ApiOperation(value="任务明细-根据任务ID查询", notes="任务明细-根据任务ID查询")
	@GetMapping(value = "/detailsByTaskId")
	public Result<?> detailsByTaskId(@RequestParam(name="taskId",required=true) String taskId) {
		List<WcTaskDetail> detailList = wcTaskDetailService.getDetailsByTaskId(taskId);
		return Result.OK(detailList);
	}
	
	/**
	 * 批量保存任务明细
	 *
	 * @param taskDetailList
	 * @return
	 */
	@ApiOperation(value="任务明细-批量保存", notes="任务明细-批量保存")
	@PostMapping(value = "/batchSave")
	public Result<?> batchSave(@RequestBody List<WcTaskDetail> taskDetailList) {
		boolean success = wcTaskDetailService.batchSaveTaskDetails(taskDetailList);
		if (success) {
			return Result.OK("批量保存成功！");
		} else {
			return Result.error("批量保存失败！");
		}
	}
	
	/**
	 * 批量更新任务明细
	 *
	 * @param taskDetailList
	 * @return
	 */
	@ApiOperation(value="任务明细-批量更新", notes="任务明细-批量更新")
	@PostMapping(value = "/batchUpdate")
	public Result<?> batchUpdate(@RequestBody List<WcTaskDetail> taskDetailList) {
		boolean success = wcTaskDetailService.batchUpdateTaskDetails(taskDetailList);
		if (success) {
			return Result.OK("批量更新成功！");
		} else {
			return Result.error("批量更新失败！");
		}
	}
	
	/**
	 * 根据任务ID删除任务明细
	 *
	 * @param taskId
	 * @return
	 */
	@ApiOperation(value="任务明细-根据任务ID删除", notes="任务明细-根据任务ID删除")
	@DeleteMapping(value = "/deleteByTaskId")
	public Result<?> deleteByTaskId(@RequestParam(name="taskId",required=true) String taskId) {
		boolean success = wcTaskDetailService.deleteByTaskId(taskId);
		if (success) {
			return Result.OK("删除成功！");
		} else {
			return Result.error("删除失败！");
		}
	}
	
	/**
	 * 批改任务明细
	 *
	 * @param taskDetailList
	 * @return
	 */
	@ApiOperation(value="任务明细-批改", notes="任务明细-批改")
	@PostMapping(value = "/correctTaskDetails")
	public Result<?> correctTaskDetails(@RequestBody List<WcTaskDetail> taskDetailList) {
		boolean success = wcTaskDetailService.correctTaskDetails(taskDetailList);
		if (success) {
			return Result.OK("批改成功！");
		} else {
			return Result.error("批改失败！");
		}
	}
}