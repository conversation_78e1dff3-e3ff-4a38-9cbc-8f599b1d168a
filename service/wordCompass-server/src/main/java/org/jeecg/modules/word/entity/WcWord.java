package org.jeecg.modules.word.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.system.base.entity.JeecgEntity;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * @Description: 单词信息
 * @Author: wordCompass
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="WcWord对象", description="单词信息")
@TableName("wc_word")
public class WcWord extends JeecgEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 单词名称
     */
    @Excel(name = "单词", width = 20)
    @ApiModelProperty(value = "单词名称")
    private String wordName;

    /**
     * 音标
     */
    @Excel(name = "音标", width = 20)
    @ApiModelProperty(value = "音标")
    private String phonetic;

    /**
     * 读音音频文件路径
     */
    @ApiModelProperty(value = "读音音频文件路径")
    private String audioFile;

    /**
     * 中文译义
     */
    @Excel(name = "中文译义", width = 30)
    @ApiModelProperty(value = "中文译义")
    private String translation;

    /**
     * 所属教材ID
     */
    @Excel(name = "所属教材ID", width = 20)
    @ApiModelProperty(value = "所属教材ID")
    private String textbookId;

    /**
     * 所属单元ID
     */
    @Excel(name = "所属单元ID", width = 20)
    @ApiModelProperty(value = "所属单元ID")
    private String unitId;

    /**
     * 所属章节ID
     */
    @Excel(name = "所属章节ID", width = 20)
    @ApiModelProperty(value = "所属章节ID")
    private String chapterId;

    /**
     * 单词分类(noun-名词,verb-动词,adj-形容词,adv-副词,prep-介词,conj-连词,int-感叹词)
     */
    @Excel(name = "单词分类", width = 15, dicCode = "word_category")
    @ApiModelProperty(value = "单词分类")
    private String wordCategory;

    /**
     * 难易程度(1-简单,2-中等,3-困难)
     */
    @Excel(name = "难易程度", width = 10, dicCode = "difficulty_level")
    @ApiModelProperty(value = "难易程度")
    private Integer difficultyLevel;

    /**
     * 例句
     */
    @Excel(name = "例句", width = 50)
    @ApiModelProperty(value = "例句")
    private String exampleSentence;

    /**
     * 例句翻译
     */
    @Excel(name = "例句翻译", width = 50)
    @ApiModelProperty(value = "例句翻译")
    private String exampleTranslation;

    /**
     * 排序号
     */
    @Excel(name = "排序号", width = 10)
    @ApiModelProperty(value = "排序号")
    private Integer sortOrder;

    /**
     * 状态(0-禁用,1-启用)
     */
    @Excel(name = "状态", width = 10, dicCode = "valid_status")
    @ApiModelProperty(value = "状态")
    private Integer status;

    /**
     * 备注
     */
    @Excel(name = "备注", width = 30)
    @ApiModelProperty(value = "备注")
    private String remark;
} 