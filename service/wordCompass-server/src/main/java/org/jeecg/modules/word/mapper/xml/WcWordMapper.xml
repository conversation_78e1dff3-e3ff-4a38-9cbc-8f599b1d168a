<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.word.mapper.WcWordMapper">

    <!-- 根据教材ID查询单词列表 -->
    <select id="selectByTextbookId" resultType="org.jeecg.modules.word.entity.WcWord">
        SELECT * FROM wc_word 
        WHERE textbook_id = #{textbookId} 
        AND del_flag = 0
        ORDER BY sort_order ASC, create_time ASC
    </select>
    
    <!-- 根据单元ID查询单词列表 -->
    <select id="selectByUnitId" resultType="org.jeecg.modules.word.entity.WcWord">
        SELECT * FROM wc_word 
        WHERE unit_id = #{unitId} 
        AND del_flag = 0
        ORDER BY sort_order ASC, create_time ASC
    </select>
    
    <!-- 根据章节ID查询单词列表 -->
    <select id="selectByChapterId" resultType="org.jeecg.modules.word.entity.WcWord">
        SELECT * FROM wc_word 
        WHERE chapter_id = #{chapterId} 
        AND del_flag = 0
        ORDER BY sort_order ASC, create_time ASC
    </select>
    
    <!-- 根据难易程度查询单词列表 -->
    <select id="selectByDifficultyLevel" resultType="org.jeecg.modules.word.entity.WcWord">
        SELECT * FROM wc_word 
        WHERE difficulty_level = #{difficultyLevel} 
        AND del_flag = 0
        ORDER BY sort_order ASC, create_time ASC
    </select>

</mapper>
