package org.jeecg.modules.word.mapper;

import org.jeecg.modules.word.entity.WcWechatUser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * @Description: 微信用户信息
 * @Author: wordCompass
 * @Date: 2025-01-15
 * @Version: V1.0
 */
public interface WcWechatUserMapper extends BaseMapper<WcWechatUser> {
    
    /**
     * 根据OpenID查询微信用户
     * @param openid OpenID
     * @return 微信用户信息
     */
    WcWechatUser selectByOpenid(@Param("openid") String openid);
    
    /**
     * 根据UnionID查询微信用户
     * @param unionid UnionID
     * @return 微信用户信息
     */
    WcWechatUser selectByUnionid(@Param("unionid") String unionid);
    
    /**
     * 根据关联账号ID查询微信用户列表
     * @param accountId 关联账号ID
     * @return 微信用户列表
     */
    List<WcWechatUser> selectByAccountId(@Param("accountId") String accountId);
    
} 