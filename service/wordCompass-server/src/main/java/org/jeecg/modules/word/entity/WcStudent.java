package org.jeecg.modules.word.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.system.base.entity.JeecgEntity;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * @Description: 学生信息
 * @Author: wordCompass
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="WcStudent对象", description="学生信息")
@TableName("wc_student")
public class WcStudent extends JeecgEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 账号ID
     */
    @Excel(name = "账号ID", width = 20)
    @ApiModelProperty(value = "账号ID")
    private String accountId;

    /**
     * 学生姓名
     */
    @Excel(name = "学生姓名", width = 20)
    @ApiModelProperty(value = "学生姓名")
    private String studentName;

    /**
     * 年级
     */
    @Excel(name = "年级", width = 15)
    @ApiModelProperty(value = "年级")
    private String grade;

    /**
     * 学校
     */
    @Excel(name = "学校", width = 30)
    @ApiModelProperty(value = "学校")
    private String school;

    /**
     * 班级
     */
    @Excel(name = "班级", width = 20)
    @ApiModelProperty(value = "班级")
    private String className;

    /**
     * 性别(1-男,2-女)
     */
    @Excel(name = "性别", width = 10, dicCode = "gender")
    @ApiModelProperty(value = "性别")
    private Integer gender;

    /**
     * 出生日期
     */
    @Excel(name = "出生日期", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "出生日期")
    private Date birthDate;

    /**
     * 头像
     */
    @ApiModelProperty(value = "头像")
    private String avatar;

    /**
     * 状态(0-禁用,1-启用)
     */
    @Excel(name = "状态", width = 10, dicCode = "valid_status")
    @ApiModelProperty(value = "状态")
    private Integer status;

    /**
     * 排序号
     */
    @ApiModelProperty(value = "排序号")
    private Integer sortOrder;

    /**
     * 备注
     */
    @Excel(name = "备注", width = 30)
    @ApiModelProperty(value = "备注")
    private String remark;
} 