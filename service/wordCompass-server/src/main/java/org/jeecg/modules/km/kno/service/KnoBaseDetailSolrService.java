package org.jeecg.modules.km.kno.service;

import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.common.SignTools;
import org.jeecg.modules.km.kno.vo.KnoBaseDetailVo;
import org.jeecg.modules.km.kno.repository.KnoBaseDetailVoRepository;
import org.jeecg.modules.system.entity.SysDepart;
import org.jeecg.modules.system.service.impl.SysBaseApiImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.solr.core.SolrTemplate;
import org.springframework.data.solr.core.query.Criteria;
import org.springframework.data.solr.core.query.SimpleQuery;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.apache.commons.lang3.StringUtils;
import lombok.extern.slf4j.Slf4j;

import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/** 
 * 知识明细Solr服务类
 * 用于知识明细的Solr搜索
 * created by yf on 2025-04-25
 */
@Slf4j
@Service
public class KnoBaseDetailSolrService {

    @Autowired
    private KnoBaseDetailVoRepository knoBaseDetailVoRepository;

    @Autowired
    private SolrTemplate solrTemplate;

    @Autowired
    private KnoDeptService knoDeptService;

    @Autowired
    private SysBaseApiImpl sysBaseApi;

    /**
     * 搜索知识明细（带数据权限控制）
     * @param keywords 关键词
     * @param number 页码
     * @param size 页大小
     * @return 分页结果
     */
    public IPage<KnoBaseDetailVo> search(String keywords, long number, int size) {
        Pageable pageable = PageRequest.of((int) number, size);
        Criteria criteria = new Criteria();
        
        // 构建关键词查询条件
        if (StringUtils.isNotBlank(keywords)) {
            criteria = buildKeywordsCriteria(keywords);
        }
        
        // 添加数据权限控制
        criteria = addDataPermissionFilter(criteria);

        SimpleQuery query = new SimpleQuery(criteria, pageable);
        addProjectionFields(query);
        
        // 按评分降序排序
        query.addSort(Sort.by(Sort.Direction.DESC, "score"));
        
        Page<KnoBaseDetailVo> solrPage = solrTemplate.queryForPage(KnoBaseDetailVo.SOLR_CORE, query, KnoBaseDetailVo.class);

        // 处理图片路径签名
        processImagePaths(solrPage.getContent());
        
        // 转换为MyBatis Plus分页对象
        return convertToMybatisPlusPage(solrPage, number, size);
    }

    /**
     * 构建关键词查询条件
     * @param keywords 关键词
     * @return 查询条件
     */
    private Criteria buildKeywordsCriteria(String keywords) {
        Criteria criteria = new Criteria();
        
        // 分割关键词
        String[] keywordArray = keywords.split("[,\\s]+");
        int maxKeywords = 15; // 限制关键词数量
        if (keywordArray.length > maxKeywords) {
            keywordArray = Arrays.copyOf(keywordArray, maxKeywords);
        }
        
        // 对每个关键词构建查询条件
        for (String keyword : keywordArray) {
            if (StringUtils.isNotBlank(keyword)) {
                criteria = criteria.or(new Criteria("kbasedel_content").contains(keyword).boost(3.5f))
                        .or(new Criteria("kbasedel_ocrContent").contains(keyword).boost(3.0f))
                        .or(new Criteria("kbasedel_title").contains(keyword).boost(4.0f))
                        .or(new Criteria("kbasedel_knoTagNames").contains(keyword).boost(1.5f))
                        .or(new Criteria("kbasedel_srcipt").contains(keyword).boost(1.0f))
                        .or(new Criteria("kbasedel_srciptSimplify").contains(keyword).boost(1.0f))
                        .or(new Criteria("kbasedel_keywords").contains(keyword).boost(4.5f));
            }
        }
        
        return criteria;
    }

    /**
     * 添加数据权限过滤条件
     * @param criteria 现有查询条件
     * @return 添加权限过滤后的查询条件
     */
    private Criteria addDataPermissionFilter(Criteria criteria) {
        try {
            // 获取当前登录用户
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (loginUser == null) {
                log.warn("未获取到当前登录用户，将返回空结果");
                return criteria.and(new Criteria("kbasedel_organId").is("__NO_DATA__"));
            }
            
            String username = loginUser.getUsername();
            log.debug("当前用户: {}", username);
            
            // 获取用户所属部门ID
            List<String> deptIds = sysBaseApi.getDepartIdsByUsername(username);
            if (deptIds == null || deptIds.isEmpty()) {
                log.warn("用户 {} 未分配部门，将返回空结果", username);
                return criteria.and(new Criteria("kbasedel_organId").is("__NO_DATA__"));
            }
            
            String currentDeptId = deptIds.get(0);
            log.debug("用户所属部门ID: {}", currentDeptId);
            
            // 查找根机构
            SysDepart rootDepart = knoDeptService.findRootOrganByOrganId(currentDeptId);
            if (rootDepart == null) {
                log.warn("未找到用户 {} 的根机构，将返回空结果", username);
                return criteria.and(new Criteria("kbasedel_organId").is("__NO_DATA__"));
            }
            
            String orgCategory = rootDepart.getOrgCategory();
            log.debug("根机构类别: {}, 机构名称: {}", orgCategory, rootDepart.getDepartName());
            
            // 根据机构类别设置数据权限
            if ("1".equals(orgCategory)) {
                // 总公司：可以查看所有数据，不添加部门限制
                log.debug("总公司用户，可查看所有数据");
                return criteria;
            } else {
                // 分公司或其他：只能查看本公司及其子部门的数据
                List<String> allowedDeptIds = knoDeptService.getDeptAndSubDeptIds(rootDepart.getId());
                if (allowedDeptIds == null || allowedDeptIds.isEmpty()) {
                    log.warn("未找到根机构 {} 的子部门，将返回空结果", rootDepart.getDepartName());
                    return criteria.and(new Criteria("kbasedel_organId").is("__NO_DATA__"));
                }
                
                log.debug("分公司用户，允许查看的部门数量: {}", allowedDeptIds.size());
                
                // 使用 Solr 的 IN 查询
                return criteria.and(new Criteria("kbasedel_organId").in(allowedDeptIds));
            }
            
        } catch (Exception e) {
            log.error("添加数据权限过滤条件失败", e);
            // 发生异常时，返回空结果以确保数据安全
            return criteria.and(new Criteria("kbasedel_organId").is("__NO_DATA__"));
        }
    }

    /**
     * 添加查询字段
     * @param query 查询对象
     */
    private void addProjectionFields(SimpleQuery query) {
        query.addProjectionOnField("kbasedel_title");
        query.addProjectionOnField("kbasedel_content");
        query.addProjectionOnField("kbasedel_ocrContent");
        query.addProjectionOnField("kbasedel_srcipt");
        query.addProjectionOnField("kbasedel_srciptSimplify");
        query.addProjectionOnField("kbasedel_keywords");
        query.addProjectionOnField("kbasedel_knoTagNames");
        query.addProjectionOnField("score");
        query.addProjectionOnField("kbasedel_id");
        query.addProjectionOnField("kbasedel_category_id");
        query.addProjectionOnField("kbasedel_parentId");
        query.addProjectionOnField("kbasedel_knowledgeId");
        query.addProjectionOnField("kbasedel_viewCount");
        query.addProjectionOnField("kbasedel_status");
        query.addProjectionOnField("kbasedel_sourceType");
        query.addProjectionOnField("kbasedel_creatorName");
        query.addProjectionOnField("kbasedel_updaterName");
        query.addProjectionOnField("kbasedel_createTime");
        query.addProjectionOnField("kbasedel_updateTime");
        query.addProjectionOnField("kbasedel_organName");
        query.addProjectionOnField("kbasedel_organId");
        query.addProjectionOnField("kbasedel_singleImageFilePath");
    }

    /**
     * 处理图片路径签名
     * @param detailList 知识明细列表
     */
    private void processImagePaths(List<KnoBaseDetailVo> detailList) {
        for (KnoBaseDetailVo detail : detailList) {
            String singleImageFilePath = detail.getSingleImageFilePath();
            if (oConvertUtils.isNotEmpty(singleImageFilePath)) {
                String signedUrl = SignTools.generateSignedUrl(singleImageFilePath);
                detail.setSingleImageFilePathForView(signedUrl);
            }
        }
    }

    /**
     * 转换为MyBatis Plus分页对象
     * @param solrPage Solr分页结果
     * @param number 页码
     * @param size 页大小
     * @return MyBatis Plus分页对象
     */
    private IPage<KnoBaseDetailVo> convertToMybatisPlusPage(Page<KnoBaseDetailVo> solrPage, long number, int size) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<KnoBaseDetailVo> mybatisPage = 
            new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>();
        mybatisPage.setCurrent(number + 1);
        mybatisPage.setSize(size);
        mybatisPage.setTotal(solrPage.getTotalElements());
        mybatisPage.setRecords(solrPage.getContent());
        return mybatisPage;
    }

    /**
     * 搜索知识明细（不带数据权限控制，供系统内部使用）
     * @param keywords 关键词
     * @param number 页码
     * @param size 页大小
     * @return 分页结果
     */
    public IPage<KnoBaseDetailVo> searchWithoutPermission(String keywords, long number, int size) {
        Pageable pageable = PageRequest.of((int) number, size);
        Criteria criteria = new Criteria();
        
        // 构建关键词查询条件
        if (StringUtils.isNotBlank(keywords)) {
            criteria = buildKeywordsCriteria(keywords);
        }

        SimpleQuery query = new SimpleQuery(criteria, pageable);
        addProjectionFields(query);
        
        // 按评分降序排序
        query.addSort(Sort.by(Sort.Direction.DESC, "score"));
        
        Page<KnoBaseDetailVo> solrPage = solrTemplate.queryForPage(KnoBaseDetailVo.SOLR_CORE, query, KnoBaseDetailVo.class);

        // 处理图片路径签名
        processImagePaths(solrPage.getContent());
        
        // 转换为MyBatis Plus分页对象
        return convertToMybatisPlusPage(solrPage, number, size);
    }

    /**
     * 获取评分最高的记录（带数据权限控制）
     * @param keywords 关键词
     * @return 评分最高的记录
     */
    public KnoBaseDetailVo getHighestScoreRecord(String keywords) {
        IPage<KnoBaseDetailVo> page = search(keywords, 0, 1);
        if (page != null && !page.getRecords().isEmpty()) {
            return page.getRecords().get(0);
        }
        return null;
    }

    /**
     * 获取评分最高的记录（不带数据权限控制）
     * @param keywords 关键词
     * @return 评分最高的记录
     */
    public KnoBaseDetailVo getHighestScoreRecordWithoutPermission(String keywords) {
        IPage<KnoBaseDetailVo> page = searchWithoutPermission(keywords, 0, 1);
        if (page != null && !page.getRecords().isEmpty()) {
            return page.getRecords().get(0);
        }
        return null;
    }

    @Transactional
    public void batchInsert(List<KnoBaseDetailVo> knoBaseDetailVos) {
        knoBaseDetailVoRepository.saveAll(knoBaseDetailVos);
    }

    @Transactional
    public void deleteById(int id) {
        knoBaseDetailVoRepository.deleteById(String.valueOf(id));
    }

    @Transactional
    public KnoBaseDetailVo save(KnoBaseDetailVo knoBaseDetailVo) {
        return knoBaseDetailVoRepository.save(knoBaseDetailVo);
    }

    @Transactional
    public void deleteAll() {
        knoBaseDetailVoRepository.deleteAll();
    }

    public List<KnoBaseDetailVo> findAll() {
        return (List<KnoBaseDetailVo>) knoBaseDetailVoRepository.findAll();
    }

    public KnoBaseDetailVo findById(int id) {
        Optional<KnoBaseDetailVo> optional = knoBaseDetailVoRepository.findById(String.valueOf(id));
        return optional.orElse(null);
    }
} 