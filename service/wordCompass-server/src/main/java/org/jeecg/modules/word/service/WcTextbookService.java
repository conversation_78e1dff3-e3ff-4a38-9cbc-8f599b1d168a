package org.jeecg.modules.word.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.word.entity.WcTextbook;
import org.jeecg.modules.word.entity.WcTextbookDetail;
import org.jeecg.modules.word.mapper.WcTextbookDetailMapper;
import org.jeecg.modules.word.mapper.WcTextbookMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @Description: 教材基本信息
 * @Author: wordCompass
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Service
public class WcTextbookService extends ServiceImpl<WcTextbookMapper, WcTextbook> {

    @Autowired
    private WcTextbookMapper wcTextbookMapper;

    @Autowired
    private WcTextbookDetailMapper wcTextbookDetailMapper;

    /**
     * 根据年级查询教材列表
     */
    public List<WcTextbook> getTextbooksByGrade(String grade) {
        return wcTextbookMapper.selectByGrade(grade);
    }

    /**
     * 根据年级和版本查询教材列表
     */
    public List<WcTextbook> getTextbooksByGradeAndVersion(String grade, String version) {
        return wcTextbookMapper.selectByGradeAndVersion(grade, version);
    }

    /**
     * 获取教材的树形结构
     */
    public List<WcTextbookDetail> getTextbookTree(String textbookId) {
        return wcTextbookDetailMapper.selectTreeByTextbookId(textbookId);
    }

    /**
     * 获取教材明细列表
     */
    public List<WcTextbookDetail> getTextbookDetails(String textbookId, String type) {
        if (type != null && !type.trim().isEmpty()) {
            return wcTextbookDetailMapper.selectByTextbookIdAndType(textbookId, type);
        } else {
            return wcTextbookDetailMapper.selectByTextbookId(textbookId);
        }
    }

    /**
     * 保存教材明细
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveTextbookDetail(WcTextbookDetail detail) {
        return wcTextbookDetailMapper.insert(detail) > 0;
    }

    /**
     * 更新教材明细
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTextbookDetail(WcTextbookDetail detail) {
        return wcTextbookDetailMapper.updateById(detail) > 0;
    }

    /**
     * 删除教材明细
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTextbookDetail(String detailId) {
        // 检查是否有子节点
        List<WcTextbookDetail> children = wcTextbookDetailMapper.selectByParentId(detailId);
        if (children != null && !children.isEmpty()) {
            throw new RuntimeException("该节点下还有子节点，无法删除");
        }
        return wcTextbookDetailMapper.deleteById(detailId) > 0;
    }

    /**
     * 获取教材明细详情
     */
    public WcTextbookDetail getTextbookDetailById(String detailId) {
        return wcTextbookDetailMapper.selectById(detailId);
    }
}