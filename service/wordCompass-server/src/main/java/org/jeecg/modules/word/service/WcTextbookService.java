package org.jeecg.modules.word.service;

import org.jeecg.modules.word.entity.WcTextbook;
import org.jeecg.modules.word.mapper.WcTextbookMapper;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

/**
 * @Description: 教材基本信息
 * @Author: wordCompass
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Service
public class WcTextbookService extends ServiceImpl<WcTextbookMapper, WcTextbook> {

    private WcTextbookMapper wcTextbookMapper;

    public List<WcTextbook> getTextbooksByGrade(String grade) {
        return wcTextbookMapper.selectByGrade(grade);
    }

    public List<WcTextbook> getTextbooksByGradeAndVersion(String grade, String version) {
        return wcTextbookMapper.selectByGradeAndVersion(grade, version);
    }
} 