package org.jeecg.modules.word.vo;

import org.jeecg.modules.word.entity.WcTask;
import org.jeecg.modules.word.entity.WcTaskDetail;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.util.List;

/**
 * @Description: 任务VO
 * @Author: wordCompass
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class WcTaskVO extends WcTask {
    
    /**
     * 任务明细列表
     */
    private List<WcTaskDetail> taskDetailList;
    
    /**
     * 选中的单词ID列表（用于创建任务）
     */
    private List<String> wordIds;
    
    /**
     * 教材名称
     */
    private String textbookName;
    
    /**
     * 单元名称
     */
    private String unitName;
    
    /**
     * 章节名称
     */
    private String chapterName;
    
    /**
     * 学生姓名
     */
    private String studentName;
    
} 