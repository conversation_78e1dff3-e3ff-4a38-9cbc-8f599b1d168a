package org.jeecg.modules.km.kno.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.km.kno.entity.KnoBusinessType;
import org.jeecg.modules.km.kno.service.KnoBusinessTypeService;
import java.util.Date;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.system.model.TreeSelectModel;
import org.apache.commons.lang.StringUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

@Slf4j
@Api(tags="知识业务类型")
@RestController
@RequestMapping("/kno/knoBusinessType")
public class KnoBusinessTypeController extends JeecgController<KnoBusinessType, KnoBusinessTypeService> {
	@Autowired
	private KnoBusinessTypeService knoBusinessTypeService;

	@ApiOperation("知识业务类型-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<KnoBusinessType>> queryPageList(KnoBusinessType knoBusinessType,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		if(oConvertUtils.isEmpty(knoBusinessType.getParentId())){
			knoBusinessType.setParentId("0");
		}
		Result<IPage<KnoBusinessType>> result = new Result<IPage<KnoBusinessType>>();
		QueryWrapper<KnoBusinessType> queryWrapper = QueryGenerator.initQueryWrapper(knoBusinessType, req.getParameterMap());
		String name = knoBusinessType.getName();
		if(StringUtils.isBlank(name)){
			queryWrapper.eq("parent_id", knoBusinessType.getParentId());
		}
		Page<KnoBusinessType> page = new Page<KnoBusinessType>(pageNo, pageSize);
		IPage<KnoBusinessType> pageList = knoBusinessTypeService.findKnoBusinessTypeByPage(knoBusinessType, pageNo, pageSize, req.getParameterMap());
		result.setSuccess(true);
		result.setResult(pageList);
		return result;
	}

	@ApiOperation("知识业务类型-子节点列表查询")
	@GetMapping(value = "/childList")
	public Result<List<KnoBusinessType>> queryChildList(KnoBusinessType knoBusinessType, HttpServletRequest req) {
		Result<List<KnoBusinessType>> result = new Result<List<KnoBusinessType>>();
		QueryWrapper<KnoBusinessType> queryWrapper = QueryGenerator.initQueryWrapper(knoBusinessType, req.getParameterMap());
		Page<KnoBusinessType> page = new Page<>(1, 10);
		List<KnoBusinessType> list = knoBusinessTypeService.findKnoBusinessTypes(page, queryWrapper).getRecords();
		result.setSuccess(true);
		result.setResult(list);
		return result;
	}

	@ApiOperation("知识业务类型-批量查询子节点")
	@GetMapping("/getChildListBatch")
	public Result getChildListBatch(@RequestParam("parentIds") String parentIds) {
		try {
			QueryWrapper<KnoBusinessType> queryWrapper = new QueryWrapper<>();
			List<String> parentIdList = Arrays.asList(parentIds.split(","));
			queryWrapper.in("parent_id", parentIdList);
			Page<KnoBusinessType> page = new Page<>(1, 10);
			List<KnoBusinessType> list = knoBusinessTypeService.findKnoBusinessTypes(page, queryWrapper).getRecords();
			IPage<KnoBusinessType> pageList = new Page<>(1, 10, list.size());
			pageList.setRecords(list);
			return Result.OK(pageList);
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			return Result.error("批量查询子节点失败：" + e.getMessage());
		}
	}

	@ApiOperation("知识业务类型-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody KnoBusinessType knoBusinessType) {
		knoBusinessTypeService.saveOrUpdateKnoBusinessType(knoBusinessType);
		return Result.OK("添加成功！");
	}

	@ApiOperation("知识业务类型-编辑")
	@PostMapping(value = "/edit")
	public Result<String> edit(@RequestBody KnoBusinessType knoBusinessType) {
		knoBusinessTypeService.saveOrUpdateKnoBusinessType(knoBusinessType);
		return Result.OK("编辑成功!");
	}

	@ApiOperation("知识业务类型-通过id删除")
	@PostMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		knoBusinessTypeService.deleteKnoBusinessType(id);
		return Result.OK("删除成功!");
	}

	@ApiOperation("知识业务类型-批量删除")
	@PostMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.knoBusinessTypeService.deleteKnoBusinessTypeByIds(ids);
		return Result.OK("批量删除成功！");
	}

	@ApiOperation("知识业务类型-通过id查询")
	@PostMapping(value = "/queryById")
	public Result<KnoBusinessType> queryById(@RequestParam(name="id",required=true) String id) {
		KnoBusinessType knoBusinessType = knoBusinessTypeService.findKnoBusinessTypeById(id);
		return Result.OK(knoBusinessType);
	}

	@ApiOperation("知识业务类型-加载树形数据")
	@GetMapping(value = "/loadTreeRoot")
	public Result<List<TreeSelectModel>> loadTreeRoot(@RequestParam(name="async") Boolean async, @RequestParam(name="pcode") String pcode) {
		Result<List<TreeSelectModel>> result = new Result<List<TreeSelectModel>>();
		try {
			List<TreeSelectModel> ls = this.knoBusinessTypeService.findTreeSelectModelByCode(pcode);
			if(!async) {
				loadAllBusinessTypeChildren(ls);
			}
			result.setResult(ls);
			result.setSuccess(true);
		} catch (Exception e) {
			e.printStackTrace();
			result.setMessage(e.getMessage());
			result.setSuccess(false);
		}
		return result;
	}

	@ApiOperation("知识业务类型-加载子节点")
	@GetMapping(value = "/loadTreeChildren")
	public Result<List<TreeSelectModel>> loadTreeChildren(@RequestParam(name="pid") String pid) {
		Result<List<TreeSelectModel>> result = new Result<List<TreeSelectModel>>();
		try {
			List<TreeSelectModel> ls = this.knoBusinessTypeService.findTreeSelectModelByPid(pid);
			result.setResult(ls);
			result.setSuccess(true);
		} catch (Exception e) {
			e.printStackTrace();
			result.setMessage(e.getMessage());
			result.setSuccess(false);
		}
		return result;
	}

	@ApiOperation("知识业务类型-查询所有启用的业务类型")
	@GetMapping(value = "/enabled")
	public Result<List<KnoBusinessType>> getEnabledTypes() {
		Result<List<KnoBusinessType>> result = new Result<List<KnoBusinessType>>();
		try {
			List<KnoBusinessType> list = this.knoBusinessTypeService.findEnabledTypes();
			result.setResult(list);
			result.setSuccess(true);
		} catch (Exception e) {
			e.printStackTrace();
			result.setMessage(e.getMessage());
			result.setSuccess(false);
		}
		return result;
	}

	@ApiOperation("知识业务类型-根据层级查询")
	@GetMapping(value = "/byLevel")
	public Result<List<KnoBusinessType>> getTypesByLevel(@RequestParam(name="level",required=true) Integer level) {
		Result<List<KnoBusinessType>> result = new Result<List<KnoBusinessType>>();
		try {
			List<KnoBusinessType> list = this.knoBusinessTypeService.findTypesByLevel(level);
			result.setResult(list);
			result.setSuccess(true);
		} catch (Exception e) {
			e.printStackTrace();
			result.setMessage(e.getMessage());
			result.setSuccess(false);
		}
		return result;
	}

	@ApiOperation("知识业务类型-查询树形结构数据")
	@GetMapping(value = "/treeData")
	public Result<List<TreeSelectModel>> getTreeData(@RequestParam(name="parentId",required=false) String parentId) {
		Result<List<TreeSelectModel>> result = new Result<List<TreeSelectModel>>();
		try {
			String pid = parentId != null ? parentId : "0";
			List<TreeSelectModel> list = this.knoBusinessTypeService.findTreeSelectModelByParentId(pid);
			result.setResult(list);
			result.setSuccess(true);
		} catch (Exception e) {
			e.printStackTrace();
			result.setMessage(e.getMessage());
			result.setSuccess(false);
		}
		return result;
	}

	/**
	 * 加载所有子节点
	 */
	private void loadAllBusinessTypeChildren(List<TreeSelectModel> ls) {
		for (TreeSelectModel tsm : ls) {
			List<TreeSelectModel> children = this.knoBusinessTypeService.findTreeSelectModelByPid(tsm.getKey());
			if (children != null && children.size() > 0) {
				tsm.setChildren(children);
				loadAllBusinessTypeChildren(children);
			}
		}
	}

	@ApiOperation("知识业务类型-导出excel")
	@RequestMapping(value = "/exportXls")
	public ModelAndView exportXls(HttpServletRequest request, KnoBusinessType knoBusinessType) {
		return super.exportXls(request, knoBusinessType, KnoBusinessType.class, "知识业务类型");
	}

	@ApiOperation("知识业务类型-导入excel")
	@RequestMapping(value = "/importExcel", method = RequestMethod.POST)
	public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
		return super.importExcel(request, response, KnoBusinessType.class);
	}
} 