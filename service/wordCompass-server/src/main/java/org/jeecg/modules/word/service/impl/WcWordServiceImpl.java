package org.jeecg.modules.word.service.impl;

import org.jeecg.modules.word.entity.WcWord;
import org.jeecg.modules.word.mapper.WcWordMapper;
import org.jeecg.modules.word.service.IWcWordService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.entity.ImportParams;
import lombok.extern.slf4j.Slf4j;
import java.util.List;

/**
 * @Description: 单词信息
 * @Author: wordCompass
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Slf4j
@Service
public class WcWordServiceImpl extends ServiceImpl<WcWordMapper, WcWord> implements IWcWordService {

    @Autowired
    private WcWordMapper wcWordMapper;

    @Override
    public List<WcWord> getWordsByTextbookId(String textbookId) {
        return wcWordMapper.selectByTextbookId(textbookId);
    }

    @Override
    public List<WcWord> getWordsByUnitId(String unitId) {
        return wcWordMapper.selectByUnitId(unitId);
    }

    @Override
    public List<WcWord> getWordsByChapterId(String chapterId) {
        return wcWordMapper.selectByChapterId(chapterId);
    }

    @Override
    public String importWordsFromExcel(MultipartFile file, String textbookId) {
        try {
            ImportParams params = new ImportParams();
            params.setTitleRows(1);
            params.setHeadRows(1);
            List<WcWord> wordList = ExcelImportUtil.importExcel(file.getInputStream(), WcWord.class, params);
            
            // 设置教材ID
            for (WcWord word : wordList) {
                word.setTextbookId(textbookId);
                word.setStatus(1);
            }
            
            // 批量保存
            this.saveBatch(wordList);
            
            return "成功导入 " + wordList.size() + " 个单词";
        } catch (Exception e) {
            log.error("Excel导入单词失败", e);
            return "导入失败：" + e.getMessage();
        }
    }

    @Override
    public String importWordsFromText(String content, String textbookId) {
        try {
            // 简单的文本解析，按行分割
            String[] lines = content.split("\n");
            List<WcWord> wordList = new java.util.ArrayList<>();
            
            for (String line : lines) {
                if (line.trim().isEmpty()) continue;
                
                // 假设格式为：单词 音标 中文译义
                String[] parts = line.trim().split("\\s+", 3);
                if (parts.length >= 2) {
                    WcWord word = new WcWord();
                    word.setWordName(parts[0]);
                    if (parts.length >= 3) {
                        word.setPhonetic(parts[1]);
                        word.setTranslation(parts[2]);
                    } else {
                        word.setTranslation(parts[1]);
                    }
                    word.setTextbookId(textbookId);
                    word.setStatus(1);
                    wordList.add(word);
                }
            }
            
            // 批量保存
            this.saveBatch(wordList);
            
            return "成功导入 " + wordList.size() + " 个单词";
        } catch (Exception e) {
            log.error("文本导入单词失败", e);
            return "导入失败：" + e.getMessage();
        }
    }

    @Override
    public List<WcWord> searchWordsByName(String wordName) {
        return wcWordMapper.selectByWordNameLike(wordName);
    }
} 