package org.jeecg.modules.km.kno.service;

import com.aspose.slides.License;
import com.aspose.slides.Presentation;
import com.aspose.slides.PdfOptions;
import com.aspose.slides.SaveFormat;
import com.itextpdf.kernel.colors.ColorConstants;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.pdf.*;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.kernel.pdf.extgstate.PdfExtGState;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.km.kno.entity.KnoBase;
import org.jeecg.modules.km.kno.mapper.KnoBaseMapper;
import org.jeecg.modules.microsoft.AsposeUtils;
import org.jeecg.modules.microsoft.MicrosoftConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.io.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 将知识库的PPT附件转换为带水印的PDF，并保存为临时文件
 * creater: yf
 * date: 2025-07-07
 */
@Service
@Slf4j
public class PptToPdfWatermarkService {

    @Autowired
    private KnoBaseService knoBaseService;

    @Value("${jeecg.path.upload}")
    private String uploadPath;

    @Value("${jeecg.path.uploadTmp}")
    private String uploadTmpPath;

    @Value("${app.pdf.watermark.text:机密文档}")
    private String defaultWatermarkText;

    @Value("${app.pdf.watermark.opacity:0.15}")
    private float watermarkOpacity;

    /**
     * 将知识库的PPT附件转换为带水印的PDF，并保存为临时文件
     *
     * @param knowledgeId 知识库ID
     * @param watermarkText 水印文本，如果为空则使用默认水印
     * @return PDF临时文件路径
     */
    public String convertPptToPdfWithWatermark(String knowledgeId, String watermarkText) throws Exception {
        log.info("开始转换PPT为PDF，知识库ID: {}", knowledgeId);

        // 1. 获取知识库基础信息
        KnoBase knoBase = knoBaseService.findKnoBaseById(knowledgeId);
        if (knoBase == null) {
            throw new RuntimeException("知识库不存在，ID: " + knowledgeId);
        }

        // 检查知识库状态
        if (knoBase.getStatus() != 1) {
            //throw new RuntimeException("只有已发布的知识库才能导出PDF");
        }

        // 检查是否有PPT附件
        if (knoBase.getMainFilePath() == null || knoBase.getMainFilePath().trim().isEmpty()) {
            throw new RuntimeException("知识库没有PPT附件文件");
        }

        // 2. 获取PPT文件
        File pptFile = getPptFile(knoBase.getMainFilePath());
        if (!pptFile.exists()) {
            throw new RuntimeException("PPT附件文件不存在: " + knoBase.getMainFilePath());
        }

        // 3. 生成唯一的临时文件名
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"));
        String fileName = String.format("ppt_to_pdf_%s_%s_%s.pdf", 
            knowledgeId, timestamp, Thread.currentThread().getId());
        
        // 4. 创建临时文件路径
        File tempDir = new File(uploadTmpPath, "pdf_convert");
        if (!tempDir.exists()) {
            tempDir.mkdirs();
        }
        File tempPdfFile = new File(tempDir, fileName);

        try {
            // 5. 转换PPT为PDF并直接写入临时文件
            convertPptToPdfToFile(pptFile, tempPdfFile, knoBase, watermarkText);
            
            // 6. 返回临时文件的相对路径
            String relativePath = "pdf_convert/" + fileName;
            log.info("PPT转PDF转换完成，临时文件: {}", relativePath);
            return relativePath;
            
        } catch (Exception e) {
            // 转换失败时清理临时文件
            if (tempPdfFile.exists()) {
                try {
                    tempPdfFile.delete();
                } catch (Exception deleteEx) {
                    log.warn("清理临时文件失败: {}", deleteEx.getMessage());
                }
            }
            throw e;
        }
    }

    /**
     * 获取PPT文件
     */
    private File getPptFile(String filePath) {
        // 如果是绝对路径，直接使用
        if (filePath.startsWith("/") || filePath.contains(":")) {
            return new File(filePath);
        }
        
        // 尝试在上传目录中查找
        File file = new File(uploadPath, filePath);
        if (file.exists()) {
            return file;
        }
        
        // 尝试在临时目录中查找
        file = new File(uploadTmpPath, filePath);
        if (file.exists()) {
            return file;
        }
        
        // 直接作为相对于工作目录的路径
        return new File(filePath);
    }

    /**
     * 转换PPT为PDF
     */
    private byte[] convertPptToPdf(File pptFile, KnoBase knoBase) throws Exception {
        log.info("开始转换PPT为PDF，文件: {}", pptFile.getAbsolutePath());

        Presentation presentation = null;
        ByteArrayOutputStream outputStream = null;
        FileInputStream fileInputStream = null;
        
        try {
            // 加载Aspose Slides许可证
            AsposeUtils.loadLicense(MicrosoftConstants.PPTX_TO_OTHER);

            // 加载PPT文档
            fileInputStream = new FileInputStream(pptFile);
            presentation = new Presentation(fileInputStream);

            // 创建PDF选项
            PdfOptions pdfOptions = createPdfOptions();

            // 转换为PDF字节数组
            outputStream = new ByteArrayOutputStream();
            presentation.save(outputStream, SaveFormat.Pdf, pdfOptions);

            byte[] pdfBytes = outputStream.toByteArray();
            log.info("PPT转PDF成功，原始PDF大小: {} bytes", pdfBytes.length);

            return pdfBytes;

        } catch (OutOfMemoryError e) {
            log.error("内存不足，PPT转PDF失败: {}", e.getMessage());
            // 强制垃圾回收
            System.gc();
            throw new RuntimeException("内存不足，无法转换PPT文件，请稍后重试或联系管理员", e);
        } catch (Exception e) {
            log.error("PPT转PDF失败: {}", e.getMessage(), e);
            throw new RuntimeException("PPT转PDF失败: " + e.getMessage(), e);
        } finally {
            // 释放资源
            try {
                if (presentation != null) {
                    presentation.dispose();
                }
            } catch (Exception e) {
                log.warn("释放Presentation资源失败: {}", e.getMessage());
            }
            
            try {
                if (outputStream != null) {
                    outputStream.close();
                }
            } catch (Exception e) {
                log.warn("关闭输出流失败: {}", e.getMessage());
            }
            
            try {
                if (fileInputStream != null) {
                    fileInputStream.close();
                }
            } catch (Exception e) {
                log.warn("关闭输入流失败: {}", e.getMessage());
            }
            
            // 建议进行垃圾回收
            System.gc();
        }
    }

    /**
     * 创建PDF转换选项
     */
    private PdfOptions createPdfOptions() {
        PdfOptions options = new PdfOptions();
        
        // 设置高质量转换
        options.setSufficientResolution(150); // 提高分辨率，获得更好的显示效果
        options.setSaveMetafilesAsPng(false); // 保持矢量图形格式
        options.setShowHiddenSlides(false); // 不包含隐藏幻灯片
        
        // 可以根据需要添加更多选项
        // options.setBestImagesCompressionRatio(true); // 启用最佳图像压缩
        
        return options;
    }

    /**
     * 转换PPT为PDF并添加水印，直接保存到文件
     */
    private void convertPptToPdfToFile(File pptFile, File outputPdfFile, KnoBase knoBase, String watermarkText) throws Exception {
        log.info("开始转换PPT为PDF文件，输出到: {}", outputPdfFile.getAbsolutePath());

        Presentation presentation = null;
        FileInputStream fileInputStream = null;
        
        try {
            // 加载Aspose Slides许可证
            AsposeUtils.loadLicense(MicrosoftConstants.PPTX_TO_OTHER);

            // 加载PPT文档
            fileInputStream = new FileInputStream(pptFile);
            presentation = new Presentation(fileInputStream);

            // 创建PDF选项
            PdfOptions pdfOptions = createPdfOptions();

            // 先转换为临时PDF文件（不带水印）
            File tempPdfFile = new File(outputPdfFile.getParent(), "temp_" + outputPdfFile.getName());
            try (FileOutputStream fos = new FileOutputStream(tempPdfFile)) {
                presentation.save(fos, SaveFormat.Pdf, pdfOptions);
            }
            
            log.info("PPT转PDF完成，开始添加水印");
            
            // 添加水印并保存到最终文件
            addWatermarkToPdfFile(tempPdfFile, outputPdfFile, knoBase, watermarkText);
            
            // 删除临时文件
            if (tempPdfFile.exists()) {
                tempPdfFile.delete();
            }
            
            log.info("PDF水印添加完成，文件大小: {} bytes", outputPdfFile.length());

        } catch (OutOfMemoryError e) {
            log.error("内存不足，PPT转PDF失败: {}", e.getMessage());
            // 强制垃圾回收
            System.gc();
            throw new RuntimeException("内存不足，无法转换PPT文件，请稍后重试或联系管理员", e);
        } catch (Exception e) {
            log.error("PPT转PDF失败: {}", e.getMessage(), e);
            throw new RuntimeException("PPT转PDF失败: " + e.getMessage(), e);
        } finally {
            // 释放资源
            try {
                if (presentation != null) {
                    presentation.dispose();
                }
            } catch (Exception e) {
                log.warn("释放Presentation资源失败: {}", e.getMessage());
            }
            
            try {
                if (fileInputStream != null) {
                    fileInputStream.close();
                }
            } catch (Exception e) {
                log.warn("关闭输入流失败: {}", e.getMessage());
            }
            
            // 建议进行垃圾回收
            System.gc();
        }
    }

    /**
     * 为PDF文件添加水印
     */
    private void addWatermarkToPdfFile(File inputPdfFile, File outputPdfFile, KnoBase knoBase, String watermarkText) throws Exception {
        log.info("开始为PDF文件添加水印，输入: {}, 输出: {}", inputPdfFile.getName(), outputPdfFile.getName());

        try (FileInputStream fis = new FileInputStream(inputPdfFile);
             FileOutputStream fos = new FileOutputStream(outputPdfFile);
             PdfReader reader = new PdfReader(fis);
             PdfWriter writer = new PdfWriter(fos);
             PdfDocument pdfDoc = new PdfDocument(reader, writer)) {

            // 设置PDF文档属性
            setPdfDocumentInfo(pdfDoc, knoBase);

            int numberOfPages = pdfDoc.getNumberOfPages();
            log.info("PDF总页数: {}", numberOfPages);

            // 准备字体
            PdfFont font = createFont();

            // 准备水印文本
            String finalWatermarkText = (watermarkText != null && !watermarkText.trim().isEmpty()) 
                ? watermarkText.trim() 
                : defaultWatermarkText;

            // 为每一页添加水印
            for (int i = 1; i <= numberOfPages; i++) {
                PdfPage page = pdfDoc.getPage(i);
                Rectangle pageSize = page.getPageSize();
                
                // 在页面内容之上添加水印
                PdfCanvas pdfCanvas = new PdfCanvas(page.newContentStreamAfter(), page.getResources(), pdfDoc);
                
                // 添加水印
                addWatermarkToPage(pdfCanvas, pageSize, finalWatermarkText, font);
                
                // 添加页脚信息
                addFooterToPage(pdfCanvas, pageSize, knoBase, font, i, numberOfPages);
            }

            log.info("PDF水印添加完成");
        }catch (Exception e) {
            log.error("为PDF文件添加水印失败: {}", e.getMessage(), e);
            throw new RuntimeException("为PDF文件添加水印失败: " + e.getMessage(), e);
        }
    }

    /**
     * 设置PDF文档信息
     */
    private void setPdfDocumentInfo(PdfDocument pdfDoc, KnoBase knoBase) {
        PdfDocumentInfo info = pdfDoc.getDocumentInfo();
        info.setTitle(knoBase.getTitle());
        info.setAuthor(knoBase.getCreatorName());
        info.setCreator("知识管理系统");
        info.setSubject("知识库PPT文档");
        if(knoBase.getKeywords() != null && !knoBase.getKeywords().trim().isEmpty()) {
            info.setKeywords(knoBase.getKeywords());
        }
        info.setProducer("Knowledge Management System PDF Generator");
    }

    /**
     * 创建字体
     */
    private PdfFont createFont() throws Exception {
        try {
            // 尝试使用中文字体
            return PdfFontFactory.createFont("STSong-Light", "UniGB-UCS2-H");
        } catch (Exception e) {
            log.warn("无法加载中文字体，使用默认字体: {}", e.getMessage());
            // 回退到默认字体
            return PdfFontFactory.createFont();
        }
    }

    /**
     * 为页面添加水印
     */
    private void addWatermarkToPage(PdfCanvas canvas, Rectangle pageSize, String watermarkText, PdfFont font) {
        // 保存当前图形状态
        canvas.saveState();

        // 设置透明度
        PdfExtGState gState = new PdfExtGState();
        gState.setFillOpacity(watermarkOpacity);
        canvas.setExtGState(gState);

        // 添加主水印（中心大水印）
        addCenterWatermark(canvas, pageSize, watermarkText, font);

        // 添加重复小水印
        addRepeatedWatermarks(canvas, pageSize, watermarkText, font);

        // 恢复图形状态
        canvas.restoreState();
    }

    /**
     * 添加中心大水印
     */
    private void addCenterWatermark(PdfCanvas canvas, Rectangle pageSize, String watermarkText, PdfFont font) {
        float x = pageSize.getWidth() / 2;
        float y = pageSize.getHeight() / 2;
        float angle = (float) Math.toRadians(225); // 改回45度角，从左下到右上

        
        canvas.saveState();
        
        // 移动到中心点
        canvas.concatMatrix(1, 0, 0, 1, x, y);
        
        // 镜面翻转 - 水平镜像（X轴翻转）
        canvas.concatMatrix(-1, 0, 0, 1, 0, 0);

        canvas.concatMatrix(
            (float) Math.cos(angle), (float) Math.sin(angle),
            -(float) Math.sin(angle), (float) Math.cos(angle), 
            0, 0
        ); 

        
        canvas.setFontAndSize(font, 72); // 大字体
        canvas.setFillColor(ColorConstants.LIGHT_GRAY);
        
        canvas.beginText();
        canvas.moveText(-watermarkText.length() * 18, -20); // 调整位置使文字居中
        canvas.showText(watermarkText);
        canvas.endText();
        
        canvas.restoreState();
    }

    /**
     * 添加重复小水印
     */
    private void addRepeatedWatermarks(PdfCanvas canvas, Rectangle pageSize, String watermarkText, PdfFont font) {
        int rows = 3;
        int cols = 4;
        float angle = (float) Math.toRadians(225); // 改回45度角，从左下到右上
        
        for (int i = 0; i < rows; i++) {
            for (int j = 0; j < cols; j++) {
                float x = pageSize.getWidth() * (0.15f + j * 0.25f);
                float y = pageSize.getHeight() * (0.2f + i * 0.3f);
                
                canvas.saveState();
                
                // 移动到指定位置
                canvas.concatMatrix(1, 0, 0, 1, x, y);
                
                // 镜面翻转 - 水平镜像（X轴翻转）
                canvas.concatMatrix(-1, 0, 0, 1, 0, 0);
                canvas.concatMatrix(
                    (float) Math.cos(angle), (float) Math.sin(angle),
                    -(float) Math.sin(angle), (float) Math.cos(angle), 
                    0, 0
                ); 
                
                canvas.setFontAndSize(font, 24); // 小字体
                canvas.setFillColor(ColorConstants.LIGHT_GRAY);
                
                canvas.beginText();
                canvas.moveText(-watermarkText.length() * 6, -8);
                canvas.showText(watermarkText);
                canvas.endText();
                
                canvas.restoreState();
            }
        }
    }

    /**
     * 添加页脚信息
     */
    private void addFooterToPage(PdfCanvas canvas, Rectangle pageSize, KnoBase knoBase, PdfFont font, int currentPage, int totalPages) {
        canvas.saveState();
        
        // 设置不透明度（页脚信息需要清晰可见）
        PdfExtGState gState = new PdfExtGState();
        gState.setFillOpacity(1.0f);
        canvas.setExtGState(gState);

        canvas.setFontAndSize(font, 9);
        canvas.setFillColor(ColorConstants.DARK_GRAY);

        // 页脚位置 - 移到页面底部
        float footerY = pageSize.getHeight() - 30; // 距离页面底边30个单位
        
        // 左侧：页码和生成时间 - 水平旋转180度
        String leftText = String.format("第%d页/共%d页 | %s", 
            currentPage, totalPages, 
            LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")));
        
        canvas.saveState();
        canvas.concatMatrix(1, 0, 0, 1, 50, footerY); // 移动到左侧位置
        // 水平旋转180度 - 左右翻转
        canvas.concatMatrix(1, 0, 0, -1, 0, 0);
        canvas.beginText();
        //canvas.moveText(-leftText.length() * 4, 0); // 调整位置补偿
        canvas.showText(leftText);
        canvas.endText();
        canvas.restoreState();

        // 中间：创建者 - 水平旋转180度
        String creatorText = "创建者: " + (knoBase.getCreatorName() != null ? knoBase.getCreatorName() : "");
        canvas.saveState();
        canvas.concatMatrix(1, 0, 0, 1, pageSize.getWidth() / 2 - 50, footerY); // 移动到中间位置
        // 水平旋转180度 - 左右翻转
        canvas.concatMatrix(1, 0, 0, -1, 0, 0);
        canvas.beginText();
        //canvas.moveText(-creatorText.length() * 3, 0); // 调整位置补偿
        canvas.showText(creatorText);
        canvas.endText();
        canvas.restoreState();

        // 右侧：文档标题和部门 - 水平旋转180度
        String rightText = String.format("%s | %s", 
            truncateText(knoBase.getTitle(), 30), 
            knoBase.getOrganName() != null ? knoBase.getOrganName() : "");
        
        canvas.saveState();
        canvas.concatMatrix(1, 0, 0, 1, pageSize.getWidth() - 250, footerY); // 移动到右侧位置
        // 水平旋转180度 - 左右翻转
        canvas.concatMatrix(1, 0, 0, -1, 0, 0);
        canvas.beginText();
        //canvas.moveText(-rightText.length() * 3, 0); // 调整位置补偿
        canvas.showText(rightText);
        canvas.endText();
        canvas.restoreState();

        // 在页脚上方添加一条分隔线
        canvas.setStrokeColor(ColorConstants.LIGHT_GRAY);
        canvas.setLineWidth(0.5f);
        canvas.moveTo(50, footerY + 15); // 分隔线在页脚文字上方15个单位
        canvas.lineTo(pageSize.getWidth() - 50, pageSize.getHeight() - 15);
        canvas.stroke();
        
        canvas.restoreState();
    }

    /**
     * 截断文本到指定长度
     */
    private String truncateText(String text, int maxLength) {
        if (text == null) {
            return "";
        }
        if (text.length() <= maxLength) {
            return text;
        }
        return text.substring(0, maxLength - 3) + "...";
    }

    /**
     * 检查知识库是否可以转换为PDF
     */
    public boolean canConvertToPdf(String knowledgeId) {
        try {
            KnoBase knoBase = knoBaseService.findKnoBaseById(knowledgeId);
            if (knoBase == null) {
                return false;
            }

            // 检查状态
            if (knoBase.getStatus() != 1) {
                //return false;
            }

            // 检查是否有PPT文件
            if (knoBase.getMainFilePath() == null || knoBase.getMainFilePath().trim().isEmpty()) {
                return false;
            }

            // 检查文件是否存在
            File pptFile = getPptFile(knoBase.getMainFilePath());
            if (!pptFile.exists()) {
                return false;
            }

            // 检查文件格式
            String fileName = pptFile.getName().toLowerCase();
            return fileName.endsWith(".ppt") || fileName.endsWith(".pptx") || 
                   fileName.endsWith(".pptm") || fileName.endsWith(".potx");

        } catch (Exception e) {
            log.error("检查PDF转换能力失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查知识库是否包含可转换的PPT文件
     */
    public boolean checkPptConvertible(String knowledgeId) {
        try {
            log.debug("检查知识库PPT转换可行性，知识ID: {}", knowledgeId);
            
            // 获取知识库信息
            KnoBase knoBase = knoBaseService.findKnoBaseById(knowledgeId);
            if (knoBase == null) {
                log.warn("知识库不存在，ID: {}", knowledgeId);
                return false;
            }
            
            // 检查状态是否为已发布
            if (!"1".equals(knoBase.getStatus())) {
                log.warn("知识库未发布，ID: {}", knowledgeId);
                return false;
            }
            
            // 检查是否有主文件路径
            String mainFilePath = knoBase.getMainFilePath();
            if (mainFilePath == null || mainFilePath.trim().isEmpty()) {
                log.warn("知识库无主文件路径，ID: {}", knowledgeId);
                return false;
            }
            
            // 检查文件是否为PPT格式
            String lowerPath = mainFilePath.toLowerCase();
            if (!lowerPath.endsWith(".ppt") && !lowerPath.endsWith(".pptx")) {
                log.warn("主文件不是PPT格式，文件路径: {}", mainFilePath);
                return false;
            }
            
            // 检查物理文件是否存在
            File pptFile = new File(mainFilePath);
            if (!pptFile.exists() || !pptFile.isFile()) {
                log.warn("PPT物理文件不存在，文件路径: {}", mainFilePath);
                return false;
            }
            
            log.debug("知识库PPT转换可行，ID: {}", knowledgeId);
            return true;
            
        } catch (Exception e) {
            log.error("检查PPT转换可行性失败，知识ID: {}", knowledgeId, e);
            return false;
        }
    }

    /**
     * 清理过期的临时PDF文件
     * 定时任务：每天凌晨2点执行一次
     */
    // @Scheduled(cron = "0 0 2 * * ?")
    public void cleanExpiredTempFiles() {
        try {
            String tempDirPath = "temp/pdf";
            File tempDir = new File(tempDirPath);
            
            if (!tempDir.exists()) {
                return;
            }
            
            log.info("开始清理过期临时PDF文件，目录: {}", tempDirPath);
            
            long currentTime = System.currentTimeMillis();
            long maxAge = 24 * 60 * 60 * 1000; // 24小时
            int cleanedCount = 0;
            
            File[] files = tempDir.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isFile() && file.getName().endsWith(".pdf")) {
                        long fileAge = currentTime - file.lastModified();
                        if (fileAge > maxAge) {
                            if (file.delete()) {
                                cleanedCount++;
                                log.debug("删除过期临时文件: {}", file.getName());
                            }
                        }
                    }
                }
            }
            
            log.info("临时PDF文件清理完成，删除文件数: {}", cleanedCount);
            
        } catch (Exception e) {
            log.error("清理临时PDF文件失败", e);
        }
    }
} 