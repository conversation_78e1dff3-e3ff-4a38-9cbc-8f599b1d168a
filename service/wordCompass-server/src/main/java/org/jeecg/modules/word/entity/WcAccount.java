package org.jeecg.modules.word.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.system.base.entity.JeecgEntity;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * @Description: 账号信息
 * @Author: wordCompass
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="WcAccount对象", description="账号信息")
@TableName("wc_account")
public class WcAccount extends JeecgEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 昵称
     */
    @Excel(name = "昵称", width = 20)
    @ApiModelProperty(value = "昵称")
    private String nickname;

    /**
     * 手机号(唯一)
     */
    @Excel(name = "手机号", width = 15)
    @ApiModelProperty(value = "手机号")
    private String mobile;

    /**
     * 密码
     */
    @ApiModelProperty(value = "密码")
    private String password;

    /**
     * 盐值
     */
    @ApiModelProperty(value = "盐值")
    private String salt;

    /**
     * 邮箱
     */
    @Excel(name = "邮箱", width = 25)
    @ApiModelProperty(value = "邮箱")
    private String email;

    /**
     * 性别(1-男,2-女)
     */
    @Excel(name = "性别", width = 10, dicCode = "gender")
    @ApiModelProperty(value = "性别")
    private Integer gender;

    /**
     * 头像附件
     */
    @ApiModelProperty(value = "头像附件")
    private String avatar;

    /**
     * 真实姓名
     */
    @Excel(name = "真实姓名", width = 20)
    @ApiModelProperty(value = "真实姓名")
    private String realName;

    /**
     * 最后登录时间
     */
    @Excel(name = "最后登录时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最后登录时间")
    private Date lastLoginTime;

    /**
     * 最后登录IP
     */
    @ApiModelProperty(value = "最后登录IP")
    private String lastLoginIp;

    /**
     * 状态(0-冻结,1-正常)
     */
    @Excel(name = "状态", width = 10, dicCode = "account_status")
    @ApiModelProperty(value = "状态")
    private Integer status;

    /**
     * 账号类型(1-普通用户,2-VIP用户)
     */
    @Excel(name = "账号类型", width = 15, dicCode = "account_type")
    @ApiModelProperty(value = "账号类型")
    private Integer accountType;

    /**
     * VIP到期时间
     */
    @Excel(name = "VIP到期时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "VIP到期时间")
    private Date vipExpireTime;

    /**
     * 备注
     */
    @Excel(name = "备注", width = 30)
    @ApiModelProperty(value = "备注")
    private String remark;
} 