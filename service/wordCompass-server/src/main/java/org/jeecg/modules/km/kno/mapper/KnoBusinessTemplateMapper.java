package org.jeecg.modules.km.kno.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.km.kno.entity.KnoBusinessTemplate;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * @Description: 知识业务模板
 * @Author: jeecg-boot
 * @Date:   2024-01-01
 * @Version: V1.0
 */
public interface KnoBusinessTemplateMapper extends BaseMapper<KnoBusinessTemplate> {

    /**
     * 分页查询业务模板列表
     * @param page 分页参数
     * @param name 模板名称
     * @param status 状态
     * @return 分页结果
     */
    IPage<KnoBusinessTemplate> selectPageList(Page<KnoBusinessTemplate> page, 
                                             @Param("name") String name, 
                                             @Param("status") Integer status);

    /**
     * 根据原始ID查询所有版本
     * @param originId 原始ID
     * @return 版本列表
     */
    List<KnoBusinessTemplate> selectByOriginId(@Param("originId") String originId);

    /**
     * 更新其他版本为非最新版本
     * @param originId 原始ID
     * @param excludeId 排除的ID
     */
    void updateOtherVersionNotLatest(@Param("originId") String originId, @Param("excludeId") String excludeId);

} 