<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.word.mapper.WcTaskMapper">

    <!-- 根据账号ID查询任务列表 -->
    <select id="selectByAccountId" resultType="org.jeecg.modules.word.entity.WcTask">
        SELECT * FROM wc_task 
        WHERE account_id = #{accountId} 
        AND del_flag = 0
        ORDER BY create_time DESC
    </select>
    
    <!-- 根据学生ID查询任务列表 -->
    <select id="selectByStudentId" resultType="org.jeecg.modules.word.entity.WcTask">
        SELECT * FROM wc_task 
        WHERE student_id = #{studentId} 
        AND del_flag = 0
        ORDER BY create_time DESC
    </select>
    
    <!-- 根据任务状态查询任务列表 -->
    <select id="selectByStatus" resultType="org.jeecg.modules.word.entity.WcTask">
        SELECT * FROM wc_task 
        WHERE status = #{status} 
        AND del_flag = 0
        ORDER BY create_time DESC
    </select>
    
    <!-- 根据任务类型查询任务列表 -->
    <select id="selectByTaskType" resultType="org.jeecg.modules.word.entity.WcTask">
        SELECT * FROM wc_task 
        WHERE task_type = #{taskType} 
        AND del_flag = 0
        ORDER BY create_time DESC
    </select>

</mapper>
