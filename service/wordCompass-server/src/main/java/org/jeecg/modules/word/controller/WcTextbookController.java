package org.jeecg.modules.word.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.word.entity.WcTextbook;
import org.jeecg.modules.word.entity.WcTextbookDetail;
import org.jeecg.modules.word.service.IWcTextbookService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: 教材基本信息
 * @Author: wordCompass
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Api(tags = "教材基本信息管理")
@RestController
@RequestMapping("/word/textbook")
@Slf4j
public class WcTextbookController {

    @Autowired
    private IWcTextbookService wcTextbookService;

    /**
     * 分页列表查询
     */
    @ApiOperation(value = "教材基本信息-分页列表查询", notes = "教材基本信息-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(WcTextbook wcTextbook,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<WcTextbook> queryWrapper = QueryGenerator.initQueryWrapper(wcTextbook, req.getParameterMap());
        queryWrapper.orderByAsc("sort_order").orderByDesc("create_time");
        Page<WcTextbook> page = new Page<WcTextbook>(pageNo, pageSize);
        IPage<WcTextbook> pageList = wcTextbookService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     */
    @ApiOperation(value = "教材基本信息-添加", notes = "教材基本信息-添加")
    @PostMapping(value = "/add")
    @AutoLog(value = "教材基本信息-添加")
    public Result<?> add(@RequestBody WcTextbook wcTextbook) {
        wcTextbookService.save(wcTextbook);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     */
    @ApiOperation(value = "教材基本信息-编辑", notes = "教材基本信息-编辑")
    @PutMapping(value = "/edit")
    @AutoLog(value = "教材基本信息-编辑")
    public Result<?> edit(@RequestBody WcTextbook wcTextbook) {
        wcTextbookService.updateById(wcTextbook);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     */
    @ApiOperation(value = "教材基本信息-通过id删除", notes = "教材基本信息-通过id删除")
    @DeleteMapping(value = "/delete")
    @AutoLog(value = "教材基本信息-删除")
    public Result<?> delete(@RequestParam(name = "id") String id) {
        wcTextbookService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     */
    @ApiOperation(value = "教材基本信息-批量删除", notes = "教材基本信息-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    @AutoLog(value = "教材基本信息-批量删除")
    public Result<?> deleteBatch(@RequestParam(name = "ids") String ids) {
        this.wcTextbookService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     */
    @ApiOperation(value = "教材基本信息-通过id查询", notes = "教材基本信息-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id") String id) {
        WcTextbook wcTextbook = wcTextbookService.getById(id);
        if (wcTextbook == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(wcTextbook);
    }


    /**
     * 根据年级查询教材列表
     */
    @ApiOperation(value = "根据年级查询教材列表", notes = "根据年级查询教材列表")
    @GetMapping(value = "/listByGrade")
    public Result<?> listByGrade(@RequestParam(name = "grade") String grade) {
        List<WcTextbook> list = wcTextbookService.getTextbooksByGrade(grade);
        return Result.OK(list);
    }

    /**
     * 根据年级和版本查询教材列表
     */
    @ApiOperation(value = "根据年级和版本查询教材列表", notes = "根据年级和版本查询教材列表")
    @GetMapping(value = "/listByGradeAndVersion")
    public Result<?> listByGradeAndVersion(@RequestParam(name = "grade") String grade,
                                           @RequestParam(name = "version") String version) {
        List<WcTextbook> list = wcTextbookService.getTextbooksByGradeAndVersion(grade, version);
        return Result.OK(list);
    }

    // ==================== 教材明细管理相关API ====================

    /**
     * 获取教材树形结构
     */
    @AutoLog(value = "教材明细-获取树形结构")
    @ApiOperation(value = "教材明细-获取树形结构", notes = "教材明细-获取树形结构")
    @GetMapping(value = "/detail/tree")
    public Result<List<WcTextbookDetail>> getTextbookTree(@RequestParam(name = "textbookId") String textbookId) {
        List<WcTextbookDetail> tree = wcTextbookService.getTextbookTree(textbookId);
        return Result.OK(tree);
    }

    /**
     * 获取教材明细列表
     */
    @AutoLog(value = "教材明细-获取列表")
    @ApiOperation(value = "教材明细-获取列表", notes = "教材明细-获取列表")
    @GetMapping(value = "/detail/list")
    public Result<List<WcTextbookDetail>> getTextbookDetails(@RequestParam(name = "textbookId") String textbookId,
                                                             @RequestParam(name = "type", required = false) String type) {
        List<WcTextbookDetail> list = wcTextbookService.getTextbookDetails(textbookId, type);
        return Result.OK(list);
    }

    /**
     * 保存教材明细
     */
    @AutoLog(value = "教材明细-保存")
    @ApiOperation(value = "教材明细-保存", notes = "教材明细-保存")
    @PostMapping(value = "/detail/save")
    public Result<String> saveTextbookDetail(@RequestBody WcTextbookDetail detail) {
        boolean success = wcTextbookService.saveTextbookDetail(detail);
        return success ? Result.OK("保存成功") : Result.error("保存失败");
    }

    /**
     * 更新教材明细
     */
    @AutoLog(value = "教材明细-更新")
    @ApiOperation(value = "教材明细-更新", notes = "教材明细-更新")
    @PutMapping(value = "/detail/update")
    public Result<String> updateTextbookDetail(@RequestBody WcTextbookDetail detail) {
        boolean success = wcTextbookService.updateTextbookDetail(detail);
        return success ? Result.OK("更新成功") : Result.error("更新失败");
    }

    /**
     * 删除教材明细
     */
    @AutoLog(value = "教材明细-删除")
    @ApiOperation(value = "教材明细-删除", notes = "教材明细-删除")
    @DeleteMapping(value = "/detail/delete")
    public Result<String> deleteTextbookDetail(@RequestParam(name = "id") String id) {
        boolean success = wcTextbookService.deleteTextbookDetail(id);
        return success ? Result.OK("删除成功") : Result.error("删除失败");
    }

    /**
     * 获取教材明细详情
     */
    @AutoLog(value = "教材明细-获取详情")
    @ApiOperation(value = "教材明细-获取详情", notes = "教材明细-获取详情")
    @GetMapping(value = "/detail/detail")
    public Result<WcTextbookDetail> getTextbookDetailById(@RequestParam(name = "id") String id) {
        WcTextbookDetail detail = wcTextbookService.getTextbookDetailById(id);
        return Result.OK(detail);
    }
} 