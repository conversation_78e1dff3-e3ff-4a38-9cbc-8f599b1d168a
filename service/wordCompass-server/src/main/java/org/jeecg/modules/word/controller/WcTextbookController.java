package org.jeecg.modules.word.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.word.entity.WcTextbook;
import org.jeecg.modules.word.service.IWcTextbookService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * @Description: 教材基本信息
 * @Author: wordCompass
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Api(tags = "教材基本信息管理")
@RestController
@RequestMapping("/word/textbook")
@Slf4j
public class WcTextbookController extends JeecgController<WcTextbook, IWcTextbookService> {

    @Autowired
    private IWcTextbookService wcTextbookService;

    /**
     * 分页列表查询
     */
    @ApiOperation(value = "教材基本信息-分页列表查询", notes = "教材基本信息-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(WcTextbook wcTextbook,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                   HttpServletRequest req) {
        QueryWrapper<WcTextbook> queryWrapper = QueryGenerator.initQueryWrapper(wcTextbook, req.getParameterMap());
        queryWrapper.orderByAsc("sort_order").orderByDesc("create_time");
        Page<WcTextbook> page = new Page<WcTextbook>(pageNo, pageSize);
        IPage<WcTextbook> pageList = wcTextbookService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     */
    @ApiOperation(value = "教材基本信息-添加", notes = "教材基本信息-添加")
    @PostMapping(value = "/add")
    @AutoLog(value = "教材基本信息-添加")
    public Result<?> add(@RequestBody WcTextbook wcTextbook) {
        wcTextbookService.save(wcTextbook);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     */
    @ApiOperation(value = "教材基本信息-编辑", notes = "教材基本信息-编辑")
    @PutMapping(value = "/edit")
    @AutoLog(value = "教材基本信息-编辑")
    public Result<?> edit(@RequestBody WcTextbook wcTextbook) {
        wcTextbookService.updateById(wcTextbook);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     */
    @ApiOperation(value = "教材基本信息-通过id删除", notes = "教材基本信息-通过id删除")
    @DeleteMapping(value = "/delete")
    @AutoLog(value = "教材基本信息-删除")
    public Result<?> delete(@RequestParam(name = "id") String id) {
        wcTextbookService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     */
    @ApiOperation(value = "教材基本信息-批量删除", notes = "教材基本信息-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    @AutoLog(value = "教材基本信息-批量删除")
    public Result<?> deleteBatch(@RequestParam(name = "ids") String ids) {
        this.wcTextbookService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     */
    @ApiOperation(value = "教材基本信息-通过id查询", notes = "教材基本信息-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<?> queryById(@RequestParam(name = "id") String id) {
        WcTextbook wcTextbook = wcTextbookService.getById(id);
        if (wcTextbook == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(wcTextbook);
    }

    /**
     * 导出excel
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, WcTextbook wcTextbook) {
        return super.exportXls(request, wcTextbook, WcTextbook.class, "教材基本信息");
    }

    /**
     * 通过excel导入数据
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, WcTextbook.class);
    }

    /**
     * 根据年级查询教材列表
     */
    @ApiOperation(value = "根据年级查询教材列表", notes = "根据年级查询教材列表")
    @GetMapping(value = "/listByGrade")
    public Result<?> listByGrade(@RequestParam(name = "grade") String grade) {
        List<WcTextbook> list = wcTextbookService.getTextbooksByGrade(grade);
        return Result.OK(list);
    }

    /**
     * 根据年级和版本查询教材列表
     */
    @ApiOperation(value = "根据年级和版本查询教材列表", notes = "根据年级和版本查询教材列表")
    @GetMapping(value = "/listByGradeAndVersion")
    public Result<?> listByGradeAndVersion(@RequestParam(name = "grade") String grade,
                                           @RequestParam(name = "version") String version) {
        List<WcTextbook> list = wcTextbookService.getTextbooksByGradeAndVersion(grade, version);
        return Result.OK(list);
    }

} 