package org.jeecg.modules.word.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.word.entity.WcTextbook;
import org.jeecg.modules.word.entity.WcTextbookDetail;
import org.jeecg.modules.word.mapper.WcTextbookDetailMapper;
import org.jeecg.modules.word.mapper.WcTextbookMapper;
import org.jeecg.modules.word.service.IWcTextbookService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * @Description: 教材基本信息
 * @Author: wordCompass
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Service
public class WcTextbookServiceImpl extends ServiceImpl<WcTextbookMapper, WcTextbook> implements IWcTextbookService {

    @Autowired
    private WcTextbookMapper wcTextbookMapper;

    @Autowired
    private WcTextbookDetailMapper wcTextbookDetailMapper;

    @Override
    public List<WcTextbook> getTextbooksByGrade(String grade) {
        return wcTextbookMapper.selectByGrade(grade);
    }

    @Override
    public List<WcTextbook> getTextbooksByGradeAndVersion(String grade, String version) {
        return wcTextbookMapper.selectByGradeAndVersion(grade, version);
    }

    @Override
    public List<WcTextbookDetail> getTextbookTree(String textbookId) {
        return wcTextbookDetailMapper.selectTreeByTextbookId(textbookId);
    }

    @Override
    public List<WcTextbookDetail> getTextbookDetails(String textbookId, String type) {
        return wcTextbookDetailMapper.selectByTextbookIdAndType(textbookId, type);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveTextbookDetail(WcTextbookDetail detail) {
        return wcTextbookDetailMapper.insert(detail) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTextbookDetail(WcTextbookDetail detail) {
        return wcTextbookDetailMapper.updateById(detail) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTextbookDetail(String detailId) {
        // 检查是否有子节点
        List<WcTextbookDetail> children = wcTextbookDetailMapper.selectByParentId(detailId);
        if (children != null && !children.isEmpty()) {
            throw new RuntimeException("该节点下还有子节点，无法删除");
        }
        return wcTextbookDetailMapper.deleteById(detailId) > 0;
    }

    @Override
    public WcTextbookDetail getTextbookDetailById(String detailId) {
        return wcTextbookDetailMapper.selectById(detailId);
    }
} 