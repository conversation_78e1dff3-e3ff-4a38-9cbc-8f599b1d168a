package org.jeecg.modules.word.service.impl;

import org.jeecg.modules.word.entity.WcTextbook;
import org.jeecg.modules.word.mapper.WcTextbookMapper;
import org.jeecg.modules.word.service.IWcTextbookService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;

/**
 * @Description: 教材基本信息
 * @Author: wordCompass
 * @Date: 2025-01-15
 * @Version: V1.0
 */
@Service
public class WcTextbookServiceImpl extends ServiceImpl<WcTextbookMapper, WcTextbook> implements IWcTextbookService {

    @Autowired
    private WcTextbookMapper wcTextbookMapper;

    @Override
    public List<WcTextbook> getTextbooksByGrade(String grade) {
        return wcTextbookMapper.selectByGrade(grade);
    }

    @Override
    public List<WcTextbook> getTextbooksByGradeAndVersion(String grade, String version) {
        return wcTextbookMapper.selectByGradeAndVersion(grade, version);
    }
} 