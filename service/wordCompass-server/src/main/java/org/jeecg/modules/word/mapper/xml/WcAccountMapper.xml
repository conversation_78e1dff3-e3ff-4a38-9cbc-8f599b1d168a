<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.word.mapper.WcAccountMapper">

    <!-- 根据手机号查询账号 -->
    <select id="selectByMobile" resultType="org.jeecg.modules.word.entity.WcAccount">
        SELECT * FROM wc_account 
        WHERE mobile = #{mobile} 
        AND del_flag = 0
    </select>
    
    <!-- 根据邮箱查询账号 -->
    <select id="selectByEmail" resultType="org.jeecg.modules.word.entity.WcAccount">
        SELECT * FROM wc_account 
        WHERE email = #{email} 
        AND del_flag = 0
    </select>
    
    <!-- 根据账号类型查询账号列表 -->
    <select id="selectByAccountType" resultType="org.jeecg.modules.word.entity.WcAccount">
        SELECT * FROM wc_account 
        WHERE account_type = #{accountType} 
        AND del_flag = 0
        ORDER BY create_time DESC
    </select>

</mapper>
