package org.jeecg.modules.km.kno.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.stereotype.Service;
import org.springframework.web.client.ResponseErrorHandler;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 知识脚本服务类
 * 用于调用知识脚本服务API
 * yf add 2025-08-11
 */
@Slf4j
@Service
public class KnowledgeScriptService {

    @Value("${knowledge.service.base-url}")
    private String knowledgeServiceBaseUrl;

    @Value("${knowledge.service.connect-timeout:30000}")
    private int connectTimeout;

    @Value("${knowledge.service.read-timeout:600000}")
    private int readTimeout;


    @Value("${knowledge.service.max-retries:3}")
    private int maxRetries;

    @Value("${knowledge.service.retry-interval:1000}")
    private int retryInterval;


    private RestTemplate knowledgeRestTemplate;

    /**
     * 初始化专门用于知识脚本服务的RestTemplate
     * 设置更长的超时时间以适应知识脚本处理的长时间操作
     */
    @PostConstruct
    public void initKnowledgeRestTemplate() {
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        // 从配置文件读取超时时间
        requestFactory.setConnectTimeout(connectTimeout);
        requestFactory.setReadTimeout(readTimeout);


        knowledgeRestTemplate = new RestTemplate(requestFactory);
        // 初始化重试策略
        knowledgeRestTemplate.setErrorHandler(new KnowledgeScriptService.KnowledgeServiceErrorHandler(maxRetries, retryInterval));
        // 解决乱码问题
        knowledgeRestTemplate.getMessageConverters().set(1, new StringHttpMessageConverter(StandardCharsets.UTF_8));

        log.info("知识服务RestTemplate初始化完成，连接超时: {}ms，读取超时: {}ms，最大重试次数: {}，重试间隔: {}ms", connectTimeout, readTimeout, maxRetries, retryInterval);
    }




    /**
     * 根据 detailId 生成话术
     * @param detailId 详情ID
     * @return 同步结果
     */
    public Map<String, Object> generateSrciptByDetailId(String detailId) {
        try {
            log.info("开始生成话术，知识明细ID: {}", detailId);
            Map <String, Object> result = new HashMap<>();
            String url = knowledgeServiceBaseUrl + "/generateSrcipt?detailId=" + detailId;

            ResponseEntity<String> response = knowledgeRestTemplate.exchange(
                url, 
                HttpMethod.POST, 
                null, 
                String.class
            );
            
            String res = response.getBody();
            if (null != res && res.equals("success")) {
                result.put("success", true);
                result.put("message", "生成话术成功");
            } else {
                result.put("success", false);
                result.put("message", "生成话术失败");
            }
            
            log.info("生成话术完成，知识明细ID: {}, 结果: {}", detailId, result);
            return result;
            
        } catch (Exception e) {
            log.error("生成话术失败，知识明细ID: {}", detailId, e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "生成话术失败");

            // 根据异常类型提供更具体的错误信息
            if (e.getMessage().contains("Read timed out")) {
                errorResult.put("message", "生成话术超时，请稍后重试或联系管理员");
            } else if (e.getMessage().contains("Connection refused")) {
                errorResult.put("message", "无法连接到知识服务，请检查服务是否启动");
            } else {
                errorResult.put("message", "生成话术失败: " + e.getMessage());
            }
            
            return errorResult;
        }
    }



    /**
     * 根据 baseId 生成话术
     * @param baseId  知识ID
     * @return 同步结果
     */
    public Map<String, Object> generateSrciptByBaseId(String baseId) {
        try {
            log.info("开始生成话术，知识ID: {}", baseId);
            Map <String, Object> result = new HashMap<>();
            String url = knowledgeServiceBaseUrl + "/generateSrciptByBaseId?baseId=" + baseId;

            ResponseEntity<String> response = knowledgeRestTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    null,
                    String.class
            );

            String res = response.getBody();
            if (null != res && res.equals("success")) {
                result.put("success", true);
                result.put("message", "生成话术成功");
            } else {
                result.put("success", false);
                result.put("message", "生成话术失败");
            }

            log.info("生成话术完成，知识ID: {}, 结果: {}", baseId, result);
            return result;

        } catch (Exception e) {
            log.error("生成话术失败，知识ID: {}", baseId, e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "生成话术失败");

            // 根据异常类型提供更具体的错误信息
            if (e.getMessage().contains("Read timed out")) {
                errorResult.put("message", "生成话术超时，请稍后重试或联系管理员");
            } else if (e.getMessage().contains("Connection refused")) {
                errorResult.put("message", "无法连接到知识服务，请检查服务是否启动");
            } else {
                errorResult.put("message", "生成话术失败: " + e.getMessage());
            }

            return errorResult;
        }
    }


    /**
     * 为所有文档生成话术
     * @return 同步结果
     */
    public Map<String, Object> generateSrciptForAllDetail() {
        try {
            log.info("开始为所有文档生成话术");
            Map<String, Object> result = new HashMap<>();
            
            String url = knowledgeServiceBaseUrl + "/generateSrciptForAllDetail";
            
            ResponseEntity<String> response = knowledgeRestTemplate.exchange(
                url, 
                HttpMethod.POST,
                null, 
                String.class
            );
            
            String res = response.getBody();
            if (res != null && res.equals("success")) {
                result.put("success", true);
                result.put("message", "为所有文档生成话术成功");
            } else {
                result.put("success", false);
                result.put("message", "为所有文档生成话术失败");
            }

            log.info("为所有文档生成话术完成，结果: {}", result);
            return result;
            
        } catch (Exception e) {
            log.error("为所有文档生成话术失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "为所有文档生成话术失败");

            // 根据异常类型提供更具体的错误信息
            if (e.getMessage().contains("Read timed out")) {
                errorResult.put("message", "为所有文档生成话术超时，请稍后重试或联系管理员");
            } else if (e.getMessage().contains("Connection refused")) {
                errorResult.put("message", "为所有文档生成话术失败: 无法连接到知识服务，请检查服务是否启动");
            } else {
                errorResult.put("message", "为所有文档生成话术失败: " + e.getMessage());
            }
            return errorResult;
        }
    }

    /**
     * 获取知识服务错误信息
     */
    private  static class KnowledgeServiceErrorHandler implements ResponseErrorHandler {
        public KnowledgeServiceErrorHandler(int maxRetries, int retryInterval) {
            KnowledgeServiceErrorHandler.maxRetries = maxRetries;
            KnowledgeServiceErrorHandler.retryInterval = retryInterval;
        }
        private static int maxRetries;
        private static int retryInterval;
        @Override
        public boolean hasError(ClientHttpResponse response) throws IOException {
            return response.getStatusCode().is4xxClientError() || response.getStatusCode().is5xxServerError();
        }

        @Override
        public void handleError(ClientHttpResponse response) throws IOException {
            if (KnowledgeServiceErrorHandler.maxRetries > 0) {
                KnowledgeServiceErrorHandler.maxRetries--;
                try {
                    Thread.sleep(KnowledgeServiceErrorHandler.retryInterval);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                handleError(response);
            } else {
                throw new IOException("Max retries reached. Status code: " + response.getStatusCode());
            }
        }
    }
}
