package org.jeecg.modules.word.api.interceptor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.oConvertUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;

/**
 * 小程序专用拦截器
 * @author: yf  add
 * @date: 2025年8月20日 上午10:14:28
 */
@Slf4j
@Component
public class WechatAuthInterceptor implements HandlerInterceptor {

    @Autowired
    private RedisUtil redisUtil;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        log.info("小程序API拦截器 - URI: {}", request.getRequestURI());
        
        // 获取请求头中的token
        String token = request.getHeader("Authorization");
        if (oConvertUtils.isEmpty(token)) {
            token = request.getHeader("X-Access-Token");
            if (oConvertUtils.isEmpty(token)) {
               token = request.getHeader("authStr");
            }
        }
        
        // 排除不需要token的接口
        String uri = request.getRequestURI();
        if (isExcludedPath(uri)) {
            log.info("小程序API - 排除路径: {}", uri);
            return true;
        }
        
        if (oConvertUtils.isEmpty(token)) {
            log.warn("小程序API - Token为空: {}", uri);
            return returnUnauthorized(response, "未提供访问令牌");
        }
        
        // 验证token
        if (!validateToken(token)) {
            log.warn("小程序API - Token无效: {}", token);
            return returnUnauthorized(response, "访问令牌无效或已过期");
        }

        // 从Redis获取用户信息
        String tokenKey = "access_token:" + token;
        String userInfo = (String) redisUtil.get(tokenKey);
        if (oConvertUtils.isNotEmpty(userInfo)) {
            JSONObject userInfoJson = JSONObject.parseObject(userInfo);
            request.setAttribute("userId", userInfoJson.getString("userId"));
            request.setAttribute("userName", userInfoJson.getString("userName"));
        }


        
        log.info("小程序API - Token验证通过");
        return true;
    }
    
    /**
     * 判断是否为排除路径
     */
    private boolean isExcludedPath(String uri) {
        String[] excludePaths = {
            "/word/api/wechat/login/authLogin",
            "/word/api/wechat/login/autoLogin",
            "/word/api/wechat/login/sendPhoneSms",
            "/word/api/wechat/login/deleteBindWeixin"
        };
        
        for (String path : excludePaths) {
            if (uri.contains(path)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 验证token
     */
    private boolean validateToken(String token) {
        try {
            // 清理token前缀
            if (token.startsWith("Bearer ")) {
                token = token.substring(7);
            }
            
            String tokenKey = "access_token:" + token;
            String tokenInfo = (String) redisUtil.get(tokenKey);
            
            return oConvertUtils.isNotEmpty(tokenInfo);
        } catch (Exception e) {
            log.error("验证token失败", e);
            return false;
        }
    }
    
    /**
     * 返回未授权响应
     */
    private boolean returnUnauthorized(HttpServletResponse response, String message) throws Exception {
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json; charset=utf-8");
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        
        Result<?> result = Result.error(401, message);
        PrintWriter out = response.getWriter();
        out.print(JSON.toJSON(result));
        return false;
    }
}