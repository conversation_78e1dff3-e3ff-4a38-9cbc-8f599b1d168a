#!/bin/bash

echo "正在启动单词指南针管理系统 (生产环境)..."

# 生产环境JVM参数
JAVA_OPTS="-XX:ReservedCodeCacheSize=512m \
-XX:InitialCodeCacheSize=128m \
-XX:MaxDirectMemorySize=2048m \
-XX:+UseG1GC \
-XX:MaxGCPauseMillis=200 \
-XX:+UseStringDeduplication \
-XX:+UseCompressedOops \
-XX:+PrintGCDetails \
-XX:+PrintGCTimeStamps \
-Xloggc:logs/gc.log \
-Xms512m \
-Xmx1024m \
-Dfile.encoding=UTF-8 \
-Djava.awt.headless=true"

echo "生产环境JVM参数: $JAVA_OPTS"

# 创建日志目录
mkdir -p logs

# 启动应用
nohup java $JAVA_OPTS -jar /opt/module/app/wordcompass/wordCompass-server.jar --spring.profiles.active=pro > logs/application.log 2>&1 &

echo "应用正在后台启动，日志文件: logs/application.log"
echo "进程ID: $!"

# 保存PID
echo $! > wordcompass-server.pid