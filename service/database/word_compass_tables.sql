-- 单词指南针系统数据库建表脚本
-- 创建时间: 2025-01-15
-- 版本: V1.0

-- ================================
-- 1. 教材基本信息表
-- ================================
CREATE TABLE `wc_textbook` (
  `id` varchar(32) NOT NULL COMMENT 'ID',
  `textbook_name` varchar(100) NOT NULL COMMENT '教材名称',
  `version` varchar(50) DEFAULT NULL COMMENT '版本',
  `volume` varchar(50) DEFAULT NULL COMMENT '册别',
  `grade` varchar(20) DEFAULT NULL COMMENT '年级',
  `publisher` varchar(100) DEFAULT NULL COMMENT '出版社',
  `sort_order` int DEFAULT '0' COMMENT '排序号',
  `status` tinyint DEFAULT '1' COMMENT '状态(0-禁用,1-启用)',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_textbook_name` (`textbook_name`),
  KEY `idx_grade_version` (`grade`, `version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='教材基本信息表';

-- ================================
-- 2. 教材明细信息表(单元、章节)
-- ================================
CREATE TABLE `wc_textbook_detail` (
  `id` varchar(32) NOT NULL COMMENT 'ID',
  `textbook_id` varchar(32) NOT NULL COMMENT '教材ID',
  `parent_id` varchar(32) DEFAULT NULL COMMENT '父级ID',
  `detail_name` varchar(100) NOT NULL COMMENT '名称',
  `content` text COMMENT '内容',
  `sort_order` int DEFAULT '0' COMMENT '排序号',
  `detail_type` varchar(20) NOT NULL COMMENT '类型(unit-单元,chapter-章节,lesson-课文)',
  `level` int DEFAULT '1' COMMENT '层级',
  `has_child` tinyint DEFAULT '0' COMMENT '是否有子节点(0-否,1-是)',
  `status` tinyint DEFAULT '1' COMMENT '状态(0-禁用,1-启用)',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_textbook_id` (`textbook_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_detail_type` (`detail_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='教材明细信息表';

-- ================================
-- 3. 单词信息表
-- ================================
CREATE TABLE `wc_word` (
  `id` varchar(32) NOT NULL COMMENT 'ID',
  `word_name` varchar(100) NOT NULL COMMENT '单词名称',
  `phonetic` varchar(200) DEFAULT NULL COMMENT '音标',
  `audio_file` varchar(500) DEFAULT NULL COMMENT '读音音频文件路径',
  `translation` varchar(500) NOT NULL COMMENT '中文译义',
  `textbook_id` varchar(32) DEFAULT NULL COMMENT '所属教材ID',
  `unit_id` varchar(32) DEFAULT NULL COMMENT '所属单元ID',
  `chapter_id` varchar(32) DEFAULT NULL COMMENT '所属章节ID',
  `word_category` varchar(20) DEFAULT NULL COMMENT '单词分类(noun-名词,verb-动词,adj-形容词,adv-副词,prep-介词,conj-连词,int-感叹词)',
  `difficulty_level` tinyint DEFAULT '1' COMMENT '难易程度(1-简单,2-中等,3-困难)',
  `example_sentence` text COMMENT '例句',
  `example_translation` text COMMENT '例句翻译',
  `sort_order` int DEFAULT '0' COMMENT '排序号',
  `status` tinyint DEFAULT '1' COMMENT '状态(0-禁用,1-启用)',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_word_name` (`word_name`),
  KEY `idx_textbook_id` (`textbook_id`),
  KEY `idx_unit_id` (`unit_id`),
  KEY `idx_chapter_id` (`chapter_id`),
  KEY `idx_difficulty_level` (`difficulty_level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='单词信息表';

-- ================================
-- 4. 错词信息表
-- ================================
CREATE TABLE `wc_wrong_word` (
  `id` varchar(32) NOT NULL COMMENT 'ID',
  `account_id` varchar(32) NOT NULL COMMENT '账号ID',
  `student_id` varchar(32) NOT NULL COMMENT '学生ID',
  `word_id` varchar(32) NOT NULL COMMENT '单词ID',
  `word_name` varchar(100) NOT NULL COMMENT '单词名称',
  `dictation_count` int DEFAULT '0' COMMENT '听写次数',
  `error_count` int DEFAULT '0' COMMENT '错误次数',
  `correct_count` int DEFAULT '0' COMMENT '正确次数',
  `error_rate` decimal(5,2) DEFAULT '0.00' COMMENT '错误率',
  `last_dictation_time` datetime DEFAULT NULL COMMENT '最后听写时间',
  `master_status` tinyint DEFAULT '0' COMMENT '掌握状态(0-未掌握,1-已掌握)',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_account_student_word` (`account_id`, `student_id`, `word_id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_student_id` (`student_id`),
  KEY `idx_word_id` (`word_id`),
  KEY `idx_master_status` (`master_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='错词信息表';

-- ================================
-- 5. 任务信息表
-- ================================
CREATE TABLE `wc_task` (
  `id` varchar(32) NOT NULL COMMENT 'ID',
  `account_id` varchar(32) NOT NULL COMMENT '账号ID',
  `student_id` varchar(32) NOT NULL COMMENT '学生ID',
  `task_name` varchar(100) NOT NULL COMMENT '任务名称',
  `task_description` varchar(500) DEFAULT NULL COMMENT '任务描述',
  `word_count` int DEFAULT '0' COMMENT '单词数量',
  `error_count` int DEFAULT '0' COMMENT '错误数量',
  `correct_count` int DEFAULT '0' COMMENT '正确数量',
  `error_rate` decimal(5,2) DEFAULT '0.00' COMMENT '错误率',
  `correct_rate` decimal(5,2) DEFAULT '0.00' COMMENT '正确率',
  `homework_photo` varchar(500) DEFAULT NULL COMMENT '作业照片附件',
  `finish_time` datetime DEFAULT NULL COMMENT '完成时间',
  `correction_time` datetime DEFAULT NULL COMMENT '批改时间',
  `task_type` varchar(20) NOT NULL COMMENT '任务类型(dictation-听写单词,translation-翻译汉语,recite_english-背诵英文,recite_chinese-背诵汉语)',
  `status` varchar(20) DEFAULT 'pending' COMMENT '状态(pending-待做,finished-已做,corrected-已批改)',
  `textbook_id` varchar(32) DEFAULT NULL COMMENT '所属教材ID',
  `unit_id` varchar(32) DEFAULT NULL COMMENT '所属单元ID',
  `chapter_id` varchar(32) DEFAULT NULL COMMENT '所属章节ID',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_student_id` (`student_id`),
  KEY `idx_task_type` (`task_type`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务信息表';

-- ================================
-- 6. 任务明细信息表
-- ================================
CREATE TABLE `wc_task_detail` (
  `id` varchar(32) NOT NULL COMMENT 'ID',
  `account_id` varchar(32) NOT NULL COMMENT '账号ID',
  `student_id` varchar(32) NOT NULL COMMENT '学生ID',
  `task_id` varchar(32) NOT NULL COMMENT '任务ID',
  `word_id` varchar(32) NOT NULL COMMENT '单词ID',
  `word_name` varchar(100) NOT NULL COMMENT '单词名称',
  `word_translation` varchar(500) DEFAULT NULL COMMENT '单词中文译义',
  `student_answer` varchar(500) DEFAULT NULL COMMENT '学生写的内容',
  `is_correct` tinyint DEFAULT '0' COMMENT '是否正确(0-错误,1-正确)',
  `correction_remark` varchar(500) DEFAULT NULL COMMENT '批改备注',
  `sort_order` int DEFAULT '0' COMMENT '排序号',
  `score` decimal(5,2) DEFAULT '0.00' COMMENT '得分',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_student_id` (`student_id`),
  KEY `idx_word_id` (`word_id`),
  KEY `idx_is_correct` (`is_correct`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务明细信息表';

-- ================================
-- 7. 学生信息表
-- ================================
CREATE TABLE `wc_student` (
  `id` varchar(32) NOT NULL COMMENT 'ID',
  `account_id` varchar(32) NOT NULL COMMENT '账号ID',
  `student_name` varchar(50) NOT NULL COMMENT '学生姓名',
  `grade` varchar(20) DEFAULT NULL COMMENT '年级',
  `school` varchar(100) DEFAULT NULL COMMENT '学校',
  `class_name` varchar(50) DEFAULT NULL COMMENT '班级',
  `gender` tinyint DEFAULT NULL COMMENT '性别(1-男,2-女)',
  `birth_date` date DEFAULT NULL COMMENT '出生日期',
  `avatar` varchar(500) DEFAULT NULL COMMENT '头像',
  `status` tinyint DEFAULT '1' COMMENT '状态(0-禁用,1-启用)',
  `sort_order` int DEFAULT '0' COMMENT '排序号',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_student_name` (`student_name`),
  KEY `idx_grade` (`grade`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='学生信息表';

-- ================================
-- 8. 账号信息表
-- ================================
CREATE TABLE `wc_account` (
  `id` varchar(32) NOT NULL COMMENT 'ID',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `mobile` varchar(20) NOT NULL COMMENT '手机号(唯一)',
  `password` varchar(100) NOT NULL COMMENT '密码',
  `salt` varchar(50) DEFAULT NULL COMMENT '盐值',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `gender` tinyint DEFAULT NULL COMMENT '性别(1-男,2-女)',
  `avatar` varchar(500) DEFAULT NULL COMMENT '头像附件',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
  `status` tinyint DEFAULT '1' COMMENT '状态(0-冻结,1-正常)',
  `account_type` tinyint DEFAULT '1' COMMENT '账号类型(1-普通用户,2-VIP用户)',
  `vip_expire_time` datetime DEFAULT NULL COMMENT 'VIP到期时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_mobile` (`mobile`),
  KEY `idx_nickname` (`nickname`),
  KEY `idx_status` (`status`),
  KEY `idx_account_type` (`account_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='账号信息表';

-- ================================
-- 9. 微信用户信息表
-- ================================
CREATE TABLE `wc_wechat_user` (
  `id` varchar(32) NOT NULL COMMENT 'ID',
  `wechat_nickname` varchar(100) DEFAULT NULL COMMENT '微信昵称',
  `openid` varchar(100) NOT NULL COMMENT '微信OpenID',
  `unionid` varchar(100) DEFAULT NULL COMMENT '微信UnionID',
  `account_id` varchar(32) DEFAULT NULL COMMENT '关联的账号ID',
  `wechat_avatar` varchar(500) DEFAULT NULL COMMENT '微信头像',
  `gender` tinyint DEFAULT '0' COMMENT '性别(0-未知,1-男,2-女)',
  `country` varchar(50) DEFAULT NULL COMMENT '国家',
  `province` varchar(50) DEFAULT NULL COMMENT '省份',
  `city` varchar(50) DEFAULT NULL COMMENT '城市',
  `language` varchar(20) DEFAULT NULL COMMENT '语言',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `status` tinyint DEFAULT '1' COMMENT '状态(0-禁用,1-启用)',
  `subscribe_status` tinyint DEFAULT '0' COMMENT '关注状态(0-未关注,1-已关注)',
  `subscribe_time` datetime DEFAULT NULL COMMENT '关注时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_openid` (`openid`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_unionid` (`unionid`),
  KEY `idx_subscribe_status` (`subscribe_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信用户信息表';

-- ================================
-- 插入初始数据
-- ================================

-- 插入教材示例数据
INSERT INTO `wc_textbook` (`id`, `textbook_name`, `version`, `volume`, `grade`, `publisher`, `sort_order`, `status`, `create_time`) VALUES
('1', '人教版英语', 'PEP', '上册', '三年级', '人民教育出版社', 1, 1, NOW()),
('2', '人教版英语', 'PEP', '下册', '三年级', '人民教育出版社', 2, 1, NOW()),
('3', '人教版英语', 'PEP', '上册', '四年级', '人民教育出版社', 3, 1, NOW()),
('4', '人教版英语', 'PEP', '下册', '四年级', '人民教育出版社', 4, 1, NOW());

-- 插入教材明细示例数据
INSERT INTO `wc_textbook_detail` (`id`, `textbook_id`, `parent_id`, `detail_name`, `sort_order`, `detail_type`, `level`, `has_child`, `status`, `create_time`) VALUES
('11', '1', NULL, 'Unit 1 Hello!', 1, 'unit', 1, 1, 1, NOW()),
('12', '1', '11', 'Part A', 1, 'chapter', 2, 0, 1, NOW()),
('13', '1', '11', 'Part B', 2, 'chapter', 2, 0, 1, NOW()),
('14', '1', NULL, 'Unit 2 Colours', 2, 'unit', 1, 1, 1, NOW()),
('15', '1', '14', 'Part A', 1, 'chapter', 2, 0, 1, NOW()),
('16', '1', '14', 'Part B', 2, 'chapter', 2, 0, 1, NOW());

-- 插入单词示例数据
INSERT INTO `wc_word` (`id`, `word_name`, `phonetic`, `translation`, `textbook_id`, `unit_id`, `chapter_id`, `word_category`, `difficulty_level`, `sort_order`, `status`, `create_time`) VALUES
('21', 'hello', '/həˈləʊ/', '你好', '1', '11', '12', 'int', 1, 1, 1, NOW()),
('22', 'hi', '/haɪ/', '嗨', '1', '11', '12', 'int', 1, 2, 1, NOW()),
('23', 'goodbye', '/ˌɡʊdˈbaɪ/', '再见', '1', '11', '12', 'int', 1, 3, 1, NOW()),
('24', 'red', '/red/', '红色', '1', '14', '15', 'adj', 1, 1, 1, NOW()),
('25', 'blue', '/bluː/', '蓝色', '1', '14', '15', 'adj', 1, 2, 1, NOW()),
('26', 'green', '/ɡriːn/', '绿色', '1', '14', '15', 'adj', 1, 3, 1, NOW()); 