//gradle 7.6.3

buildscript {
    ext {
        springBootVersion = '2.7.10'
    }
    repositories {
        maven { url "https://maven.aliyun.com/repository/public/" }
        mavenLocal()
//        mavenCentral()
    }
    dependencies {
        /* 添加插件依赖路径（通过jar方式） */
        classpath("org.springframework.boot:spring-boot-gradle-plugin:$springBootVersion")
    }
}


subprojects {
    apply plugin: 'java-library'
    apply plugin: 'idea'
    apply plugin: 'org.springframework.boot'
    apply plugin: 'io.spring.dependency-management'
    apply plugin: 'eclipse'

    repositories {
        mavenLocal()
        maven {
            url = uri('https://maven.aliyun.com/repository/public')
        }

        maven {
            url = uri('https://maven.jeecg.org/nexus/content/repositories/jeecg')
        }

        maven {
            url = uri('https://repo.maven.apache.org/maven2/')
        }
    }

    sourceCompatibility = '1.8'
    targetCompatibility = '1.8'
    tasks.withType(JavaCompile) {
        options.encoding = 'UTF-8'
    }


    configurations {
        compileOnly {
            extendsFrom annotationProcessor
        }
    }

    configurations.all {
        exclude group: 'org.apache.logging.log4j', module: 'log4j-to-slf4j'
        exclude group: 'org.apache.logging.log4j', module: 'log4j-slf4j-impl'
        exclude group: 'org.slf4j', module: 'slf4j-simple'
    }

    dependencies {
        annotationProcessor 'org.projectlombok:lombok:1.18.26'
        api 'com.alibaba:fastjson:1.2.83'
        api 'org.pegdown:pegdown:1.6.0'

        testAnnotationProcessor 'org.projectlombok:lombok:1.18.26'
        testImplementation 'org.springframework.boot:spring-boot-starter-test:2.7.10'
        testImplementation 'junit:junit:4.13.2'
    }

}



