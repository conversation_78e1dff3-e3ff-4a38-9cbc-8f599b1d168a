package ${bussiPackage}.${entityPackage}.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.util.oConvertUtils;
import ${bussiPackage}.${entityPackage}.entity.${entityName};
import ${bussiPackage}.${entityPackage}.service.${entityName}Service;
import java.util.Date;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.query.QueryGenerator;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


@Slf4j
@Api(tags="${tableVo.ftlDescription}")
@RestController
@RequestMapping("/${entityPackage}/${entityName?uncap_first}")
public class ${entityName}Controller {
	@Autowired
	private ${entityName}Service ${entityName?uncap_first}Service;

	@ApiOperation("${tableVo.ftlDescription}-分页列表查询")
	@PostMapping(value = "/list")
	public Result<IPage<${entityName}>> queryPageList(@RequestBody ${entityName} ${entityName?uncap_first},
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		  IPage<${entityName}> pageList = ${entityName?uncap_first}Service.find${entityName}ByPage(${entityName?uncap_first},
                         pageNo, pageSize, req.getParameterMap());
       return Result.OK(pageList);
	}



	@ApiOperation("${tableVo.ftlDescription}-分页列表查询")
	@PostMapping(value = "/listmap")
	public Result<IPage<${entityName}>> listmap(@RequestBody Map<String, Object> paramMap) {

        Object pageNo = paramMap.get("pageNo");
        Object pageSize = paramMap.get("pageSize");
        if (pageNo == null) pageNo = "1";
        if (pageSize == null) pageSize = "10";
        QueryWrapper<${entityName}> queryWrapper = new QueryWrapper<>();
        if (paramMap.get("code") != null) {
            queryWrapper.like("CODE", paramMap.get("code"));
        }
        queryWrapper.orderByDesc("CREATE_DATE");
     Page<${entityName}> page = new Page<${entityName}>(Long.getLong(pageNo.toString()), Long.getLong(pageSize.toString()));
	 IPage<${entityName}> pageList = ${entityName?uncap_first}Service.find${entityName}s(page,queryWrapper);
       return Result.OK(pageList);
	}



	@ApiOperation("${tableVo.ftlDescription}-添加")
	@PostMapping(value = "/add")
	public Result<?> add(@RequestBody ${entityName} ${entityName?uncap_first}) {
		${entityName?uncap_first}Service.saveOrUpdate${entityName}(${entityName?uncap_first});
		return Result.OK("添加成功！");
	}

	@ApiOperation("${tableVo.ftlDescription}-编辑")
	@PostMapping(value = "/edit")
	public Result<String> edit(@RequestBody ${entityName} ${entityName?uncap_first}) {
		${entityName?uncap_first}Service.saveOrUpdate${entityName}(${entityName?uncap_first});
		return Result.OK("编辑成功!");
	}

	@ApiOperation("${tableVo.ftlDescription}-通过id删除")
	@PostMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		${entityName?uncap_first}Service.delete${entityName}(id);
		return Result.OK("删除成功!");
	}

	@ApiOperation("${tableVo.ftlDescription}-批量删除")
	@PostMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.${entityName?uncap_first}Service.delete${entityName}ByIds(ids);
		return Result.OK("批量删除成功！");
	}


	@ApiOperation("${tableVo.ftlDescription}-通过id查询")
	@PostMapping(value = "/queryById")
	public Result<${entityName}> queryById(@RequestParam(name="id",required=true) String id) {
		${entityName} ${entityName?uncap_first} = ${entityName?uncap_first}Service.find${entityName}ById(id);
		return Result.OK(${entityName?uncap_first});
	}


}
