package org.jeecg.codegenerate;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;

import java.util.Date;
import java.util.HashMap;

public class CespGenSql {

    static String[] CODE = {"bas_building_area,bas,基础－楼宇区域信息",
            "bas_room,bas,基础－教室场所信息",
            "bas_student,bas,基础－学生",
            "bas_teacher,bas,基础－教师信息",
            "cou_course,cou,课程-课程",
            "cou_course_chapter,cou,课程-章节",
            "cou_course_favorite,cou,课程-收藏",
            "cou_course_learn,cou,课程-学员学习",
            "cou_course_learn_chapter,cou,课程-学员学习章节",
            "cou_tag,cou,课程-标签",
            "cou_teacher,cou,课程-讲师",
            "cou_type,cou,课程-类型",
            "ent_car_behavior,ent,进出-车辆行为",
            "ent_car_bio,ent,进出-车辆信息",
            "ent_person_bio,ent,进出-人员生物待征库",
            "ent_visitor_person,ent,进出-人员来访",
            "eva_paper,eva,测评-试卷",
            "eva_paper_question,eva,测评-试卷试题",
            "eva_question,eva,测评-试题",
            "eva_user_paper,eva,测评-用户试卷",
            "eva_user_paper_question,eva,测评-用户试卷试题",
            "sit_channel,sit,网站-栏目",
            "sit_content,sit,网站-内容",
            "sit_content_type,sit,网站-内容类型",
            "sys_user_weixin,sys,系统 - 用户微信验证",
            "tas_safe_agree_organ,tas,任务-责任书",
            "tas_safe_agree_organ_inst,tas,任务-责任书实例",
            "tas_safe_agree_user,tas,任务-承诺书",
            "tas_safe_agree_user_inst,tas,任务-承诺书实例",
            "tas_task_user,tas,任务-用户任务",
            "tas_task_user_inst,tas,任务-用户任务实例"
    };


    static String sql_insert = "\n" +
            "INSERT INTO sys_permission(id, parent_id, name, url, component, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_route, is_leaf, keep_alive, " +
            "hidden, hide_tab, description, status, del_flag, rule_flag, create_by, create_time, update_by, update_time, internal_or_external) \n" +
            "VALUES ('{id}0', NULL, '{tabelNameCn}', '/{entityPackage}/{UentityName}List', '{entityPackage}/{entityName}List', NULL, NULL, 0, NULL, '1', 0.00, 0, NULL, 1," +
            " 0, 0, 0, 0, NULL, '1', 0, 0, 'admin', '{currentDate}', NULL, NULL, 0);\n" +
            "\n" +
            "-- 权限控制sql\n" +
            "-- 新增\n" +
            "INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, " +
            "hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)\n" +
            "VALUES ('{id}1', '{id}', '添加{tabelNameCn}', NULL, NULL, 0, NULL, NULL, 2, '{entityPackage}:{tableName}:add', '1', NULL, 0, NULL, 1, 0, " +
            "0, 0, NULL, 'admin', '{currentDate}', NULL, NULL, 0, 0, '1', 0);\n" +
            "-- 编辑\n" +
            "INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, " +
            "hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)\n" +
            "VALUES ('{id}2', '{id}', '编辑{tabelNameCn}', NULL, NULL, 0, NULL, NULL, 2, '{entityPackage}:{tableName}:edit', '1', NULL, 0, NULL, 1, 0, " +
            "0, 0, NULL, 'admin', '{currentDate}', NULL, NULL, 0, 0, '1', 0);\n" +
            "-- 删除\n" +
            "INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, " +
            "hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)\n" +
            "VALUES ('{id}3', '{id}', '删除{tabelNameCn}', NULL, NULL, 0, NULL, NULL, 2, '{entityPackage}:{tableName}:delete', '1', NULL, 0, NULL, 1, " +
            "0, 0, 0, NULL, 'admin', '{currentDate}', NULL, NULL, 0, 0, '1', 0);\n" +
            "-- 批量删除\n" +
            "INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, " +
            "hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)\n" +
            "VALUES ('{id}4', '{id}', '批量删除{tabelNameCn}', NULL, NULL, 0, NULL, NULL, 2, '{entityPackage}:{tableName}:deleteBatch', '1', NULL, 0, " +
            "NULL, 1, 0, 0, 0, NULL, 'admin', '{currentDate}', NULL, NULL, 0, 0, '1', 0);\n" +
            "-- 导出excel\n" +
            "INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, " +
            "hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)\n" +
            "VALUES ('{id}5', '{id}', '导出excel_{tabelNameCn}', NULL, NULL, 0, NULL, NULL, 2, '{entityPackage}:{tableName}:exportXls', '1', NULL, 0, " +
            "NULL, 1, 0, 0, 0, NULL, 'admin', '{currentDate}', NULL, NULL, 0, 0, '1', 0);\n" +
            "-- 导入excel\n" +
            "INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, " +
            "hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)\n" +
            "VALUES ('{id}6', '{id}', '导入excel_{tabelNameCn}', NULL, NULL, 0, NULL, NULL, 2, '{entityPackage}:{tableName}:importExcel', '1', NULL, 0," +
            " NULL, 1, 0, 0, 0, NULL, 'admin', '{currentDate}', NULL, NULL, 0, 0, '1', 0);";

    /**
     * /**
     * 一对多(父子表)数据模型，生成方法
     *
     * @param args
     */
    public static void main(String[] args) {
        String id = DateUtil.format(new Date(), "yyyyMMddhhmmSSsss");

        HashMap map = new HashMap();


        //第一步：设置主表配置
        for (String code : CODE) {
            String[] arrayCode = code.split(",");
            String tableName = arrayCode[0];
            String tabelNameCn = arrayCode[2];
            String entityPackage = arrayCode[1];


            map.put("id", id);
            map.put("currentDate", DateUtil.now());
            map.put("tabelNameCn", tabelNameCn);
            map.put("entityPackage", entityPackage);
            map.put("tableName", tableName);
            map.put("UentityName", StrUtil.toCamelCase("_" + arrayCode[0]));
            map.put("entityName", StrUtil.toCamelCase("_" + arrayCode[0]));

            String sqlInsert = StrUtil.format(sql_insert, map);
            System.out.println(sqlInsert);
        }


    }


}
