2025-08-16 12:49:36.232 [background-preinit] INFO  org.hibernate.validator.internal.util.Version:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-08-16 12:49:36.299 [main] INFO  org.jeecg.WordCompassApplication:55 - Starting WordCompassApplication using Java 17.0.6 on DESKTOP-EU3ACCV with PID 31256 (E:\my-work-2025\work-wordcompass\wordCompass\service\wordCompass-server\build\classes\java\main started by Administrator in E:\my-work-2025\work-wordcompass\wordCompass)
2025-08-16 12:49:36.301 [main] INFO  org.jeecg.WordCompassApplication:637 - The following 1 profile is active: "dev"
2025-08-16 12:49:38.210 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-16 12:49:38.212 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:132 - Bootstrapping Spring Data Solr repositories in DEFAULT mode.
2025-08-16 12:49:38.408 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:201 - Finished Spring Data repository scanning in 182 ms. Found 1 Solr repository interfaces.
2025-08-16 12:49:38.930 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-16 12:49:38.931 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-16 12:49:39.125 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport:349 - Spring Data Redis - Could not safely identify store assignment for repository candidate interface org.jeecg.modules.km.kno.repository.KnoBaseDetailVoRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-08-16 12:49:39.125 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:201 - Finished Spring Data repository scanning in 184 ms. Found 0 Redis repository interfaces.
2025-08-16 12:49:39.574 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-16 12:49:39.578 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-16 12:49:39.601 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration' of type [org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-16 12:49:39.602 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusMetricsExportAutoConfiguration' of type [org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusMetricsExportAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-16 12:49:39.605 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'management.metrics.export.prometheus-org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusProperties' of type [org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-16 12:49:39.607 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'prometheusConfig' of type [org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusPropertiesConfigAdapter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-16 12:49:39.610 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'collectorRegistry' of type [io.prometheus.client.CollectorRegistry] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-16 12:49:39.611 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration' of type [org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-16 12:49:39.611 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'micrometerClock' of type [io.micrometer.core.instrument.Clock$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-16 12:49:39.634 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'prometheusMeterRegistry' of type [io.micrometer.prometheus.PrometheusMeterRegistry] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-16 12:49:39.638 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'micrometerOptions' of type [io.lettuce.core.metrics.MicrometerOptions] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-16 12:49:39.639 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'lettuceMetrics' of type [org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration$$Lambda$633/0x000000080120b5a0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-16 12:49:39.751 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-16 12:49:39.864 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-16 12:49:39.871 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'jeecgBaseConfig' of type [org.jeecg.config.JeecgBaseConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-16 12:49:39.874 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'shiroConfig' of type [org.jeecg.config.shiro.ShiroConfig$$EnhancerBySpringCGLIB$$3bdd0ae3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-16 12:49:40.165 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-16 12:49:40.174 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration$$EnhancerBySpringCGLIB$$5e127a3e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-16 12:49:40.189 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-16 12:49:40.240 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'shiroRealm' of type [org.jeecg.config.shiro.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-16 12:49:40.308 [main] INFO  org.jeecg.config.shiro.ShiroConfig:247 - ===============(1)创建缓存管理器RedisCacheManager
2025-08-16 12:49:40.310 [main] INFO  org.jeecg.config.shiro.ShiroConfig:265 - ===============(2)创建RedisManager,连接Redis..
2025-08-16 12:49:40.313 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-16 12:49:40.319 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-16 12:49:40.341 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-16 12:49:40.363 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$efb9a09a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-16 12:49:40.367 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-16 12:49:40.711 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:108 - Tomcat initialized with port(s): 8080 (http)
2025-08-16 12:49:40.721 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-16 12:49:40.722 [main] INFO  org.apache.catalina.core.StandardService:173 - Starting service [Tomcat]
2025-08-16 12:49:40.722 [main] INFO  org.apache.catalina.core.StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.73]
2025-08-16 12:49:40.991 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/wordCompassApi]:173 - Initializing Spring embedded WebApplicationContext
2025-08-16 12:49:40.991 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext:292 - Root WebApplicationContext: initialization completed in 4614 ms
2025-08-16 12:49:42.044 [main] INFO  com.alibaba.druid.pool.DruidDataSource:1010 - {dataSource-1,master} inited
2025-08-16 12:49:42.046 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:154 - dynamic-datasource - add a datasource named [master] success
2025-08-16 12:49:42.046 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:237 - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-16 12:49:43.512 [main] INFO  org.jeecg.common.modules.redis.config.RedisConfig:56 -  --- redis config init --- 
2025-08-16 12:49:46.279 [main] INFO  o.j.modules.km.kno.service.KnowledgeScriptService:66 - 知识服务RestTemplate初始化完成，连接超时: 30000ms，读取超时: 600000ms，最大重试次数: 3，重试间隔: 1000ms
2025-08-16 12:49:46.363 [main] INFO  o.jeecg.modules.km.kno.service.VectorStoreService:52 - 向量服务RestTemplate初始化完成，连接超时: 30000ms，读取超时: 300000ms
2025-08-16 12:49:46.939 [main] INFO  org.quartz.impl.StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-08-16 12:49:46.942 [main] INFO  org.quartz.simpl.SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-08-16 12:49:46.953 [main] INFO  org.quartz.core.SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-08-16 12:49:46.953 [main] INFO  org.quartz.core.QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-08-16 12:49:46.955 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-08-16 12:49:46.957 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-08-16 12:49:46.957 [main] INFO  org.quartz.core.QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId 'DESKTOP-EU3ACCV1755319786941'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-08-16 12:49:46.958 [main] INFO  org.quartz.impl.StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-08-16 12:49:46.958 [main] INFO  org.quartz.impl.StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-08-16 12:49:46.958 [main] INFO  org.quartz.core.QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@3652ce04
2025-08-16 12:49:48.966 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver:58 - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-16 12:49:49.091 [main] INFO  org.jeecg.config.init.CodeGenerateDbConfig:46 -  Init CodeGenerate Config [ Get Db Config From application.yml ] 
2025-08-16 12:49:50.553 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8080"]
2025-08-16 12:49:50.584 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:220 - Tomcat started on port(s): 8080 (http) with context path '/wordCompassApi'
2025-08-16 12:49:52.946 [main] INFO  o.s.scheduling.quartz.SchedulerFactoryBean:734 - Will start Quartz Scheduler [MyScheduler] in 1 seconds
2025-08-16 12:49:52.962 [wordCompass-scheduling-1] INFO  o.j.modules.km.kno.service.KnoBaseDetailService:490 - 开始同步KnoBaseDetail数据到Solr...
2025-08-16 12:49:52.967 [main] INFO  org.jeecg.WordCompassApplication:61 - Started WordCompassApplication in 17.559 seconds (JVM running for 20.138)
2025-08-16 12:49:52.972 [wordCompass-scheduling-2] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755319792972, dbSize=846}
2025-08-16 12:49:52.976 [wordCompass-scheduling-2] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1576864, create_time=1755319792976}
2025-08-16 12:49:52.979 [main] INFO  org.jeecg.config.init.CodeTemplateInitListener:29 -  Init Code Generate Template [ 检测如果是JAR启动环境，Copy模板到config目录 ] 
2025-08-16 12:49:52.982 [main] INFO  org.jeecg.WordCompassApplication:39 - 
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8080/wordCompassApi/
	External: 	http://*************:8080/wordCompassApi/
	Swagger文档: 	http://*************:8080/wordCompassApi/doc.html
----------------------------------------------------------
2025-08-16 12:49:53.134 [wordCompass-scheduling-1] DEBUG o.j.m.km.kno.mapper.KnoBaseDetailMapper.selectList:137 - ==>  Preparing: SELECT id, title, content, ocr_content, srcipt, srcipt_simplify, category_id, parent_id, knowledge_id, keywords, view_count, status, source_type, creator_name, updater_name, create_time, update_time, organ_name, organ_id, single_image_file_path, from_detail_page_num, from_detail_page_id, order_num FROM kno_base_detail WHERE (status <> ?)
2025-08-16 12:49:53.225 [wordCompass-scheduling-1] DEBUG o.j.m.km.kno.mapper.KnoBaseDetailMapper.selectList:137 - ==> Parameters: 0(Integer)
2025-08-16 12:49:53.278 [wordCompass-scheduling-1] DEBUG o.j.m.km.kno.mapper.KnoBaseDetailMapper.selectList:137 - <==      Total: 28
2025-08-16 12:49:53.291 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 12:49:53.293 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867036524546(String)
2025-08-16 12:49:53.294 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 12:49:53.301 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 12:49:53.301 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867103633409(String)
2025-08-16 12:49:53.302 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 12:49:53.308 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 12:49:53.310 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867103633410(String)
2025-08-16 12:49:53.311 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 12:49:53.314 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 12:49:53.315 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867103633411(String)
2025-08-16 12:49:53.315 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 12:49:53.321 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 12:49:53.322 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867170742274(String)
2025-08-16 12:49:53.322 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 12:49:53.327 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 12:49:53.327 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867170742275(String)
2025-08-16 12:49:53.328 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 12:49:53.332 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 12:49:53.334 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867170742276(String)
2025-08-16 12:49:53.335 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 12:49:53.339 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 12:49:53.340 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867237851138(String)
2025-08-16 12:49:53.341 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 12:49:53.346 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 12:49:53.346 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867246239745(String)
2025-08-16 12:49:53.348 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 12:49:53.353 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 12:49:53.353 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867246239746(String)
2025-08-16 12:49:53.354 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 12:49:53.358 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 12:49:53.360 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867246239747(String)
2025-08-16 12:49:53.361 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 12:49:53.365 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 12:49:53.365 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867309154305(String)
2025-08-16 12:49:53.367 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 12:49:53.370 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 12:49:53.371 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867309154306(String)
2025-08-16 12:49:53.372 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 12:49:53.376 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 12:49:53.377 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867309154307(String)
2025-08-16 12:49:53.377 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 12:49:53.383 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 12:49:53.384 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846081(String)
2025-08-16 12:49:53.386 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 12:49:53.396 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 12:49:53.396 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846082(String)
2025-08-16 12:49:53.397 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 12:49:53.401 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 12:49:53.401 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846083(String)
2025-08-16 12:49:53.403 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 12:49:53.410 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 12:49:53.411 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846084(String)
2025-08-16 12:49:53.412 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 12:49:53.417 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 12:49:53.418 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867460149249(String)
2025-08-16 12:49:53.420 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 12:49:53.424 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 12:49:53.425 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867460149250(String)
2025-08-16 12:49:53.426 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 12:49:53.433 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 12:49:53.435 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867460149251(String)
2025-08-16 12:49:53.436 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 12:49:53.444 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 12:49:53.444 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867535646721(String)
2025-08-16 12:49:53.446 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 12:49:53.453 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 12:49:53.453 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867535646722(String)
2025-08-16 12:49:53.454 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 12:49:53.461 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 12:49:53.461 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672451836186626(String)
2025-08-16 12:49:53.463 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 12:49:53.469 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 12:49:53.469 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672452326920193(String)
2025-08-16 12:49:53.471 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 12:49:53.476 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 12:49:53.476 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672452607938561(String)
2025-08-16 12:49:53.478 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 12:49:53.484 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 12:49:53.484 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672452817653761(String)
2025-08-16 12:49:53.485 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 12:49:53.492 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 12:49:53.493 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672453232889857(String)
2025-08-16 12:49:53.494 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 12:49:53.613 [wordCompass-scheduling-1] ERROR o.j.modules.km.kno.service.KnoBaseDetailService:507 - 同步KnoBaseDetail数据到Solr失败
org.springframework.dao.DataAccessResourceFailureException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
	at org.springframework.data.solr.core.SolrExceptionTranslator.translateExceptionIfPossible(SolrExceptionTranslator.java:79)
	at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:169)
	at org.springframework.data.solr.core.SolrTemplate.delete(SolrTemplate.java:249)
	at org.springframework.data.solr.core.SolrOperations.delete(SolrOperations.java:228)
	at org.springframework.data.solr.repository.support.SimpleSolrRepository.deleteAll(SimpleSolrRepository.java:212)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
	at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
	at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService.deleteAll(KnoBaseDetailSolrService.java:313)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$FastClassBySpringCGLIB$$1d6fbb9b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$EnhancerBySpringCGLIB$$daa8b9a9.deleteAll(<generated>)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailService.syncToSolr(KnoBaseDetailService.java:497)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
	at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
	at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:940)
	at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:903)
	at org.springframework.data.solr.core.SolrTemplate.lambda$delete$6(SolrTemplate.java:249)
	at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:167)
	... 65 common frames omitted
Caused by: java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 82 common frames omitted
2025-08-16 12:49:53.950 [Quartz Scheduler [MyScheduler]] INFO  o.s.scheduling.quartz.SchedulerFactoryBean:750 - Starting Quartz Scheduler now, after delay of 1 seconds
2025-08-16 12:49:54.058 [Quartz Scheduler [MyScheduler]] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:3644 - ClusterManager: detected 1 failed or restarted instances.
2025-08-16 12:49:54.059 [Quartz Scheduler [MyScheduler]] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:3503 - ClusterManager: Scanning for instance "DESKTOP-EU3ACCV1755251243489"'s failed in-progress jobs.
2025-08-16 12:49:54.066 [Quartz Scheduler [MyScheduler]] INFO  org.quartz.core.QuartzScheduler:547 - Scheduler MyScheduler_$_DESKTOP-EU3ACCV1755319786941 started.
2025-08-16 12:50:26.981 [http-nio-8080-exec-1] INFO  org.apache.tomcat.util.http.parser.Cookie:173 - A cookie header was received [Hm_lvt_0febd9e3cacb3f627ddac64d52caac39=1755243411,1755246569,1755248930,1755319815;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-08-16 12:50:26.994 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/wordCompassApi]:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-16 12:50:26.994 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet:525 - Initializing Servlet 'dispatcherServlet'
2025-08-16 12:50:26.999 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet:547 - Completed initialization in 5 ms
2025-08-16 12:50:27.176 [http-nio-8080-exec-1] INFO  o.jeecg.modules.system.controller.LoginController:542 - 获取验证码，Redis key = a3420689587752d348c76d0baf25c3fd，checkCode = tfb8
2025-08-16 12:50:27.565 [http-nio-8080-exec-1] DEBUG org.jeecg.common.aspect.DictAspect:65 - 获取JSON数据 耗时：424ms
2025-08-16 12:50:27.565 [http-nio-8080-exec-1] DEBUG org.jeecg.common.aspect.DictAspect:69 - 注入字典到JSON数据  耗时0ms
2025-08-16 12:50:52.966 [wordCompass-scheduling-2] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755319852966, dbSize=847}
2025-08-16 12:50:52.973 [wordCompass-scheduling-2] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1540160, create_time=1755319852972}
2025-08-16 12:51:17.368 [http-nio-8080-exec-2] WARN  c.b.m.core.toolkit.support.ReflectLambdaMeta:41 - Unable to make field private final java.lang.Class java.lang.invoke.SerializedLambda.capturingClass accessible: module java.base does not "opens java.lang.invoke" to unnamed module @65e2dbf3
2025-08-16 12:51:17.480 [http-nio-8080-exec-2] INFO  o.j.modules.system.service.impl.SysUserServiceImpl:901 -  登录接口用户的租户ID = 1000
2025-08-16 12:51:17.519 [http-nio-8080-exec-2] INFO  o.j.c.modules.redis.writer.JeecgRedisCacheWriter:113 - redis remove key:sys:cache:encrypt:user::admin
2025-08-16 12:51:17.545 [http-nio-8080-exec-2] DEBUG o.j.modules.base.mapper.BaseCommonMapper.saveLog:137 - ==>  Preparing: insert into sys_log (id, log_type, log_content, method, operate_type, request_url, request_type, request_param, ip, userid, username, cost_time, create_time,create_by, tenant_id) values( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
2025-08-16 12:51:17.546 [http-nio-8080-exec-2] DEBUG o.j.modules.base.mapper.BaseCommonMapper.saveLog:137 - ==> Parameters: 1956579494112583682(String), 1(Integer), 用户名: admin,登录成功！(String), null, null, null, null, null, 0:0:0:0:0:0:0:1(String), admin(String), 管理员(String), null, 2025-08-16 12:51:17.544(Timestamp), null, null
2025-08-16 12:51:17.553 [http-nio-8080-exec-2] DEBUG o.j.modules.base.mapper.BaseCommonMapper.saveLog:137 - <==    Updates: 1
2025-08-16 12:51:17.553 [http-nio-8080-exec-2] DEBUG org.jeecg.common.aspect.DictAspect:65 - 获取JSON数据 耗时：215ms
2025-08-16 12:51:17.553 [http-nio-8080-exec-2] DEBUG org.jeecg.common.aspect.DictAspect:69 - 注入字典到JSON数据  耗时0ms
2025-08-16 12:51:17.663 [http-nio-8080-exec-3] INFO  o.j.c.desensitization.aspect.SensitiveDataAspect:76 - 加密操作，Aspect程序耗时：8ms
2025-08-16 12:51:17.886 [http-nio-8080-exec-3] DEBUG o.j.modules.system.service.impl.SysDictServiceImpl:184 - -------登录加载系统字典-----{ol_form_biz_type=[DictModel(value=demo, text=官方示例, color=null, jsonObject=null), DictModel(value=bpm, text=流程表单, color=null, jsonObject=null), DictModel(value=temp, text=测试表单, color=null, jsonObject=null), DictModel(value=bdfl_include, text=导入表单, color=null, jsonObject=null)], position_rank=[DictModel(value=1, text=员级, color=null, jsonObject=null), DictModel(value=2, text=助级, color=null, jsonObject=null), DictModel(value=3, text=中级, color=null, jsonObject=null), DictModel(value=4, text=副高级, color=null, jsonObject=null), DictModel(value=5, text=正高级, color=null, jsonObject=null)], rule_conditions=[DictModel(value=>, text=大于, color=null, jsonObject=null), DictModel(value=<, text=小于, color=null, jsonObject=null), DictModel(value=!=, text=不等于, color=null, jsonObject=null), DictModel(value==, text=等于, color=null, jsonObject=null), DictModel(value=>=, text=大于等于, color=null, jsonObject=null), DictModel(value=<=, text=小于等于, color=null, jsonObject=null), DictModel(value=LEFT_LIKE, text=左模糊, color=null, jsonObject=null), DictModel(value=RIGHT_LIKE, text=右模糊, color=null, jsonObject=null), DictModel(value=LIKE, text=模糊, color=null, jsonObject=null), DictModel(value=IN, text=包含, color=null, jsonObject=null), DictModel(value=USE_SQL_RULES, text=自定义SQL表达式, color=null, jsonObject=null)], ceshi_online=[DictModel(value=00, text=000, color=null, jsonObject=null), DictModel(value=3, text=easyui, color=null, jsonObject=null), DictModel(value=1, text=booostrap, color=null, jsonObject=null)], online_graph_data_type=[DictModel(value=sql, text=SQL, color=null, jsonObject=null), DictModel(value=json, text=JSON, color=null, jsonObject=null), DictModel(value=api, text=API, color=null, jsonObject=null)], online_graph_display_template=[DictModel(value=tab, text=Tab风格, color=null, jsonObject=null), DictModel(value=single, text=单排布局, color=null, jsonObject=null), DictModel(value=double, text=双排布局, color=null, jsonObject=null), DictModel(value=combination, text=组合布局, color=null, jsonObject=null)], company_rank=[DictModel(value=1, text=总裁/总经理/CEO, color=null, jsonObject=null), DictModel(value=2, text=副总裁/副总经理/VP, color=null, jsonObject=null), DictModel(value=3, text=总监/主管/经理, color=null, jsonObject=null), DictModel(value=4, text=员工/专员/执行, color=null, jsonObject=null), DictModel(value=5, text=其他, color=null, jsonObject=null)], user_type=[DictModel(value=333, text=333, color=null, jsonObject=null)], messageType=[DictModel(value=system, text=系统消息, color=null, jsonObject=null), DictModel(value=email, text=邮件消息, color=null, jsonObject=null), DictModel(value=dingtalk, text=钉钉消息, color=null, jsonObject=null), DictModel(value=wechat_enterprise, text=企业微信, color=null, jsonObject=null)], remindMode=[DictModel(value=1, text=邮件提醒, color=null, jsonObject=null), DictModel(value=2, text=短信提醒, color=null, jsonObject=null), DictModel(value=4, text=系统消息, color=null, jsonObject=null)], yn=[DictModel(value=1, text=是, color=null, jsonObject=null), DictModel(value=0, text=否, color=null, jsonObject=null)], tenant_status=[DictModel(value=1, text=正常, color=null, jsonObject=null), DictModel(value=0, text=冻结, color=null, jsonObject=null)], taskStatus=[DictModel(value=1, text=创建, color=null, jsonObject=null), DictModel(value=2, text=发布, color=null, jsonObject=null), DictModel(value=3, text=撤销, color=null, jsonObject=null)], deviceType=[DictModel(value=1, text=测试, color=null, jsonObject=null), DictModel(value=2, text=评估, color=null, jsonObject=null)], rangeDate=[DictModel(value=jt, text=今天, color=null, jsonObject=null), DictModel(value=zt, text=昨天, color=null, jsonObject=null), DictModel(value=qt, text=前天, color=null, jsonObject=null), DictModel(value=bz, text=本周, color=null, jsonObject=null), DictModel(value=sz, text=上周, color=null, jsonObject=null), DictModel(value=by, text=本月, color=null, jsonObject=null), DictModel(value=sy, text=上月, color=null, jsonObject=null), DictModel(value=7day, text=7日, color=null, jsonObject=null), DictModel(value=zdy, text=自定义日期, color=null, jsonObject=null)], del_flag=[DictModel(value=1, text=已删除, color=null, jsonObject=null), DictModel(value=0, text=正常, color=null, jsonObject=null)], messageHref=[DictModel(value=bpm, text=/task/myHandleTaskInfo, color=null, jsonObject=null), DictModel(value=bpm_msg_node, text=, color=null, jsonObject=null), DictModel(value=bpm_cc, text=/task/myHandleTaskInfo, color=null, jsonObject=null), DictModel(value=bpm_task, text=/task/myHandleTaskInfo, color=null, jsonObject=null), DictModel(value=email, text=/eoa/email, color=null, jsonObject=null)], cgform_table_type=[DictModel(value=1, text=单表, color=null, jsonObject=null), DictModel(value=2, text=主表, color=null, jsonObject=null), DictModel(value=3, text=附表, color=null, jsonObject=null)], is_open=[DictModel(value=Y, text=是, color=null, jsonObject=null), DictModel(value=N, text=否, color=null, jsonObject=null)], msg_category=[DictModel(value=1, text=通知公告, color=null, jsonObject=null), DictModel(value=2, text=系统消息, color=null, jsonObject=null)], org_category=[DictModel(value=3, text=岗位, color=null, jsonObject=null), DictModel(value=1, text=公司, color=null, jsonObject=null), DictModel(value=2, text=部门, color=null, jsonObject=null), DictModel(value=4, text=分公司, color=null, jsonObject=null)], priority=[DictModel(value=H, text=高, color=null, jsonObject=null), DictModel(value=M, text=中, color=null, jsonObject=null), DictModel(value=L, text=低, color=null, jsonObject=null)], dict_item_status=[DictModel(value=1, text=启用, color=null, jsonObject=null), DictModel(value=0, text=不启用, color=null, jsonObject=null)], company_department=[DictModel(value=1, text=总经办, color=null, jsonObject=null), DictModel(value=2, text=技术/IT/研发, color=null, jsonObject=null), DictModel(value=3, text=产品/设计, color=null, jsonObject=null), DictModel(value=4, text=销售/市场/运营, color=null, jsonObject=null), DictModel(value=5, text=人事/财务/行政, color=null, jsonObject=null), DictModel(value=6, text=资源/仓储/采购, color=null, jsonObject=null), DictModel(value=7, text=其他, color=null, jsonObject=null)], activiti_sync=[DictModel(value=1, text=同步, color=null, jsonObject=null), DictModel(value=0, text=不同步, color=null, jsonObject=null)], msgSendStatus=[DictModel(value=0, text=未发送, color=null, jsonObject=null), DictModel(value=1, text=发送成功, color=null, jsonObject=null), DictModel(value=2, text=发送失败, color=null, jsonObject=null)], msg_type=[DictModel(value=USER, text=指定用户, color=null, jsonObject=null), DictModel(value=ALL, text=全体用户, color=null, jsonObject=null)], eoa_plan_type=[DictModel(value=1, text=日常记录, color=null, jsonObject=null), DictModel(value=2, text=本周工作, color=null, jsonObject=null), DictModel(value=3, text=下周计划, color=null, jsonObject=null)], company_size=[DictModel(value=1, text=20人以下, color=null, jsonObject=null), DictModel(value=2, text=21-99人, color=null, jsonObject=null), DictModel(value=3, text=100-499人, color=null, jsonObject=null), DictModel(value=4, text=500-999人, color=null, jsonObject=null), DictModel(value=5, text=1000-9999人, color=null, jsonObject=null), DictModel(value=6, text=10000人以上, color=null, jsonObject=null)], status=[DictModel(value=1, text=正常, color=null, jsonObject=null), DictModel(value=2, text=冻结, color=null, jsonObject=null)], msgType=[DictModel(value=1, text=文本, color=null, jsonObject=null), DictModel(value=2, text=富文本, color=null, jsonObject=null)], eoa_plan_status=[DictModel(value=0, text=未开始, color=null, jsonObject=null), DictModel(value=1, text=进行中, color=null, jsonObject=null), DictModel(value=2, text=已完成, color=null, jsonObject=null)], database_type=[DictModel(value=1, text=MySQL5.5, color=null, jsonObject=null), DictModel(value=4, text=MYSQL5.7+, color=null, jsonObject=null), DictModel(value=2, text=Oracle, color=null, jsonObject=null), DictModel(value=3, text=SQLServer, color=null, jsonObject=null), DictModel(value=6, text=postgresql, color=null, jsonObject=null), DictModel(value=5, text=marialDB, color=null, jsonObject=null), DictModel(value=7, text=达梦, color=null, jsonObject=null), DictModel(value=8, text=人大金仓, color=null, jsonObject=null), DictModel(value=9, text=神通, color=null, jsonObject=null), DictModel(value=10, text=SQLite, color=null, jsonObject=null), DictModel(value=11, text=DB2, color=null, jsonObject=null), DictModel(value=12, text=Hsqldb, color=null, jsonObject=null), DictModel(value=13, text=Derby, color=null, jsonObject=null), DictModel(value=14, text=H2, color=null, jsonObject=null), DictModel(value=15, text=其他数据库, color=null, jsonObject=null)], log_type=[DictModel(value=2, text=操作日志, color=null, jsonObject=null), DictModel(value=1, text=登录日志, color=null, jsonObject=null)], send_status=[DictModel(value=0, text=未发布, color=null, jsonObject=null), DictModel(value=1, text=已发布, color=null, jsonObject=null), DictModel(value=2, text=已撤销, color=null, jsonObject=null)], eoa_cms_menu_type=[DictModel(value=1, text=列表, color=null, jsonObject=null), DictModel(value=2, text=链接, color=null, jsonObject=null)], bpm_process_type=[DictModel(value=test, text=测试流程, color=null, jsonObject=null), DictModel(value=oa, text=OA办公, color=null, jsonObject=null), DictModel(value=business, text=业务办理, color=null, jsonObject=null)], form_perms_type=[DictModel(value=1, text=可见(未授权不可见), color=null, jsonObject=null), DictModel(value=2, text=可编辑(未授权禁用), color=null, jsonObject=null)], valid_status=[DictModel(value=0, text=无效, color=null, jsonObject=null), DictModel(value=1, text=有效, color=null, jsonObject=null)], urgent_level=[DictModel(value=1, text=一般, color=null, jsonObject=null), DictModel(value=2, text=重要, color=null, jsonObject=null), DictModel(value=3, text=紧急, color=null, jsonObject=null)], user_status=[DictModel(value=1, text=正常, color=null, jsonObject=null), DictModel(value=2, text=冻结, color=null, jsonObject=null)], operate_type=[DictModel(value=1, text=查询, color=null, jsonObject=null), DictModel(value=2, text=添加, color=null, jsonObject=null), DictModel(value=3, text=修改, color=null, jsonObject=null), DictModel(value=4, text=删除, color=null, jsonObject=null), DictModel(value=5, text=导入, color=null, jsonObject=null), DictModel(value=6, text=导出, color=null, jsonObject=null)], quartz_status=[DictModel(value=0, text=正常, color=null, jsonObject=null), DictModel(value=-1, text=停止, color=null, jsonObject=null)], menu_type=[DictModel(value=2, text=按钮权限, color=null, jsonObject=null), DictModel(value=1, text=子菜单, color=null, jsonObject=null), DictModel(value=0, text=一级菜单, color=null, jsonObject=null)], sex=[DictModel(value=1, text=男, color=null, jsonObject=null), DictModel(value=2, text=女, color=null, jsonObject=null)], perms_type=[DictModel(value=1, text=显示, color=null, jsonObject=null), DictModel(value=2, text=禁用, color=null, jsonObject=null)], global_perms_type=[DictModel(value=1, text=可见/可访问(授权后可见/可访问), color=null, jsonObject=null), DictModel(value=2, text=可编辑(未授权时禁用), color=null, jsonObject=null)], online_graph_type=[DictModel(value=bar, text=柱状图, color=null, jsonObject=null), DictModel(value=line, text=曲线图, color=null, jsonObject=null), DictModel(value=pie, text=饼图, color=null, jsonObject=null), DictModel(value=table, text=数据列表, color=null, jsonObject=null)], trade=[DictModel(value=1, text=信息传输、软件和信息技术服务业, color=null, jsonObject=null), DictModel(value=2, text=制造业, color=null, jsonObject=null), DictModel(value=3, text=租赁和商务服务业, color=null, jsonObject=null), DictModel(value=4, text=教育, color=null, jsonObject=null), DictModel(value=5, text=金融业, color=null, jsonObject=null), DictModel(value=6, text=建筑业, color=null, jsonObject=null), DictModel(value=7, text=科学研究和技术服务业, color=null, jsonObject=null), DictModel(value=8, text=批发和零售业, color=null, jsonObject=null), DictModel(value=9, text=住宿和餐饮业, color=null, jsonObject=null), DictModel(value=10, text=电子商务, color=null, jsonObject=null), DictModel(value=11, text=线下零售与服务业, color=null, jsonObject=null), DictModel(value=12, text=文化、体育和娱乐业, color=null, jsonObject=null), DictModel(value=13, text=房地产业, color=null, jsonObject=null), DictModel(value=14, text=交通运输、仓储和邮政业, color=null, jsonObject=null), DictModel(value=15, text=卫生和社会工作, color=null, jsonObject=null), DictModel(value=16, text=公共管理、社会保障和社会组织, color=null, jsonObject=null), DictModel(value=18, text=电力、热力、燃气及水生产和供应业, color=null, jsonObject=null), DictModel(value=19, text=水利、环境和公共设施管理业, color=null, jsonObject=null), DictModel(value=20, text=居民服务、修理和其他服务业, color=null, jsonObject=null), DictModel(value=21, text=政府机构, color=null, jsonObject=null), DictModel(value=22, text=农、林、牧、渔业, color=null, jsonObject=null), DictModel(value=23, text=采矿业, color=null, jsonObject=null), DictModel(value=24, text=国际组织, color=null, jsonObject=null), DictModel(value=25, text=其他, color=null, jsonObject=null)], bpm_status=[DictModel(value=1, text=待提交, color=null, jsonObject=null), DictModel(value=2, text=处理中, color=null, jsonObject=null), DictModel(value=3, text=已完成, color=null, jsonObject=null), DictModel(value=4, text=已作废, color=null, jsonObject=null)], depart_status=[DictModel(value=0, text=不启用, color=null, jsonObject=null), DictModel(value=1, text=启用, color=null, jsonObject=null)]}
2025-08-16 12:51:17.889 [http-nio-8080-exec-3] DEBUG org.jeecg.common.aspect.DictAspect:65 - 获取JSON数据 耗时：200ms
2025-08-16 12:51:17.889 [http-nio-8080-exec-3] DEBUG org.jeecg.common.aspect.DictAspect:69 - 注入字典到JSON数据  耗时0ms
2025-08-16 12:51:18.796 [http-nio-8080-exec-4] DEBUG org.jeecg.common.aspect.DictAspect:65 - 获取JSON数据 耗时：212ms
2025-08-16 12:51:18.796 [http-nio-8080-exec-4] DEBUG org.jeecg.common.aspect.DictAspect:69 - 注入字典到JSON数据  耗时0ms
2025-08-16 12:51:23.388 [http-nio-8080-exec-10] DEBUG org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-08-16 12:51:23.453 [http-nio-8080-exec-6] DEBUG org.jeecg.common.aspect.DictAspect:65 - 获取JSON数据 耗时：77ms
2025-08-16 12:51:23.453 [http-nio-8080-exec-6] DEBUG org.jeecg.common.aspect.DictAspect:69 - 注入字典到JSON数据  耗时0ms
2025-08-16 12:51:52.738 [http-nio-8080-exec-7] INFO  o.j.m.monitor.controller.ActuatorRedisController:117 - 查询磁盘信息:6个
2025-08-16 12:51:52.769 [http-nio-8080-exec-7] INFO  o.j.m.monitor.controller.ActuatorRedisController:130 - {name=OS (C:), rest=186166153216, restPPT=56, max=429700067328}
2025-08-16 12:51:52.776 [http-nio-8080-exec-7] INFO  o.j.m.monitor.controller.ActuatorRedisController:130 - {name=本地磁盘 (D:), rest=138221797376, restPPT=48, max=268607250432}
2025-08-16 12:51:52.781 [http-nio-8080-exec-7] INFO  o.j.m.monitor.controller.ActuatorRedisController:130 - {name=本地磁盘 (E:), rest=57584033792, restPPT=61, max=151161372672}
2025-08-16 12:51:52.786 [http-nio-8080-exec-7] INFO  o.j.m.monitor.controller.ActuatorRedisController:130 - {name=本地磁盘 (F:), rest=69867331584, restPPT=53, max=150828929024}
2025-08-16 12:51:52.793 [http-nio-8080-exec-7] INFO  o.j.m.monitor.controller.ActuatorRedisController:130 - {name=本地磁盘 (G:), rest=137231179776, restPPT=72, max=500170412032}
2025-08-16 12:51:52.799 [http-nio-8080-exec-7] INFO  o.j.m.monitor.controller.ActuatorRedisController:130 - {name=本地磁盘 (H:), rest=260168253440, restPPT=47, max=500030472192}
2025-08-16 12:51:52.801 [http-nio-8080-exec-7] DEBUG org.jeecg.common.aspect.DictAspect:65 - 获取JSON数据 耗时：309ms
2025-08-16 12:51:52.801 [http-nio-8080-exec-7] DEBUG org.jeecg.common.aspect.DictAspect:69 - 注入字典到JSON数据  耗时0ms
2025-08-16 12:51:52.977 [wordCompass-scheduling-1] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755319912977, dbSize=848}
2025-08-16 12:51:52.982 [wordCompass-scheduling-1] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755319912982}
2025-08-16 12:52:18.740 [http-nio-8080-exec-8] DEBUG org.jeecg.modules.message.websocket.WebSocket:111 - 【系统 WebSocket】收到客户端消息:ping
2025-08-16 12:52:20.748 [http-nio-8080-exec-9] DEBUG org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-08-16 12:52:26.758 [http-nio-8080-exec-5] DEBUG org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-08-16 12:52:52.972 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755319972972, dbSize=848}
2025-08-16 12:52:52.975 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755319972975}
2025-08-16 12:53:22.747 [http-nio-8080-exec-1] DEBUG org.jeecg.modules.message.websocket.WebSocket:111 - 【系统 WebSocket】收到客户端消息:ping
2025-08-16 12:53:24.744 [http-nio-8080-exec-2] DEBUG org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-08-16 12:53:29.765 [http-nio-8080-exec-3] DEBUG org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-08-16 12:53:31.587 [http-nio-8080-exec-4] INFO  o.j.m.system.controller.SysPermissionController:111 - ======获取全部菜单数据=====耗时:39毫秒
2025-08-16 12:53:31.589 [http-nio-8080-exec-4] DEBUG org.jeecg.common.aspect.DictAspect:65 - 获取JSON数据 耗时：41ms
2025-08-16 12:53:31.589 [http-nio-8080-exec-4] DEBUG org.jeecg.common.aspect.DictAspect:69 - 注入字典到JSON数据  耗时0ms
2025-08-16 12:53:46.748 [http-nio-8080-exec-10] DEBUG org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-08-16 12:53:47.653 [http-nio-8080-exec-6] DEBUG org.jeecg.common.aspect.DictAspect:65 - 获取JSON数据 耗时：106ms
2025-08-16 12:53:47.653 [http-nio-8080-exec-6] DEBUG org.jeecg.common.aspect.DictAspect:69 - 注入字典到JSON数据  耗时0ms
2025-08-16 12:53:48.105 [http-nio-8080-exec-7] DEBUG org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-08-16 12:53:48.173 [http-nio-8080-exec-8] DEBUG org.jeecg.common.aspect.DictAspect:65 - 获取JSON数据 耗时：68ms
2025-08-16 12:53:48.173 [http-nio-8080-exec-8] DEBUG org.jeecg.common.aspect.DictAspect:69 - 注入字典到JSON数据  耗时0ms
2025-08-16 12:53:52.970 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755320032970, dbSize=848}
2025-08-16 12:53:52.970 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755320032970}
2025-08-16 12:54:43.118 [http-nio-8080-exec-9] DEBUG org.jeecg.modules.message.websocket.WebSocket:111 - 【系统 WebSocket】收到客户端消息:ping
2025-08-16 12:54:44.121 [http-nio-8080-exec-5] DEBUG org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-08-16 12:54:49.149 [http-nio-8080-exec-1] DEBUG org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-08-16 12:54:52.964 [wordCompass-scheduling-1] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755320092964, dbSize=848}
2025-08-16 12:54:52.966 [wordCompass-scheduling-1] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755320092966}
2025-08-16 12:55:44.165 [http-nio-8080-exec-2] DEBUG org.jeecg.modules.message.websocket.WebSocket:111 - 【系统 WebSocket】收到客户端消息:ping
2025-08-16 12:55:45.174 [http-nio-8080-exec-3] DEBUG org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-08-16 12:55:50.194 [http-nio-8080-exec-4] DEBUG org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-08-16 12:55:52.967 [wordCompass-scheduling-2] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755320152967, dbSize=848}
2025-08-16 12:55:52.968 [wordCompass-scheduling-2] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755320152968}
2025-08-16 12:56:45.206 [http-nio-8080-exec-10] DEBUG org.jeecg.modules.message.websocket.WebSocket:111 - 【系统 WebSocket】收到客户端消息:ping
2025-08-16 12:56:46.217 [http-nio-8080-exec-6] DEBUG org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-08-16 12:56:51.235 [http-nio-8080-exec-7] DEBUG org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-08-16 12:56:52.977 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755320212977, dbSize=848}
2025-08-16 12:56:52.977 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755320212977}
2025-08-16 12:57:46.241 [http-nio-8080-exec-8] DEBUG org.jeecg.modules.message.websocket.WebSocket:111 - 【系统 WebSocket】收到客户端消息:ping
2025-08-16 12:57:47.244 [http-nio-8080-exec-9] DEBUG org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-08-16 12:57:52.759 [http-nio-8080-exec-5] DEBUG org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-08-16 12:57:52.965 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755320272965, dbSize=848}
2025-08-16 12:57:52.968 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755320272968}
2025-08-16 12:57:58.743 [http-nio-8080-exec-1] DEBUG org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-08-16 12:58:00.077 [http-nio-8080-exec-2] DEBUG org.jeecg.common.aspect.DictAspect:65 - 获取JSON数据 耗时：151ms
2025-08-16 12:58:00.078 [http-nio-8080-exec-2] DEBUG org.jeecg.common.aspect.DictAspect:69 - 注入字典到JSON数据  耗时0ms
2025-08-16 12:58:00.535 [http-nio-8080-exec-3] DEBUG org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-08-16 12:58:00.591 [http-nio-8080-exec-4] DEBUG org.jeecg.common.aspect.DictAspect:65 - 获取JSON数据 耗时：56ms
2025-08-16 12:58:00.591 [http-nio-8080-exec-4] DEBUG org.jeecg.common.aspect.DictAspect:69 - 注入字典到JSON数据  耗时0ms
2025-08-16 12:58:05.234 [http-nio-8080-exec-10] DEBUG org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-08-16 12:58:05.870 [http-nio-8080-exec-6] DEBUG org.jeecg.common.aspect.DictAspect:65 - 获取JSON数据 耗时：93ms
2025-08-16 12:58:05.870 [http-nio-8080-exec-6] DEBUG org.jeecg.common.aspect.DictAspect:69 - 注入字典到JSON数据  耗时0ms
2025-08-16 12:58:06.224 [http-nio-8080-exec-7] DEBUG org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-08-16 12:58:06.290 [http-nio-8080-exec-8] DEBUG org.jeecg.common.aspect.DictAspect:65 - 获取JSON数据 耗时：64ms
2025-08-16 12:58:06.291 [http-nio-8080-exec-8] DEBUG org.jeecg.common.aspect.DictAspect:69 - 注入字典到JSON数据  耗时0ms
2025-08-16 12:58:11.064 [http-nio-8080-exec-9] INFO  o.j.m.monitor.controller.ActuatorRedisController:117 - 查询磁盘信息:6个
2025-08-16 12:58:11.070 [http-nio-8080-exec-9] INFO  o.j.m.monitor.controller.ActuatorRedisController:130 - {name=OS (C:), rest=185906327552, restPPT=56, max=429700067328}
2025-08-16 12:58:11.076 [http-nio-8080-exec-9] INFO  o.j.m.monitor.controller.ActuatorRedisController:130 - {name=本地磁盘 (D:), rest=138221797376, restPPT=48, max=268607250432}
2025-08-16 12:58:11.079 [http-nio-8080-exec-9] INFO  o.j.m.monitor.controller.ActuatorRedisController:130 - {name=本地磁盘 (E:), rest=57583968256, restPPT=61, max=151161372672}
2025-08-16 12:58:11.085 [http-nio-8080-exec-9] INFO  o.j.m.monitor.controller.ActuatorRedisController:130 - {name=本地磁盘 (F:), rest=69867331584, restPPT=53, max=150828929024}
2025-08-16 12:58:11.090 [http-nio-8080-exec-9] INFO  o.j.m.monitor.controller.ActuatorRedisController:130 - {name=本地磁盘 (G:), rest=137231179776, restPPT=72, max=500170412032}
2025-08-16 12:58:11.092 [http-nio-8080-exec-9] INFO  o.j.m.monitor.controller.ActuatorRedisController:130 - {name=本地磁盘 (H:), rest=260168253440, restPPT=47, max=500030472192}
2025-08-16 12:58:11.092 [http-nio-8080-exec-9] DEBUG org.jeecg.common.aspect.DictAspect:65 - 获取JSON数据 耗时：29ms
2025-08-16 12:58:11.092 [http-nio-8080-exec-9] DEBUG org.jeecg.common.aspect.DictAspect:69 - 注入字典到JSON数据  耗时0ms
2025-08-16 12:58:52.975 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755320332975, dbSize=848}
2025-08-16 12:58:52.977 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755320332977}
2025-08-16 12:59:01.742 [http-nio-8080-exec-5] DEBUG org.jeecg.modules.message.websocket.WebSocket:111 - 【系统 WebSocket】收到客户端消息:ping
2025-08-16 12:59:03.738 [http-nio-8080-exec-1] DEBUG org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-08-16 12:59:09.755 [http-nio-8080-exec-2] DEBUG org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-08-16 12:59:31.294 [http-nio-8080-exec-3] DEBUG o.j.m.k.k.m.KnoBusinessTypeMapper.selectByParentId:137 - ==>  Preparing: SELECT * FROM kno_business_type WHERE parent_id = ? ORDER BY sort ASC, create_time ASC
2025-08-16 12:59:31.295 [http-nio-8080-exec-3] DEBUG o.j.m.k.k.m.KnoBusinessTypeMapper.selectByParentId:137 - ==> Parameters: 0(String)
2025-08-16 12:59:31.297 [http-nio-8080-exec-3] DEBUG o.j.m.k.k.m.KnoBusinessTypeMapper.selectByParentId:137 - <==      Total: 5
2025-08-16 12:59:31.301 [http-nio-8080-exec-3] DEBUG o.j.m.k.k.m.KnoBusinessTypeMapper.selectByParentId:137 - ==>  Preparing: SELECT * FROM kno_business_type WHERE parent_id = ? ORDER BY sort ASC, create_time ASC
2025-08-16 12:59:31.301 [http-nio-8080-exec-3] DEBUG o.j.m.k.k.m.KnoBusinessTypeMapper.selectByParentId:137 - ==> Parameters: 1949423093518999554(String)
2025-08-16 12:59:31.303 [http-nio-8080-exec-3] DEBUG o.j.m.k.k.m.KnoBusinessTypeMapper.selectByParentId:137 - <==      Total: 0
2025-08-16 12:59:31.306 [http-nio-8080-exec-3] DEBUG o.j.m.k.k.m.KnoBusinessTypeMapper.selectByParentId:137 - ==>  Preparing: SELECT * FROM kno_business_type WHERE parent_id = ? ORDER BY sort ASC, create_time ASC
2025-08-16 12:59:31.307 [http-nio-8080-exec-3] DEBUG o.j.m.k.k.m.KnoBusinessTypeMapper.selectByParentId:137 - ==> Parameters: 1948584181056360449(String)
2025-08-16 12:59:31.308 [http-nio-8080-exec-3] DEBUG o.j.m.k.k.m.KnoBusinessTypeMapper.selectByParentId:137 - <==      Total: 0
2025-08-16 12:59:31.311 [http-nio-8080-exec-3] DEBUG o.j.m.k.k.m.KnoBusinessTypeMapper.selectByParentId:137 - ==>  Preparing: SELECT * FROM kno_business_type WHERE parent_id = ? ORDER BY sort ASC, create_time ASC
2025-08-16 12:59:31.311 [http-nio-8080-exec-3] DEBUG o.j.m.k.k.m.KnoBusinessTypeMapper.selectByParentId:137 - ==> Parameters: 1949423158035783681(String)
2025-08-16 12:59:31.312 [http-nio-8080-exec-3] DEBUG o.j.m.k.k.m.KnoBusinessTypeMapper.selectByParentId:137 - <==      Total: 0
2025-08-16 12:59:31.315 [http-nio-8080-exec-3] DEBUG o.j.m.k.k.m.KnoBusinessTypeMapper.selectByParentId:137 - ==>  Preparing: SELECT * FROM kno_business_type WHERE parent_id = ? ORDER BY sort ASC, create_time ASC
2025-08-16 12:59:31.316 [http-nio-8080-exec-3] DEBUG o.j.m.k.k.m.KnoBusinessTypeMapper.selectByParentId:137 - ==> Parameters: 1949431759345311745(String)
2025-08-16 12:59:31.317 [http-nio-8080-exec-3] DEBUG o.j.m.k.k.m.KnoBusinessTypeMapper.selectByParentId:137 - <==      Total: 0
2025-08-16 12:59:31.320 [http-nio-8080-exec-3] DEBUG o.j.m.k.k.m.KnoBusinessTypeMapper.selectByParentId:137 - ==>  Preparing: SELECT * FROM kno_business_type WHERE parent_id = ? ORDER BY sort ASC, create_time ASC
2025-08-16 12:59:31.320 [http-nio-8080-exec-3] DEBUG o.j.m.k.k.m.KnoBusinessTypeMapper.selectByParentId:137 - ==> Parameters: 1949653561743642626(String)
2025-08-16 12:59:31.321 [http-nio-8080-exec-3] DEBUG o.j.m.k.k.m.KnoBusinessTypeMapper.selectByParentId:137 - <==      Total: 0
2025-08-16 12:59:31.321 [http-nio-8080-exec-3] DEBUG org.jeecg.common.aspect.DictAspect:65 - 获取JSON数据 耗时：44ms
2025-08-16 12:59:31.321 [http-nio-8080-exec-3] DEBUG org.jeecg.common.aspect.DictAspect:69 - 注入字典到JSON数据  耗时0ms
2025-08-16 12:59:31.406 [http-nio-8080-exec-4] DEBUG org.jeecg.modules.km.kno.service.KnoDeptService:85 - 找到根机构：总公司, orgCategory: 1
2025-08-16 12:59:31.411 [http-nio-8080-exec-4] DEBUG o.j.m.k.k.m.K.searchKnoBasePage_mpCount:137 - ==>  Preparing: SELECT COUNT(*) AS total FROM kno_base WHERE 1 = 1
2025-08-16 12:59:31.413 [http-nio-8080-exec-4] DEBUG o.j.m.k.k.m.K.searchKnoBasePage_mpCount:137 - ==> Parameters: 
2025-08-16 12:59:31.417 [http-nio-8080-exec-4] DEBUG o.j.m.k.k.m.K.searchKnoBasePage_mpCount:137 - <==      Total: 1
2025-08-16 12:59:31.419 [http-nio-8080-exec-4] DEBUG o.j.m.k.kno.mapper.KnoBaseMapper.searchKnoBasePage:137 - ==>  Preparing: SELECT * FROM kno_base WHERE 1 = 1 ORDER BY create_time DESC LIMIT ?
2025-08-16 12:59:31.419 [http-nio-8080-exec-4] DEBUG o.j.m.k.kno.mapper.KnoBaseMapper.searchKnoBasePage:137 - ==> Parameters: 10(Long)
2025-08-16 12:59:31.420 [http-nio-8080-exec-4] DEBUG o.j.m.k.kno.mapper.KnoBaseMapper.searchKnoBasePage:137 - <==      Total: 7
2025-08-16 12:59:31.422 [http-nio-8080-exec-4] DEBUG o.j.m.k.k.m.K.selectByKnowledgeId:137 - ==>  Preparing: SELECT * FROM kno_base_detail WHERE knowledge_id = ?
2025-08-16 12:59:31.424 [http-nio-8080-exec-4] DEBUG o.j.m.k.k.m.K.selectByKnowledgeId:137 - ==> Parameters: 1950022388302213122(String)
2025-08-16 12:59:31.426 [http-nio-8080-exec-4] DEBUG o.j.m.k.k.m.K.selectByKnowledgeId:137 - <==      Total: 2
2025-08-16 12:59:31.437 [http-nio-8080-exec-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_id = ?)
2025-08-16 12:59:31.438 [http-nio-8080-exec-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1950022388302213122(String)
2025-08-16 12:59:31.439 [http-nio-8080-exec-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 1
2025-08-16 12:59:31.443 [http-nio-8080-exec-4] DEBUG o.j.m.km.kno.mapper.KnoTagMapper.selectBatchIds:137 - ==>  Preparing: SELECT id, name, create_time FROM kno_tag WHERE id IN (?)
2025-08-16 12:59:31.444 [http-nio-8080-exec-4] DEBUG o.j.m.km.kno.mapper.KnoTagMapper.selectBatchIds:137 - ==> Parameters: 3(String)
2025-08-16 12:59:31.446 [http-nio-8080-exec-4] DEBUG o.j.m.km.kno.mapper.KnoTagMapper.selectBatchIds:137 - <==      Total: 1
2025-08-16 12:59:31.447 [http-nio-8080-exec-4] DEBUG o.j.m.k.k.m.K.selectByKnowledgeId:137 - ==>  Preparing: SELECT * FROM kno_base_detail WHERE knowledge_id = ?
2025-08-16 12:59:31.447 [http-nio-8080-exec-4] DEBUG o.j.m.k.k.m.K.selectByKnowledgeId:137 - ==> Parameters: 1949789424754593794(String)
2025-08-16 12:59:31.448 [http-nio-8080-exec-4] DEBUG o.j.m.k.k.m.K.selectByKnowledgeId:137 - <==      Total: 1
2025-08-16 12:59:31.453 [http-nio-8080-exec-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_id = ?)
2025-08-16 12:59:31.453 [http-nio-8080-exec-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949789424754593794(String)
2025-08-16 12:59:31.453 [http-nio-8080-exec-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 12:59:31.456 [http-nio-8080-exec-4] DEBUG o.j.m.k.k.m.K.selectByKnowledgeId:137 - ==>  Preparing: SELECT * FROM kno_base_detail WHERE knowledge_id = ?
2025-08-16 12:59:31.456 [http-nio-8080-exec-4] DEBUG o.j.m.k.k.m.K.selectByKnowledgeId:137 - ==> Parameters: 1949789248434442241(String)
2025-08-16 12:59:31.457 [http-nio-8080-exec-4] DEBUG o.j.m.k.k.m.K.selectByKnowledgeId:137 - <==      Total: 2
2025-08-16 12:59:31.462 [http-nio-8080-exec-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_id = ?)
2025-08-16 12:59:31.462 [http-nio-8080-exec-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949789248434442241(String)
2025-08-16 12:59:31.463 [http-nio-8080-exec-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 1
2025-08-16 12:59:31.466 [http-nio-8080-exec-4] DEBUG o.j.m.km.kno.mapper.KnoTagMapper.selectBatchIds:137 - ==>  Preparing: SELECT id, name, create_time FROM kno_tag WHERE id IN (?)
2025-08-16 12:59:31.467 [http-nio-8080-exec-4] DEBUG o.j.m.km.kno.mapper.KnoTagMapper.selectBatchIds:137 - ==> Parameters: 3(String)
2025-08-16 12:59:31.467 [http-nio-8080-exec-4] DEBUG o.j.m.km.kno.mapper.KnoTagMapper.selectBatchIds:137 - <==      Total: 1
2025-08-16 12:59:31.468 [http-nio-8080-exec-4] DEBUG o.j.m.k.k.m.K.selectByKnowledgeId:137 - ==>  Preparing: SELECT * FROM kno_base_detail WHERE knowledge_id = ?
2025-08-16 12:59:31.468 [http-nio-8080-exec-4] DEBUG o.j.m.k.k.m.K.selectByKnowledgeId:137 - ==> Parameters: 1949789097791819777(String)
2025-08-16 12:59:31.469 [http-nio-8080-exec-4] DEBUG o.j.m.k.k.m.K.selectByKnowledgeId:137 - <==      Total: 2
2025-08-16 12:59:31.475 [http-nio-8080-exec-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_id = ?)
2025-08-16 12:59:31.475 [http-nio-8080-exec-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949789097791819777(String)
2025-08-16 12:59:31.476 [http-nio-8080-exec-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 1
2025-08-16 12:59:31.480 [http-nio-8080-exec-4] DEBUG o.j.m.km.kno.mapper.KnoTagMapper.selectBatchIds:137 - ==>  Preparing: SELECT id, name, create_time FROM kno_tag WHERE id IN (?)
2025-08-16 12:59:31.480 [http-nio-8080-exec-4] DEBUG o.j.m.km.kno.mapper.KnoTagMapper.selectBatchIds:137 - ==> Parameters: 3(String)
2025-08-16 12:59:31.481 [http-nio-8080-exec-4] DEBUG o.j.m.km.kno.mapper.KnoTagMapper.selectBatchIds:137 - <==      Total: 1
2025-08-16 12:59:31.482 [http-nio-8080-exec-4] DEBUG o.j.m.k.k.m.K.selectByKnowledgeId:137 - ==>  Preparing: SELECT * FROM kno_base_detail WHERE knowledge_id = ?
2025-08-16 12:59:31.482 [http-nio-8080-exec-4] DEBUG o.j.m.k.k.m.K.selectByKnowledgeId:137 - ==> Parameters: 1949788930195820545(String)
2025-08-16 12:59:31.482 [http-nio-8080-exec-4] DEBUG o.j.m.k.k.m.K.selectByKnowledgeId:137 - <==      Total: 1
2025-08-16 12:59:31.488 [http-nio-8080-exec-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_id = ?)
2025-08-16 12:59:31.489 [http-nio-8080-exec-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949788930195820545(String)
2025-08-16 12:59:31.490 [http-nio-8080-exec-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 2
2025-08-16 12:59:31.493 [http-nio-8080-exec-4] DEBUG o.j.m.km.kno.mapper.KnoTagMapper.selectBatchIds:137 - ==>  Preparing: SELECT id, name, create_time FROM kno_tag WHERE id IN (?, ?)
2025-08-16 12:59:31.494 [http-nio-8080-exec-4] DEBUG o.j.m.km.kno.mapper.KnoTagMapper.selectBatchIds:137 - ==> Parameters: 2(String), 3(String)
2025-08-16 12:59:31.494 [http-nio-8080-exec-4] DEBUG o.j.m.km.kno.mapper.KnoTagMapper.selectBatchIds:137 - <==      Total: 2
2025-08-16 12:59:31.496 [http-nio-8080-exec-4] DEBUG o.j.m.k.k.m.K.selectByKnowledgeId:137 - ==>  Preparing: SELECT * FROM kno_base_detail WHERE knowledge_id = ?
2025-08-16 12:59:31.496 [http-nio-8080-exec-4] DEBUG o.j.m.k.k.m.K.selectByKnowledgeId:137 - ==> Parameters: 1949672449877446657(String)
2025-08-16 12:59:31.498 [http-nio-8080-exec-4] DEBUG o.j.m.k.k.m.K.selectByKnowledgeId:137 - <==      Total: 5
2025-08-16 12:59:31.503 [http-nio-8080-exec-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_id = ?)
2025-08-16 12:59:31.503 [http-nio-8080-exec-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672449877446657(String)
2025-08-16 12:59:31.503 [http-nio-8080-exec-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 12:59:31.506 [http-nio-8080-exec-4] DEBUG o.j.m.k.k.m.K.selectByKnowledgeId:137 - ==>  Preparing: SELECT * FROM kno_base_detail WHERE knowledge_id = ?
2025-08-16 12:59:31.506 [http-nio-8080-exec-4] DEBUG o.j.m.k.k.m.K.selectByKnowledgeId:137 - ==> Parameters: 1949666866969415681(String)
2025-08-16 12:59:31.513 [http-nio-8080-exec-4] DEBUG o.j.m.k.k.m.K.selectByKnowledgeId:137 - <==      Total: 23
2025-08-16 12:59:31.520 [http-nio-8080-exec-4] DEBUG o.j.m.km.kno.mapper.KnoCategoryMapper.selectById:137 - ==>  Preparing: SELECT id, name, parent_id, level, sort, status, create_time, update_time FROM kno_category WHERE id = ?
2025-08-16 12:59:31.521 [http-nio-8080-exec-4] DEBUG o.j.m.km.kno.mapper.KnoCategoryMapper.selectById:137 - ==> Parameters: (String)
2025-08-16 12:59:31.522 [http-nio-8080-exec-4] DEBUG o.j.m.km.kno.mapper.KnoCategoryMapper.selectById:137 - <==      Total: 0
2025-08-16 12:59:31.526 [http-nio-8080-exec-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_id = ?)
2025-08-16 12:59:31.527 [http-nio-8080-exec-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666866969415681(String)
2025-08-16 12:59:31.527 [http-nio-8080-exec-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 12:59:31.528 [http-nio-8080-exec-4] DEBUG org.jeecg.common.aspect.DictAspect:65 - 获取JSON数据 耗时：144ms
2025-08-16 12:59:31.528 [http-nio-8080-exec-4] DEBUG org.jeecg.common.aspect.DictAspect:112 -  __ 进入字典翻译切面 DictAspect —— 
2025-08-16 12:59:31.615 [http-nio-8080-exec-4] DEBUG org.jeecg.common.aspect.DictAspect:363 - translateManyDict.dictCodes:code
2025-08-16 12:59:31.615 [http-nio-8080-exec-4] DEBUG org.jeecg.common.aspect.DictAspect:364 - translateManyDict.values:1,2,3
2025-08-16 12:59:31.623 [http-nio-8080-exec-4] DEBUG org.jeecg.common.aspect.DictAspect:366 - translateManyDict.result:{}
2025-08-16 12:59:31.644 [http-nio-8080-exec-4] DEBUG org.jeecg.common.aspect.DictAspect:69 - 注入字典到JSON数据  耗时116ms
2025-08-16 12:59:33.349 [http-nio-8080-exec-10] INFO  o.j.m.monitor.controller.ActuatorRedisController:117 - 查询磁盘信息:6个
2025-08-16 12:59:33.353 [http-nio-8080-exec-10] INFO  o.j.m.monitor.controller.ActuatorRedisController:130 - {name=OS (C:), rest=185903562752, restPPT=56, max=429700067328}
2025-08-16 12:59:33.365 [http-nio-8080-exec-10] INFO  o.j.m.monitor.controller.ActuatorRedisController:130 - {name=本地磁盘 (D:), rest=138221797376, restPPT=48, max=268607250432}
2025-08-16 12:59:33.370 [http-nio-8080-exec-10] INFO  o.j.m.monitor.controller.ActuatorRedisController:130 - {name=本地磁盘 (E:), rest=57583833088, restPPT=61, max=151161372672}
2025-08-16 12:59:33.374 [http-nio-8080-exec-10] INFO  o.j.m.monitor.controller.ActuatorRedisController:130 - {name=本地磁盘 (F:), rest=69867331584, restPPT=53, max=150828929024}
2025-08-16 12:59:33.376 [http-nio-8080-exec-10] INFO  o.j.m.monitor.controller.ActuatorRedisController:130 - {name=本地磁盘 (G:), rest=137231179776, restPPT=72, max=500170412032}
2025-08-16 12:59:33.380 [http-nio-8080-exec-10] INFO  o.j.m.monitor.controller.ActuatorRedisController:130 - {name=本地磁盘 (H:), rest=260168253440, restPPT=47, max=500030472192}
2025-08-16 12:59:33.380 [http-nio-8080-exec-10] DEBUG org.jeecg.common.aspect.DictAspect:65 - 获取JSON数据 耗时：31ms
2025-08-16 12:59:33.380 [http-nio-8080-exec-10] DEBUG org.jeecg.common.aspect.DictAspect:69 - 注入字典到JSON数据  耗时0ms
2025-08-16 12:59:52.965 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755320392965, dbSize=848}
2025-08-16 12:59:52.966 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755320392966}
2025-08-16 13:00:05.746 [http-nio-8080-exec-6] DEBUG org.jeecg.modules.message.websocket.WebSocket:111 - 【系统 WebSocket】收到客户端消息:ping
2025-08-16 13:00:07.735 [http-nio-8080-exec-7] DEBUG org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-08-16 13:00:13.755 [http-nio-8080-exec-8] DEBUG org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-08-16 13:00:52.964 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755320452964, dbSize=848}
2025-08-16 13:00:52.966 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755320452966}
2025-08-16 13:01:09.743 [http-nio-8080-exec-9] DEBUG org.jeecg.modules.message.websocket.WebSocket:111 - 【系统 WebSocket】收到客户端消息:ping
2025-08-16 13:01:11.747 [http-nio-8080-exec-5] DEBUG org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-08-16 13:01:17.744 [http-nio-8080-exec-1] DEBUG org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-08-16 13:01:52.964 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755320512964, dbSize=848}
2025-08-16 13:01:52.965 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755320512965}
2025-08-16 13:02:13.736 [http-nio-8080-exec-2] DEBUG org.jeecg.modules.message.websocket.WebSocket:111 - 【系统 WebSocket】收到客户端消息:ping
2025-08-16 13:02:15.734 [http-nio-8080-exec-3] DEBUG org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-08-16 13:02:21.751 [http-nio-8080-exec-4] DEBUG org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-08-16 13:02:52.963 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755320572963, dbSize=848}
2025-08-16 13:02:52.965 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755320572965}
2025-08-16 13:03:17.736 [http-nio-8080-exec-10] DEBUG org.jeecg.modules.message.websocket.WebSocket:111 - 【系统 WebSocket】收到客户端消息:ping
2025-08-16 13:03:19.741 [http-nio-8080-exec-6] DEBUG org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-08-16 13:03:25.747 [http-nio-8080-exec-7] DEBUG org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-08-16 13:03:52.964 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755320632964, dbSize=848}
2025-08-16 13:03:52.965 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755320632965}
2025-08-16 13:04:21.737 [http-nio-8080-exec-8] DEBUG org.jeecg.modules.message.websocket.WebSocket:111 - 【系统 WebSocket】收到客户端消息:ping
2025-08-16 13:04:23.734 [http-nio-8080-exec-9] DEBUG org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-08-16 13:04:29.748 [http-nio-8080-exec-5] DEBUG org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-08-16 13:04:52.963 [wordCompass-scheduling-3] INFO  o.j.modules.km.kno.service.KnoBaseDetailService:490 - 开始同步KnoBaseDetail数据到Solr...
2025-08-16 13:04:52.964 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755320692964, dbSize=848}
2025-08-16 13:04:52.966 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755320692966}
2025-08-16 13:04:52.973 [wordCompass-scheduling-3] DEBUG o.j.m.km.kno.mapper.KnoBaseDetailMapper.selectList:137 - ==>  Preparing: SELECT id, title, content, ocr_content, srcipt, srcipt_simplify, category_id, parent_id, knowledge_id, keywords, view_count, status, source_type, creator_name, updater_name, create_time, update_time, organ_name, organ_id, single_image_file_path, from_detail_page_num, from_detail_page_id, order_num FROM kno_base_detail WHERE (status <> ?)
2025-08-16 13:04:52.973 [wordCompass-scheduling-3] DEBUG o.j.m.km.kno.mapper.KnoBaseDetailMapper.selectList:137 - ==> Parameters: 0(Integer)
2025-08-16 13:04:52.979 [wordCompass-scheduling-3] DEBUG o.j.m.km.kno.mapper.KnoBaseDetailMapper.selectList:137 - <==      Total: 28
2025-08-16 13:04:52.983 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:04:52.983 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867036524546(String)
2025-08-16 13:04:52.984 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:04:52.988 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:04:52.988 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867103633409(String)
2025-08-16 13:04:52.989 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:04:52.993 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:04:52.994 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867103633410(String)
2025-08-16 13:04:52.994 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:04:52.998 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:04:52.998 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867103633411(String)
2025-08-16 13:04:52.998 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:04:53.004 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:04:53.004 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867170742274(String)
2025-08-16 13:04:53.004 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:04:53.007 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:04:53.008 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867170742275(String)
2025-08-16 13:04:53.008 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:04:53.011 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:04:53.011 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867170742276(String)
2025-08-16 13:04:53.011 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:04:53.015 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:04:53.015 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867237851138(String)
2025-08-16 13:04:53.017 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:04:53.020 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:04:53.020 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867246239745(String)
2025-08-16 13:04:53.021 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:04:53.024 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:04:53.024 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867246239746(String)
2025-08-16 13:04:53.024 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:04:53.027 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:04:53.028 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867246239747(String)
2025-08-16 13:04:53.028 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:04:53.031 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:04:53.031 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867309154305(String)
2025-08-16 13:04:53.032 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:04:53.037 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:04:53.037 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867309154306(String)
2025-08-16 13:04:53.037 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:04:53.041 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:04:53.041 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867309154307(String)
2025-08-16 13:04:53.042 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:04:53.045 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:04:53.045 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846081(String)
2025-08-16 13:04:53.046 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:04:53.049 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:04:53.049 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846082(String)
2025-08-16 13:04:53.049 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:04:53.053 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:04:53.053 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846083(String)
2025-08-16 13:04:53.055 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:04:53.058 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:04:53.058 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846084(String)
2025-08-16 13:04:53.058 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:04:53.063 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:04:53.063 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867460149249(String)
2025-08-16 13:04:53.064 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:04:53.067 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:04:53.067 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867460149250(String)
2025-08-16 13:04:53.069 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:04:53.072 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:04:53.072 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867460149251(String)
2025-08-16 13:04:53.074 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:04:53.078 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:04:53.078 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867535646721(String)
2025-08-16 13:04:53.079 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:04:53.084 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:04:53.084 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867535646722(String)
2025-08-16 13:04:53.084 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:04:53.088 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:04:53.090 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672451836186626(String)
2025-08-16 13:04:53.090 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:04:53.096 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:04:53.096 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672452326920193(String)
2025-08-16 13:04:53.096 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:04:53.101 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:04:53.101 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672452607938561(String)
2025-08-16 13:04:53.103 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:04:53.108 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:04:53.108 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672452817653761(String)
2025-08-16 13:04:53.110 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:04:53.115 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:04:53.116 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672453232889857(String)
2025-08-16 13:04:53.117 [wordCompass-scheduling-3] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:04:53.121 [wordCompass-scheduling-3] ERROR o.j.modules.km.kno.service.KnoBaseDetailService:507 - 同步KnoBaseDetail数据到Solr失败
org.springframework.dao.DataAccessResourceFailureException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
	at org.springframework.data.solr.core.SolrExceptionTranslator.translateExceptionIfPossible(SolrExceptionTranslator.java:79)
	at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:169)
	at org.springframework.data.solr.core.SolrTemplate.delete(SolrTemplate.java:249)
	at org.springframework.data.solr.core.SolrOperations.delete(SolrOperations.java:228)
	at org.springframework.data.solr.repository.support.SimpleSolrRepository.deleteAll(SimpleSolrRepository.java:212)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
	at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
	at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService.deleteAll(KnoBaseDetailSolrService.java:313)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$FastClassBySpringCGLIB$$1d6fbb9b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$EnhancerBySpringCGLIB$$daa8b9a9.deleteAll(<generated>)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailService.syncToSolr(KnoBaseDetailService.java:497)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
	at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
	at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:940)
	at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:903)
	at org.springframework.data.solr.core.SolrTemplate.lambda$delete$6(SolrTemplate.java:249)
	at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:167)
	... 65 common frames omitted
Caused by: java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 82 common frames omitted
2025-08-16 13:05:25.744 [http-nio-8080-exec-1] DEBUG org.jeecg.modules.message.websocket.WebSocket:111 - 【系统 WebSocket】收到客户端消息:ping
2025-08-16 13:05:27.736 [http-nio-8080-exec-2] DEBUG org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-08-16 13:05:33.744 [http-nio-8080-exec-3] DEBUG org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-08-16 13:05:52.964 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755320752964, dbSize=848}
2025-08-16 13:05:52.966 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755320752966}
2025-08-16 13:06:29.733 [http-nio-8080-exec-4] DEBUG org.jeecg.modules.message.websocket.WebSocket:111 - 【系统 WebSocket】收到客户端消息:ping
2025-08-16 13:06:31.738 [http-nio-8080-exec-10] DEBUG org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-08-16 13:06:37.753 [http-nio-8080-exec-6] DEBUG org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-08-16 13:06:52.965 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755320812965, dbSize=848}
2025-08-16 13:06:52.966 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755320812966}
2025-08-16 13:07:33.737 [http-nio-8080-exec-7] DEBUG org.jeecg.modules.message.websocket.WebSocket:111 - 【系统 WebSocket】收到客户端消息:ping
2025-08-16 13:07:35.741 [http-nio-8080-exec-8] DEBUG org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-08-16 13:07:41.747 [http-nio-8080-exec-9] DEBUG org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-08-16 13:07:52.964 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755320872964, dbSize=848}
2025-08-16 13:07:52.965 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755320872965}
2025-08-16 13:08:37.744 [http-nio-8080-exec-5] DEBUG org.jeecg.modules.message.websocket.WebSocket:111 - 【系统 WebSocket】收到客户端消息:ping
2025-08-16 13:08:39.735 [http-nio-8080-exec-1] DEBUG org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-08-16 13:08:52.963 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755320932963, dbSize=848}
2025-08-16 13:08:52.965 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755320932965}
2025-08-16 13:09:52.963 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755320992963, dbSize=848}
2025-08-16 13:09:52.965 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755320992965}
2025-08-16 13:10:52.965 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755321052965, dbSize=848}
2025-08-16 13:10:52.969 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755321052969}
2025-08-16 13:11:52.966 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755321112966, dbSize=848}
2025-08-16 13:11:52.969 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755321112969}
2025-08-16 13:12:52.963 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755321172963, dbSize=848}
2025-08-16 13:12:52.966 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755321172966}
2025-08-16 13:13:46.925 [http-nio-8080-exec-2] DEBUG org.jeecg.common.aspect.DictAspect:65 - 获取JSON数据 耗时：83ms
2025-08-16 13:13:46.925 [http-nio-8080-exec-2] DEBUG org.jeecg.common.aspect.DictAspect:69 - 注入字典到JSON数据  耗时0ms
2025-08-16 13:13:52.051 [http-nio-8080-exec-3] DEBUG org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-08-16 13:13:52.094 [http-nio-8080-exec-4] DEBUG org.jeecg.common.aspect.DictAspect:65 - 获取JSON数据 耗时：37ms
2025-08-16 13:13:52.094 [http-nio-8080-exec-4] DEBUG org.jeecg.common.aspect.DictAspect:69 - 注入字典到JSON数据  耗时0ms
2025-08-16 13:13:52.964 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755321232964, dbSize=848}
2025-08-16 13:13:52.965 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755321232965}
2025-08-16 13:14:47.741 [http-nio-8080-exec-10] DEBUG org.jeecg.modules.message.websocket.WebSocket:111 - 【系统 WebSocket】收到客户端消息:ping
2025-08-16 13:14:49.746 [http-nio-8080-exec-6] DEBUG org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-08-16 13:14:52.964 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755321292964, dbSize=848}
2025-08-16 13:14:52.966 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755321292966}
2025-08-16 13:14:55.753 [http-nio-8080-exec-7] DEBUG org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-08-16 13:15:51.744 [http-nio-8080-exec-8] DEBUG org.jeecg.modules.message.websocket.WebSocket:111 - 【系统 WebSocket】收到客户端消息:ping
2025-08-16 13:15:52.964 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755321352964, dbSize=848}
2025-08-16 13:15:52.965 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755321352965}
2025-08-16 13:15:53.739 [http-nio-8080-exec-9] DEBUG org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-08-16 13:15:59.756 [http-nio-8080-exec-5] DEBUG org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-08-16 13:16:52.965 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755321412965, dbSize=848}
2025-08-16 13:16:52.967 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755321412967}
2025-08-16 13:16:55.741 [http-nio-8080-exec-1] DEBUG org.jeecg.modules.message.websocket.WebSocket:111 - 【系统 WebSocket】收到客户端消息:ping
2025-08-16 13:16:57.741 [http-nio-8080-exec-2] DEBUG org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-08-16 13:17:03.777 [http-nio-8080-exec-3] DEBUG org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-08-16 13:17:52.964 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755321472964, dbSize=848}
2025-08-16 13:17:52.965 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755321472965}
2025-08-16 13:17:59.738 [http-nio-8080-exec-4] DEBUG org.jeecg.modules.message.websocket.WebSocket:111 - 【系统 WebSocket】收到客户端消息:ping
2025-08-16 13:18:01.733 [http-nio-8080-exec-10] DEBUG org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-08-16 13:18:07.750 [http-nio-8080-exec-6] DEBUG org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-08-16 13:18:52.964 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755321532964, dbSize=848}
2025-08-16 13:18:52.965 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755321532965}
2025-08-16 13:19:03.736 [http-nio-8080-exec-7] DEBUG org.jeecg.modules.message.websocket.WebSocket:111 - 【系统 WebSocket】收到客户端消息:ping
2025-08-16 13:19:05.732 [http-nio-8080-exec-8] DEBUG org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-08-16 13:19:11.745 [http-nio-8080-exec-9] DEBUG org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-08-16 13:19:52.963 [wordCompass-scheduling-9] INFO  o.j.modules.km.kno.service.KnoBaseDetailService:490 - 开始同步KnoBaseDetail数据到Solr...
2025-08-16 13:19:52.964 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755321592964, dbSize=848}
2025-08-16 13:19:52.966 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755321592966}
2025-08-16 13:19:52.974 [wordCompass-scheduling-9] DEBUG o.j.m.km.kno.mapper.KnoBaseDetailMapper.selectList:137 - ==>  Preparing: SELECT id, title, content, ocr_content, srcipt, srcipt_simplify, category_id, parent_id, knowledge_id, keywords, view_count, status, source_type, creator_name, updater_name, create_time, update_time, organ_name, organ_id, single_image_file_path, from_detail_page_num, from_detail_page_id, order_num FROM kno_base_detail WHERE (status <> ?)
2025-08-16 13:19:52.974 [wordCompass-scheduling-9] DEBUG o.j.m.km.kno.mapper.KnoBaseDetailMapper.selectList:137 - ==> Parameters: 0(Integer)
2025-08-16 13:19:52.979 [wordCompass-scheduling-9] DEBUG o.j.m.km.kno.mapper.KnoBaseDetailMapper.selectList:137 - <==      Total: 28
2025-08-16 13:19:52.984 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:19:52.984 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867036524546(String)
2025-08-16 13:19:52.985 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:19:52.989 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:19:52.989 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867103633409(String)
2025-08-16 13:19:52.989 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:19:52.993 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:19:52.993 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867103633410(String)
2025-08-16 13:19:52.994 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:19:52.998 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:19:52.998 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867103633411(String)
2025-08-16 13:19:52.998 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:19:53.002 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:19:53.002 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867170742274(String)
2025-08-16 13:19:53.002 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:19:53.005 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:19:53.006 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867170742275(String)
2025-08-16 13:19:53.006 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:19:53.010 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:19:53.010 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867170742276(String)
2025-08-16 13:19:53.011 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:19:53.014 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:19:53.014 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867237851138(String)
2025-08-16 13:19:53.015 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:19:53.019 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:19:53.019 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867246239745(String)
2025-08-16 13:19:53.020 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:19:53.023 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:19:53.023 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867246239746(String)
2025-08-16 13:19:53.024 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:19:53.027 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:19:53.028 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867246239747(String)
2025-08-16 13:19:53.028 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:19:53.032 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:19:53.032 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867309154305(String)
2025-08-16 13:19:53.034 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:19:53.037 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:19:53.039 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867309154306(String)
2025-08-16 13:19:53.039 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:19:53.042 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:19:53.042 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867309154307(String)
2025-08-16 13:19:53.043 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:19:53.047 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:19:53.047 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846081(String)
2025-08-16 13:19:53.048 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:19:53.052 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:19:53.052 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846082(String)
2025-08-16 13:19:53.053 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:19:53.057 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:19:53.057 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846083(String)
2025-08-16 13:19:53.058 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:19:53.062 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:19:53.062 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846084(String)
2025-08-16 13:19:53.062 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:19:53.065 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:19:53.065 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867460149249(String)
2025-08-16 13:19:53.067 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:19:53.070 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:19:53.070 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867460149250(String)
2025-08-16 13:19:53.071 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:19:53.074 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:19:53.074 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867460149251(String)
2025-08-16 13:19:53.074 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:19:53.078 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:19:53.078 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867535646721(String)
2025-08-16 13:19:53.078 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:19:53.083 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:19:53.083 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867535646722(String)
2025-08-16 13:19:53.083 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:19:53.087 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:19:53.088 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672451836186626(String)
2025-08-16 13:19:53.088 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:19:53.092 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:19:53.092 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672452326920193(String)
2025-08-16 13:19:53.092 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:19:53.096 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:19:53.096 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672452607938561(String)
2025-08-16 13:19:53.096 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:19:53.100 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:19:53.100 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672452817653761(String)
2025-08-16 13:19:53.100 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:19:53.105 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:19:53.105 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672453232889857(String)
2025-08-16 13:19:53.105 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:19:53.120 [wordCompass-scheduling-9] ERROR o.j.modules.km.kno.service.KnoBaseDetailService:507 - 同步KnoBaseDetail数据到Solr失败
org.springframework.dao.DataAccessResourceFailureException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
	at org.springframework.data.solr.core.SolrExceptionTranslator.translateExceptionIfPossible(SolrExceptionTranslator.java:79)
	at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:169)
	at org.springframework.data.solr.core.SolrTemplate.delete(SolrTemplate.java:249)
	at org.springframework.data.solr.core.SolrOperations.delete(SolrOperations.java:228)
	at org.springframework.data.solr.repository.support.SimpleSolrRepository.deleteAll(SimpleSolrRepository.java:212)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
	at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
	at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService.deleteAll(KnoBaseDetailSolrService.java:313)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$FastClassBySpringCGLIB$$1d6fbb9b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$EnhancerBySpringCGLIB$$daa8b9a9.deleteAll(<generated>)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailService.syncToSolr(KnoBaseDetailService.java:497)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
	at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
	at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:940)
	at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:903)
	at org.springframework.data.solr.core.SolrTemplate.lambda$delete$6(SolrTemplate.java:249)
	at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:167)
	... 65 common frames omitted
Caused by: java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 82 common frames omitted
2025-08-16 13:20:07.740 [http-nio-8080-exec-5] DEBUG org.jeecg.modules.message.websocket.WebSocket:111 - 【系统 WebSocket】收到客户端消息:ping
2025-08-16 13:20:09.747 [http-nio-8080-exec-1] DEBUG org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-08-16 13:20:15.756 [http-nio-8080-exec-2] DEBUG org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-08-16 13:20:52.964 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755321652964, dbSize=848}
2025-08-16 13:20:52.965 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755321652965}
2025-08-16 13:21:11.736 [http-nio-8080-exec-3] DEBUG org.jeecg.modules.message.websocket.WebSocket:111 - 【系统 WebSocket】收到客户端消息:ping
2025-08-16 13:21:13.740 [http-nio-8080-exec-4] DEBUG org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-08-16 13:21:19.749 [http-nio-8080-exec-10] DEBUG org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-08-16 13:21:52.964 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755321712964, dbSize=848}
2025-08-16 13:21:52.966 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755321712966}
2025-08-16 13:22:15.741 [http-nio-8080-exec-6] DEBUG org.jeecg.modules.message.websocket.WebSocket:111 - 【系统 WebSocket】收到客户端消息:ping
2025-08-16 13:22:17.743 [http-nio-8080-exec-7] DEBUG org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-08-16 13:22:23.752 [http-nio-8080-exec-8] DEBUG org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-08-16 13:22:52.964 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755321772964, dbSize=848}
2025-08-16 13:22:52.965 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755321772965}
2025-08-16 13:23:19.735 [http-nio-8080-exec-9] DEBUG org.jeecg.modules.message.websocket.WebSocket:111 - 【系统 WebSocket】收到客户端消息:ping
2025-08-16 13:23:21.738 [http-nio-8080-exec-5] DEBUG org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-08-16 13:23:27.753 [http-nio-8080-exec-1] DEBUG org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-08-16 13:23:52.964 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755321832964, dbSize=848}
2025-08-16 13:23:52.965 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755321832965}
2025-08-16 13:24:23.731 [http-nio-8080-exec-2] DEBUG org.jeecg.modules.message.websocket.WebSocket:111 - 【系统 WebSocket】收到客户端消息:ping
2025-08-16 13:24:25.741 [http-nio-8080-exec-3] DEBUG org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-08-16 13:24:52.964 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755321892964, dbSize=848}
2025-08-16 13:24:52.966 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755321892966}
2025-08-16 13:25:52.964 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755321952964, dbSize=848}
2025-08-16 13:25:52.965 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755321952965}
2025-08-16 13:26:52.964 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755322012964, dbSize=848}
2025-08-16 13:26:52.966 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755322012966}
2025-08-16 13:27:52.964 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755322072964, dbSize=848}
2025-08-16 13:27:52.966 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755322072966}
2025-08-16 13:28:52.964 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755322132964, dbSize=848}
2025-08-16 13:28:52.966 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755322132966}
2025-08-16 13:29:52.964 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755322192964, dbSize=848}
2025-08-16 13:29:52.966 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755322192966}
2025-08-16 13:30:52.965 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755322252965, dbSize=848}
2025-08-16 13:30:52.967 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755322252967}
2025-08-16 13:31:52.967 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755322312967, dbSize=848}
2025-08-16 13:31:52.969 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755322312969}
2025-08-16 13:32:52.964 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755322372964, dbSize=848}
2025-08-16 13:32:52.966 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755322372966}
2025-08-16 13:33:52.964 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755322432964, dbSize=848}
2025-08-16 13:33:52.965 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755322432965}
2025-08-16 13:34:52.963 [wordCompass-scheduling-6] INFO  o.j.modules.km.kno.service.KnoBaseDetailService:490 - 开始同步KnoBaseDetail数据到Solr...
2025-08-16 13:34:52.964 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755322492964, dbSize=848}
2025-08-16 13:34:52.967 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755322492965}
2025-08-16 13:34:52.980 [wordCompass-scheduling-6] DEBUG o.j.m.km.kno.mapper.KnoBaseDetailMapper.selectList:137 - ==>  Preparing: SELECT id, title, content, ocr_content, srcipt, srcipt_simplify, category_id, parent_id, knowledge_id, keywords, view_count, status, source_type, creator_name, updater_name, create_time, update_time, organ_name, organ_id, single_image_file_path, from_detail_page_num, from_detail_page_id, order_num FROM kno_base_detail WHERE (status <> ?)
2025-08-16 13:34:52.980 [wordCompass-scheduling-6] DEBUG o.j.m.km.kno.mapper.KnoBaseDetailMapper.selectList:137 - ==> Parameters: 0(Integer)
2025-08-16 13:34:52.987 [wordCompass-scheduling-6] DEBUG o.j.m.km.kno.mapper.KnoBaseDetailMapper.selectList:137 - <==      Total: 28
2025-08-16 13:34:52.992 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:34:52.993 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867036524546(String)
2025-08-16 13:34:52.993 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:34:52.999 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:34:52.999 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867103633409(String)
2025-08-16 13:34:52.999 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:34:53.005 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:34:53.006 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867103633410(String)
2025-08-16 13:34:53.007 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:34:53.014 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:34:53.014 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867103633411(String)
2025-08-16 13:34:53.015 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:34:53.021 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:34:53.021 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867170742274(String)
2025-08-16 13:34:53.022 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:34:53.028 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:34:53.028 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867170742275(String)
2025-08-16 13:34:53.029 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:34:53.034 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:34:53.034 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867170742276(String)
2025-08-16 13:34:53.034 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:34:53.041 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:34:53.041 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867237851138(String)
2025-08-16 13:34:53.041 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:34:53.046 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:34:53.046 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867246239745(String)
2025-08-16 13:34:53.047 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:34:53.051 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:34:53.051 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867246239746(String)
2025-08-16 13:34:53.051 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:34:53.057 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:34:53.057 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867246239747(String)
2025-08-16 13:34:53.057 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:34:53.062 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:34:53.062 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867309154305(String)
2025-08-16 13:34:53.063 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:34:53.065 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:34:53.067 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867309154306(String)
2025-08-16 13:34:53.067 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:34:53.072 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:34:53.072 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867309154307(String)
2025-08-16 13:34:53.072 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:34:53.077 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:34:53.077 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846081(String)
2025-08-16 13:34:53.078 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:34:53.082 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:34:53.082 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846082(String)
2025-08-16 13:34:53.084 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:34:53.088 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:34:53.088 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846083(String)
2025-08-16 13:34:53.088 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:34:53.095 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:34:53.095 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846084(String)
2025-08-16 13:34:53.096 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:34:53.100 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:34:53.100 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867460149249(String)
2025-08-16 13:34:53.101 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:34:53.108 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:34:53.108 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867460149250(String)
2025-08-16 13:34:53.109 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:34:53.115 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:34:53.115 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867460149251(String)
2025-08-16 13:34:53.116 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:34:53.120 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:34:53.120 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867535646721(String)
2025-08-16 13:34:53.121 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:34:53.126 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:34:53.126 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867535646722(String)
2025-08-16 13:34:53.126 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:34:53.131 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:34:53.131 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672451836186626(String)
2025-08-16 13:34:53.131 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:34:53.136 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:34:53.136 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672452326920193(String)
2025-08-16 13:34:53.136 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:34:53.140 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:34:53.140 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672452607938561(String)
2025-08-16 13:34:53.141 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:34:53.148 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:34:53.148 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672452817653761(String)
2025-08-16 13:34:53.149 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:34:53.153 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:34:53.153 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672453232889857(String)
2025-08-16 13:34:53.154 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:34:53.167 [wordCompass-scheduling-6] ERROR o.j.modules.km.kno.service.KnoBaseDetailService:507 - 同步KnoBaseDetail数据到Solr失败
org.springframework.dao.DataAccessResourceFailureException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
	at org.springframework.data.solr.core.SolrExceptionTranslator.translateExceptionIfPossible(SolrExceptionTranslator.java:79)
	at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:169)
	at org.springframework.data.solr.core.SolrTemplate.delete(SolrTemplate.java:249)
	at org.springframework.data.solr.core.SolrOperations.delete(SolrOperations.java:228)
	at org.springframework.data.solr.repository.support.SimpleSolrRepository.deleteAll(SimpleSolrRepository.java:212)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
	at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
	at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService.deleteAll(KnoBaseDetailSolrService.java:313)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$FastClassBySpringCGLIB$$1d6fbb9b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$EnhancerBySpringCGLIB$$daa8b9a9.deleteAll(<generated>)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailService.syncToSolr(KnoBaseDetailService.java:497)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
	at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
	at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:940)
	at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:903)
	at org.springframework.data.solr.core.SolrTemplate.lambda$delete$6(SolrTemplate.java:249)
	at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:167)
	... 65 common frames omitted
Caused by: java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 82 common frames omitted
2025-08-16 13:35:52.964 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755322552964, dbSize=848}
2025-08-16 13:35:52.967 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755322552967}
2025-08-16 13:36:52.964 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755322612964, dbSize=848}
2025-08-16 13:36:52.967 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755322612967}
2025-08-16 13:37:52.965 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755322672965, dbSize=848}
2025-08-16 13:37:52.966 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755322672966}
2025-08-16 13:38:52.964 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755322732964, dbSize=848}
2025-08-16 13:38:52.966 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755322732966}
2025-08-16 13:39:52.964 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755322792964, dbSize=848}
2025-08-16 13:39:52.966 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755322792966}
2025-08-16 13:40:52.964 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755322852964, dbSize=848}
2025-08-16 13:40:52.966 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755322852966}
2025-08-16 13:41:52.965 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755322912965, dbSize=848}
2025-08-16 13:41:52.967 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755322912967}
2025-08-16 13:42:52.965 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755322972965, dbSize=848}
2025-08-16 13:42:52.967 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755322972967}
2025-08-16 13:43:52.965 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755323032965, dbSize=848}
2025-08-16 13:43:52.967 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755323032967}
2025-08-16 13:44:52.967 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755323092967, dbSize=848}
2025-08-16 13:44:52.970 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755323092970}
2025-08-16 13:45:52.964 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755323152964, dbSize=848}
2025-08-16 13:45:52.966 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755323152966}
2025-08-16 13:46:52.965 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755323212965, dbSize=848}
2025-08-16 13:46:52.969 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755323212969}
2025-08-16 13:47:52.966 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755323272966, dbSize=848}
2025-08-16 13:47:52.968 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755323272968}
2025-08-16 13:48:52.965 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755323332965, dbSize=848}
2025-08-16 13:48:52.967 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755323332967}
2025-08-16 13:49:52.963 [wordCompass-scheduling-6] INFO  o.j.modules.km.kno.service.KnoBaseDetailService:490 - 开始同步KnoBaseDetail数据到Solr...
2025-08-16 13:49:52.964 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755323392964, dbSize=848}
2025-08-16 13:49:52.966 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755323392966}
2025-08-16 13:49:52.975 [wordCompass-scheduling-6] DEBUG o.j.m.km.kno.mapper.KnoBaseDetailMapper.selectList:137 - ==>  Preparing: SELECT id, title, content, ocr_content, srcipt, srcipt_simplify, category_id, parent_id, knowledge_id, keywords, view_count, status, source_type, creator_name, updater_name, create_time, update_time, organ_name, organ_id, single_image_file_path, from_detail_page_num, from_detail_page_id, order_num FROM kno_base_detail WHERE (status <> ?)
2025-08-16 13:49:52.975 [wordCompass-scheduling-6] DEBUG o.j.m.km.kno.mapper.KnoBaseDetailMapper.selectList:137 - ==> Parameters: 0(Integer)
2025-08-16 13:49:52.982 [wordCompass-scheduling-6] DEBUG o.j.m.km.kno.mapper.KnoBaseDetailMapper.selectList:137 - <==      Total: 28
2025-08-16 13:49:52.987 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:49:52.988 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867036524546(String)
2025-08-16 13:49:52.988 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:49:52.993 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:49:52.994 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867103633409(String)
2025-08-16 13:49:52.994 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:49:53.001 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:49:53.001 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867103633410(String)
2025-08-16 13:49:53.002 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:49:53.007 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:49:53.007 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867103633411(String)
2025-08-16 13:49:53.008 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:49:53.011 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:49:53.011 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867170742274(String)
2025-08-16 13:49:53.013 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:49:53.018 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:49:53.018 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867170742275(String)
2025-08-16 13:49:53.019 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:49:53.022 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:49:53.022 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867170742276(String)
2025-08-16 13:49:53.022 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:49:53.026 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:49:53.027 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867237851138(String)
2025-08-16 13:49:53.027 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:49:53.031 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:49:53.031 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867246239745(String)
2025-08-16 13:49:53.032 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:49:53.036 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:49:53.037 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867246239746(String)
2025-08-16 13:49:53.037 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:49:53.042 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:49:53.042 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867246239747(String)
2025-08-16 13:49:53.043 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:49:53.048 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:49:53.048 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867309154305(String)
2025-08-16 13:49:53.049 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:49:53.052 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:49:53.053 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867309154306(String)
2025-08-16 13:49:53.053 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:49:53.057 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:49:53.057 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867309154307(String)
2025-08-16 13:49:53.057 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:49:53.061 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:49:53.061 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846081(String)
2025-08-16 13:49:53.061 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:49:53.066 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:49:53.066 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846082(String)
2025-08-16 13:49:53.066 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:49:53.069 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:49:53.069 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846083(String)
2025-08-16 13:49:53.069 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:49:53.074 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:49:53.075 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846084(String)
2025-08-16 13:49:53.075 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:49:53.080 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:49:53.081 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867460149249(String)
2025-08-16 13:49:53.081 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:49:53.086 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:49:53.086 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867460149250(String)
2025-08-16 13:49:53.086 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:49:53.090 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:49:53.090 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867460149251(String)
2025-08-16 13:49:53.091 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:49:53.094 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:49:53.094 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867535646721(String)
2025-08-16 13:49:53.096 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:49:53.099 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:49:53.099 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867535646722(String)
2025-08-16 13:49:53.100 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:49:53.103 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:49:53.104 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672451836186626(String)
2025-08-16 13:49:53.104 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:49:53.108 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:49:53.109 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672452326920193(String)
2025-08-16 13:49:53.109 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:49:53.113 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:49:53.113 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672452607938561(String)
2025-08-16 13:49:53.114 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:49:53.117 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:49:53.117 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672452817653761(String)
2025-08-16 13:49:53.117 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:49:53.121 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 13:49:53.122 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672453232889857(String)
2025-08-16 13:49:53.122 [wordCompass-scheduling-6] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 13:49:53.126 [wordCompass-scheduling-6] ERROR o.j.modules.km.kno.service.KnoBaseDetailService:507 - 同步KnoBaseDetail数据到Solr失败
org.springframework.dao.DataAccessResourceFailureException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
	at org.springframework.data.solr.core.SolrExceptionTranslator.translateExceptionIfPossible(SolrExceptionTranslator.java:79)
	at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:169)
	at org.springframework.data.solr.core.SolrTemplate.delete(SolrTemplate.java:249)
	at org.springframework.data.solr.core.SolrOperations.delete(SolrOperations.java:228)
	at org.springframework.data.solr.repository.support.SimpleSolrRepository.deleteAll(SimpleSolrRepository.java:212)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
	at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
	at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService.deleteAll(KnoBaseDetailSolrService.java:313)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$FastClassBySpringCGLIB$$1d6fbb9b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$EnhancerBySpringCGLIB$$daa8b9a9.deleteAll(<generated>)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailService.syncToSolr(KnoBaseDetailService.java:497)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
	at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
	at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:940)
	at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:903)
	at org.springframework.data.solr.core.SolrTemplate.lambda$delete$6(SolrTemplate.java:249)
	at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:167)
	... 65 common frames omitted
Caused by: java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 82 common frames omitted
2025-08-16 13:50:52.965 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755323452965, dbSize=848}
2025-08-16 13:50:52.966 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755323452966}
2025-08-16 13:51:52.964 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755323512964, dbSize=848}
2025-08-16 13:51:52.965 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755323512965}
2025-08-16 13:52:52.964 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755323572964, dbSize=848}
2025-08-16 13:52:52.966 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755323572966}
2025-08-16 13:53:52.964 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755323632964, dbSize=848}
2025-08-16 13:53:52.968 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755323632968}
2025-08-16 13:54:52.964 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755323692964, dbSize=848}
2025-08-16 13:54:52.966 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755323692966}
2025-08-16 13:55:52.964 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755323752964, dbSize=848}
2025-08-16 13:55:52.966 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755323752966}
2025-08-16 13:56:52.964 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755323812964, dbSize=848}
2025-08-16 13:56:52.966 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755323812966}
2025-08-16 13:57:52.965 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755323872965, dbSize=848}
2025-08-16 13:57:52.968 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755323872968}
2025-08-16 13:58:52.964 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755323932964, dbSize=848}
2025-08-16 13:58:52.966 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755323932966}
2025-08-16 13:59:52.964 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755323992964, dbSize=848}
2025-08-16 13:59:52.966 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755323992966}
2025-08-16 14:00:52.964 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755324052964, dbSize=848}
2025-08-16 14:00:52.966 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755324052966}
2025-08-16 14:01:52.964 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755324112964, dbSize=848}
2025-08-16 14:01:52.965 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755324112965}
2025-08-16 14:02:52.965 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755324172965, dbSize=848}
2025-08-16 14:02:52.968 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755324172968}
2025-08-16 14:03:52.964 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755324232964, dbSize=848}
2025-08-16 14:03:52.965 [wordCompass-scheduling-5] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755324232965}
2025-08-16 14:04:52.963 [wordCompass-scheduling-9] INFO  o.j.modules.km.kno.service.KnoBaseDetailService:490 - 开始同步KnoBaseDetail数据到Solr...
2025-08-16 14:04:52.964 [wordCompass-scheduling-2] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755324292964, dbSize=848}
2025-08-16 14:04:52.966 [wordCompass-scheduling-2] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755324292966}
2025-08-16 14:04:52.972 [wordCompass-scheduling-9] DEBUG o.j.m.km.kno.mapper.KnoBaseDetailMapper.selectList:137 - ==>  Preparing: SELECT id, title, content, ocr_content, srcipt, srcipt_simplify, category_id, parent_id, knowledge_id, keywords, view_count, status, source_type, creator_name, updater_name, create_time, update_time, organ_name, organ_id, single_image_file_path, from_detail_page_num, from_detail_page_id, order_num FROM kno_base_detail WHERE (status <> ?)
2025-08-16 14:04:52.974 [wordCompass-scheduling-9] DEBUG o.j.m.km.kno.mapper.KnoBaseDetailMapper.selectList:137 - ==> Parameters: 0(Integer)
2025-08-16 14:04:52.980 [wordCompass-scheduling-9] DEBUG o.j.m.km.kno.mapper.KnoBaseDetailMapper.selectList:137 - <==      Total: 28
2025-08-16 14:04:52.984 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:04:52.984 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867036524546(String)
2025-08-16 14:04:52.985 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:04:52.988 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:04:52.989 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867103633409(String)
2025-08-16 14:04:52.990 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:04:52.993 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:04:52.993 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867103633410(String)
2025-08-16 14:04:52.994 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:04:52.997 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:04:53.000 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867103633411(String)
2025-08-16 14:04:53.000 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:04:53.004 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:04:53.005 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867170742274(String)
2025-08-16 14:04:53.005 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:04:53.008 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:04:53.008 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867170742275(String)
2025-08-16 14:04:53.009 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:04:53.012 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:04:53.012 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867170742276(String)
2025-08-16 14:04:53.012 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:04:53.015 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:04:53.015 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867237851138(String)
2025-08-16 14:04:53.017 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:04:53.019 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:04:53.019 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867246239745(String)
2025-08-16 14:04:53.020 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:04:53.023 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:04:53.024 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867246239746(String)
2025-08-16 14:04:53.024 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:04:53.027 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:04:53.027 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867246239747(String)
2025-08-16 14:04:53.028 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:04:53.031 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:04:53.031 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867309154305(String)
2025-08-16 14:04:53.032 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:04:53.035 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:04:53.035 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867309154306(String)
2025-08-16 14:04:53.036 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:04:53.040 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:04:53.040 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867309154307(String)
2025-08-16 14:04:53.041 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:04:53.044 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:04:53.044 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846081(String)
2025-08-16 14:04:53.046 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:04:53.050 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:04:53.051 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846082(String)
2025-08-16 14:04:53.051 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:04:53.055 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:04:53.056 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846083(String)
2025-08-16 14:04:53.057 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:04:53.061 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:04:53.061 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846084(String)
2025-08-16 14:04:53.061 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:04:53.066 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:04:53.066 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867460149249(String)
2025-08-16 14:04:53.067 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:04:53.071 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:04:53.071 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867460149250(String)
2025-08-16 14:04:53.072 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:04:53.077 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:04:53.077 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867460149251(String)
2025-08-16 14:04:53.078 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:04:53.081 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:04:53.081 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867535646721(String)
2025-08-16 14:04:53.082 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:04:53.086 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:04:53.086 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867535646722(String)
2025-08-16 14:04:53.087 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:04:53.091 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:04:53.092 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672451836186626(String)
2025-08-16 14:04:53.092 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:04:53.096 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:04:53.096 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672452326920193(String)
2025-08-16 14:04:53.097 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:04:53.101 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:04:53.101 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672452607938561(String)
2025-08-16 14:04:53.102 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:04:53.105 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:04:53.105 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672452817653761(String)
2025-08-16 14:04:53.106 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:04:53.110 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:04:53.110 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672453232889857(String)
2025-08-16 14:04:53.110 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:04:53.114 [wordCompass-scheduling-9] ERROR o.j.modules.km.kno.service.KnoBaseDetailService:507 - 同步KnoBaseDetail数据到Solr失败
org.springframework.dao.DataAccessResourceFailureException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
	at org.springframework.data.solr.core.SolrExceptionTranslator.translateExceptionIfPossible(SolrExceptionTranslator.java:79)
	at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:169)
	at org.springframework.data.solr.core.SolrTemplate.delete(SolrTemplate.java:249)
	at org.springframework.data.solr.core.SolrOperations.delete(SolrOperations.java:228)
	at org.springframework.data.solr.repository.support.SimpleSolrRepository.deleteAll(SimpleSolrRepository.java:212)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
	at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
	at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService.deleteAll(KnoBaseDetailSolrService.java:313)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$FastClassBySpringCGLIB$$1d6fbb9b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$EnhancerBySpringCGLIB$$daa8b9a9.deleteAll(<generated>)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailService.syncToSolr(KnoBaseDetailService.java:497)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
	at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
	at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:940)
	at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:903)
	at org.springframework.data.solr.core.SolrTemplate.lambda$delete$6(SolrTemplate.java:249)
	at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:167)
	... 65 common frames omitted
Caused by: java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 82 common frames omitted
2025-08-16 14:05:52.979 [wordCompass-scheduling-2] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755324352979, dbSize=848}
2025-08-16 14:05:52.979 [wordCompass-scheduling-2] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755324352979}
2025-08-16 14:06:52.975 [wordCompass-scheduling-2] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755324412975, dbSize=848}
2025-08-16 14:06:52.975 [wordCompass-scheduling-2] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755324412975}
2025-08-16 14:07:52.968 [wordCompass-scheduling-2] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755324472968, dbSize=848}
2025-08-16 14:07:52.970 [wordCompass-scheduling-2] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755324472970}
2025-08-16 14:08:52.974 [wordCompass-scheduling-2] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755324532974, dbSize=848}
2025-08-16 14:08:52.974 [wordCompass-scheduling-2] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755324532974}
2025-08-16 14:09:52.975 [wordCompass-scheduling-2] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755324592975, dbSize=848}
2025-08-16 14:09:52.975 [wordCompass-scheduling-2] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755324592975}
2025-08-16 14:10:52.967 [wordCompass-scheduling-2] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755324652967, dbSize=848}
2025-08-16 14:10:52.970 [wordCompass-scheduling-2] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755324652970}
2025-08-16 14:11:52.965 [wordCompass-scheduling-2] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755324712965, dbSize=848}
2025-08-16 14:11:52.967 [wordCompass-scheduling-2] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755324712967}
2025-08-16 14:12:52.971 [wordCompass-scheduling-2] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755324772971, dbSize=848}
2025-08-16 14:12:52.973 [wordCompass-scheduling-2] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755324772973}
2025-08-16 14:13:52.974 [wordCompass-scheduling-2] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755324832974, dbSize=848}
2025-08-16 14:13:52.976 [wordCompass-scheduling-2] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755324832976}
2025-08-16 14:14:52.971 [wordCompass-scheduling-2] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755324892971, dbSize=848}
2025-08-16 14:14:52.974 [wordCompass-scheduling-2] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755324892974}
2025-08-16 14:15:52.972 [wordCompass-scheduling-2] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755324952972, dbSize=848}
2025-08-16 14:15:52.973 [wordCompass-scheduling-2] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755324952973}
2025-08-16 14:16:52.962 [wordCompass-scheduling-2] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755325012962, dbSize=848}
2025-08-16 14:16:52.962 [wordCompass-scheduling-2] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755325012962}
2025-08-16 14:17:52.967 [wordCompass-scheduling-2] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755325072967, dbSize=848}
2025-08-16 14:17:52.970 [wordCompass-scheduling-2] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755325072970}
2025-08-16 14:18:52.974 [wordCompass-scheduling-2] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755325132974, dbSize=848}
2025-08-16 14:18:52.974 [wordCompass-scheduling-2] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755325132974}
2025-08-16 14:19:52.964 [wordCompass-scheduling-7] INFO  o.j.modules.km.kno.service.KnoBaseDetailService:490 - 开始同步KnoBaseDetail数据到Solr...
2025-08-16 14:19:52.966 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755325192966, dbSize=848}
2025-08-16 14:19:52.967 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755325192967}
2025-08-16 14:19:52.980 [wordCompass-scheduling-7] DEBUG o.j.m.km.kno.mapper.KnoBaseDetailMapper.selectList:137 - ==>  Preparing: SELECT id, title, content, ocr_content, srcipt, srcipt_simplify, category_id, parent_id, knowledge_id, keywords, view_count, status, source_type, creator_name, updater_name, create_time, update_time, organ_name, organ_id, single_image_file_path, from_detail_page_num, from_detail_page_id, order_num FROM kno_base_detail WHERE (status <> ?)
2025-08-16 14:19:52.980 [wordCompass-scheduling-7] DEBUG o.j.m.km.kno.mapper.KnoBaseDetailMapper.selectList:137 - ==> Parameters: 0(Integer)
2025-08-16 14:19:52.990 [wordCompass-scheduling-7] DEBUG o.j.m.km.kno.mapper.KnoBaseDetailMapper.selectList:137 - <==      Total: 28
2025-08-16 14:19:52.996 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:19:52.996 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867036524546(String)
2025-08-16 14:19:52.996 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:19:53.003 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:19:53.003 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867103633409(String)
2025-08-16 14:19:53.003 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:19:53.008 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:19:53.008 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867103633410(String)
2025-08-16 14:19:53.008 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:19:53.013 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:19:53.013 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867103633411(String)
2025-08-16 14:19:53.017 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:19:53.020 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:19:53.020 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867170742274(String)
2025-08-16 14:19:53.022 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:19:53.029 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:19:53.030 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867170742275(String)
2025-08-16 14:19:53.030 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:19:53.035 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:19:53.035 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867170742276(String)
2025-08-16 14:19:53.035 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:19:53.040 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:19:53.040 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867237851138(String)
2025-08-16 14:19:53.042 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:19:53.047 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:19:53.047 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867246239745(String)
2025-08-16 14:19:53.048 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:19:53.053 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:19:53.053 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867246239746(String)
2025-08-16 14:19:53.054 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:19:53.058 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:19:53.058 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867246239747(String)
2025-08-16 14:19:53.059 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:19:53.063 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:19:53.063 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867309154305(String)
2025-08-16 14:19:53.063 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:19:53.067 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:19:53.067 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867309154306(String)
2025-08-16 14:19:53.070 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:19:53.070 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:19:53.074 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867309154307(String)
2025-08-16 14:19:53.074 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:19:53.080 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:19:53.080 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846081(String)
2025-08-16 14:19:53.082 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:19:53.086 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:19:53.086 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846082(String)
2025-08-16 14:19:53.086 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:19:53.093 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:19:53.093 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846083(String)
2025-08-16 14:19:53.096 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:19:53.102 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:19:53.102 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846084(String)
2025-08-16 14:19:53.103 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:19:53.108 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:19:53.108 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867460149249(String)
2025-08-16 14:19:53.108 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:19:53.113 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:19:53.117 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867460149250(String)
2025-08-16 14:19:53.117 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:19:53.123 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:19:53.123 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867460149251(String)
2025-08-16 14:19:53.123 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:19:53.128 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:19:53.128 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867535646721(String)
2025-08-16 14:19:53.128 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:19:53.134 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:19:53.134 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867535646722(String)
2025-08-16 14:19:53.134 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:19:53.139 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:19:53.139 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672451836186626(String)
2025-08-16 14:19:53.140 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:19:53.146 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:19:53.146 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672452326920193(String)
2025-08-16 14:19:53.146 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:19:53.149 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:19:53.149 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672452607938561(String)
2025-08-16 14:19:53.153 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:19:53.156 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:19:53.156 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672452817653761(String)
2025-08-16 14:19:53.156 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:19:53.161 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:19:53.161 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672453232889857(String)
2025-08-16 14:19:53.163 [wordCompass-scheduling-7] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:19:53.165 [wordCompass-scheduling-7] ERROR o.j.modules.km.kno.service.KnoBaseDetailService:507 - 同步KnoBaseDetail数据到Solr失败
org.springframework.dao.DataAccessResourceFailureException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
	at org.springframework.data.solr.core.SolrExceptionTranslator.translateExceptionIfPossible(SolrExceptionTranslator.java:79)
	at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:169)
	at org.springframework.data.solr.core.SolrTemplate.delete(SolrTemplate.java:249)
	at org.springframework.data.solr.core.SolrOperations.delete(SolrOperations.java:228)
	at org.springframework.data.solr.repository.support.SimpleSolrRepository.deleteAll(SimpleSolrRepository.java:212)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
	at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
	at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService.deleteAll(KnoBaseDetailSolrService.java:313)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$FastClassBySpringCGLIB$$1d6fbb9b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$EnhancerBySpringCGLIB$$daa8b9a9.deleteAll(<generated>)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailService.syncToSolr(KnoBaseDetailService.java:497)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
	at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
	at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:940)
	at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:903)
	at org.springframework.data.solr.core.SolrTemplate.lambda$delete$6(SolrTemplate.java:249)
	at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:167)
	... 65 common frames omitted
Caused by: java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 82 common frames omitted
2025-08-16 14:20:52.977 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755325252977, dbSize=848}
2025-08-16 14:20:52.980 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755325252980}
2025-08-16 14:21:52.968 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755325312968, dbSize=848}
2025-08-16 14:21:52.969 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755325312969}
2025-08-16 14:22:52.977 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755325372977, dbSize=848}
2025-08-16 14:22:52.979 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755325372979}
2025-08-16 14:23:52.974 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755325432974, dbSize=848}
2025-08-16 14:23:52.979 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755325432979}
2025-08-16 14:24:52.979 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755325492979, dbSize=848}
2025-08-16 14:24:52.979 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755325492979}
2025-08-16 14:25:52.974 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755325552974, dbSize=848}
2025-08-16 14:25:52.974 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755325552974}
2025-08-16 14:26:52.972 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755325612971, dbSize=848}
2025-08-16 14:26:52.974 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755325612974}
2025-08-16 14:27:52.964 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755325672964, dbSize=848}
2025-08-16 14:27:52.964 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755325672964}
2025-08-16 14:28:52.974 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755325732974, dbSize=848}
2025-08-16 14:28:52.974 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755325732974}
2025-08-16 14:29:52.979 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755325792979, dbSize=848}
2025-08-16 14:29:52.980 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755325792980}
2025-08-16 14:30:52.977 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755325852977, dbSize=848}
2025-08-16 14:30:52.980 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755325852980}
2025-08-16 14:31:52.972 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755325912972, dbSize=848}
2025-08-16 14:31:52.974 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755325912974}
2025-08-16 14:32:52.965 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755325972965, dbSize=848}
2025-08-16 14:32:52.967 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755325972967}
2025-08-16 14:33:52.964 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755326032964, dbSize=848}
2025-08-16 14:33:52.965 [wordCompass-scheduling-3] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755326032965}
2025-08-16 14:34:52.963 [wordCompass-scheduling-1] INFO  o.j.modules.km.kno.service.KnoBaseDetailService:490 - 开始同步KnoBaseDetail数据到Solr...
2025-08-16 14:34:52.963 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755326092963, dbSize=848}
2025-08-16 14:34:52.965 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755326092965}
2025-08-16 14:34:52.973 [wordCompass-scheduling-1] DEBUG o.j.m.km.kno.mapper.KnoBaseDetailMapper.selectList:137 - ==>  Preparing: SELECT id, title, content, ocr_content, srcipt, srcipt_simplify, category_id, parent_id, knowledge_id, keywords, view_count, status, source_type, creator_name, updater_name, create_time, update_time, organ_name, organ_id, single_image_file_path, from_detail_page_num, from_detail_page_id, order_num FROM kno_base_detail WHERE (status <> ?)
2025-08-16 14:34:52.973 [wordCompass-scheduling-1] DEBUG o.j.m.km.kno.mapper.KnoBaseDetailMapper.selectList:137 - ==> Parameters: 0(Integer)
2025-08-16 14:34:52.977 [wordCompass-scheduling-1] DEBUG o.j.m.km.kno.mapper.KnoBaseDetailMapper.selectList:137 - <==      Total: 28
2025-08-16 14:34:52.981 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:34:52.982 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867036524546(String)
2025-08-16 14:34:52.982 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:34:52.986 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:34:52.986 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867103633409(String)
2025-08-16 14:34:52.986 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:34:52.990 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:34:52.991 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867103633410(String)
2025-08-16 14:34:52.991 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:34:52.994 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:34:52.994 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867103633411(String)
2025-08-16 14:34:52.996 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:34:52.999 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:34:52.999 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867170742274(String)
2025-08-16 14:34:53.000 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:34:53.003 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:34:53.004 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867170742275(String)
2025-08-16 14:34:53.004 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:34:53.008 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:34:53.008 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867170742276(String)
2025-08-16 14:34:53.008 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:34:53.011 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:34:53.011 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867237851138(String)
2025-08-16 14:34:53.011 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:34:53.015 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:34:53.015 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867246239745(String)
2025-08-16 14:34:53.016 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:34:53.019 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:34:53.019 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867246239746(String)
2025-08-16 14:34:53.021 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:34:53.024 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:34:53.025 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867246239747(String)
2025-08-16 14:34:53.025 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:34:53.029 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:34:53.030 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867309154305(String)
2025-08-16 14:34:53.030 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:34:53.034 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:34:53.034 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867309154306(String)
2025-08-16 14:34:53.036 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:34:53.041 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:34:53.041 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867309154307(String)
2025-08-16 14:34:53.043 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:34:53.051 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:34:53.051 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846081(String)
2025-08-16 14:34:53.053 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:34:53.058 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:34:53.058 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846082(String)
2025-08-16 14:34:53.059 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:34:53.063 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:34:53.064 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846083(String)
2025-08-16 14:34:53.065 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:34:53.069 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:34:53.069 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846084(String)
2025-08-16 14:34:53.070 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:34:53.075 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:34:53.075 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867460149249(String)
2025-08-16 14:34:53.076 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:34:53.080 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:34:53.080 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867460149250(String)
2025-08-16 14:34:53.081 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:34:53.086 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:34:53.086 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867460149251(String)
2025-08-16 14:34:53.087 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:34:53.092 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:34:53.092 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867535646721(String)
2025-08-16 14:34:53.093 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:34:53.097 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:34:53.098 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867535646722(String)
2025-08-16 14:34:53.098 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:34:53.103 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:34:53.103 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672451836186626(String)
2025-08-16 14:34:53.103 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:34:53.108 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:34:53.108 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672452326920193(String)
2025-08-16 14:34:53.109 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:34:53.125 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:34:53.125 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672452607938561(String)
2025-08-16 14:34:53.127 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:34:53.130 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:34:53.131 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672452817653761(String)
2025-08-16 14:34:53.131 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:34:53.136 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:34:53.136 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672453232889857(String)
2025-08-16 14:34:53.136 [wordCompass-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:34:53.139 [wordCompass-scheduling-1] ERROR o.j.modules.km.kno.service.KnoBaseDetailService:507 - 同步KnoBaseDetail数据到Solr失败
org.springframework.dao.DataAccessResourceFailureException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
	at org.springframework.data.solr.core.SolrExceptionTranslator.translateExceptionIfPossible(SolrExceptionTranslator.java:79)
	at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:169)
	at org.springframework.data.solr.core.SolrTemplate.delete(SolrTemplate.java:249)
	at org.springframework.data.solr.core.SolrOperations.delete(SolrOperations.java:228)
	at org.springframework.data.solr.repository.support.SimpleSolrRepository.deleteAll(SimpleSolrRepository.java:212)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
	at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
	at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService.deleteAll(KnoBaseDetailSolrService.java:313)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$FastClassBySpringCGLIB$$1d6fbb9b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$EnhancerBySpringCGLIB$$daa8b9a9.deleteAll(<generated>)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailService.syncToSolr(KnoBaseDetailService.java:497)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
	at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
	at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:940)
	at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:903)
	at org.springframework.data.solr.core.SolrTemplate.lambda$delete$6(SolrTemplate.java:249)
	at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:167)
	... 65 common frames omitted
Caused by: java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 82 common frames omitted
2025-08-16 14:35:52.966 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755326152966, dbSize=848}
2025-08-16 14:35:52.969 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755326152969}
2025-08-16 14:36:52.965 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755326212965, dbSize=848}
2025-08-16 14:36:52.967 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755326212967}
2025-08-16 14:37:52.965 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755326272965, dbSize=848}
2025-08-16 14:37:52.967 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755326272967}
2025-08-16 14:38:52.964 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755326332964, dbSize=848}
2025-08-16 14:38:52.968 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755326332967}
2025-08-16 14:39:52.967 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755326392967, dbSize=848}
2025-08-16 14:39:52.969 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755326392969}
2025-08-16 14:40:52.964 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755326452964, dbSize=848}
2025-08-16 14:40:52.967 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755326452967}
2025-08-16 14:41:52.965 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755326512965, dbSize=848}
2025-08-16 14:41:52.967 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755326512967}
2025-08-16 14:42:52.964 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755326572964, dbSize=848}
2025-08-16 14:42:52.965 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755326572965}
2025-08-16 14:43:52.964 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755326632964, dbSize=848}
2025-08-16 14:43:52.966 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755326632966}
2025-08-16 14:44:52.964 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755326692964, dbSize=848}
2025-08-16 14:44:52.967 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755326692967}
2025-08-16 14:45:52.964 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755326752964, dbSize=848}
2025-08-16 14:45:52.967 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755326752967}
2025-08-16 14:46:52.964 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755326812964, dbSize=848}
2025-08-16 14:46:52.967 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755326812967}
2025-08-16 14:47:52.964 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755326872964, dbSize=848}
2025-08-16 14:47:52.966 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755326872966}
2025-08-16 14:48:52.964 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755326932964, dbSize=848}
2025-08-16 14:48:52.967 [wordCompass-scheduling-7] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755326932967}
2025-08-16 14:49:52.962 [wordCompass-scheduling-4] INFO  o.j.modules.km.kno.service.KnoBaseDetailService:490 - 开始同步KnoBaseDetail数据到Solr...
2025-08-16 14:49:52.964 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755326992964, dbSize=848}
2025-08-16 14:49:52.966 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755326992966}
2025-08-16 14:49:52.982 [wordCompass-scheduling-4] DEBUG o.j.m.km.kno.mapper.KnoBaseDetailMapper.selectList:137 - ==>  Preparing: SELECT id, title, content, ocr_content, srcipt, srcipt_simplify, category_id, parent_id, knowledge_id, keywords, view_count, status, source_type, creator_name, updater_name, create_time, update_time, organ_name, organ_id, single_image_file_path, from_detail_page_num, from_detail_page_id, order_num FROM kno_base_detail WHERE (status <> ?)
2025-08-16 14:49:52.983 [wordCompass-scheduling-4] DEBUG o.j.m.km.kno.mapper.KnoBaseDetailMapper.selectList:137 - ==> Parameters: 0(Integer)
2025-08-16 14:49:52.991 [wordCompass-scheduling-4] DEBUG o.j.m.km.kno.mapper.KnoBaseDetailMapper.selectList:137 - <==      Total: 28
2025-08-16 14:49:53.001 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:49:53.001 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867036524546(String)
2025-08-16 14:49:53.002 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:49:53.010 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:49:53.010 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867103633409(String)
2025-08-16 14:49:53.011 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:49:53.017 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:49:53.018 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867103633410(String)
2025-08-16 14:49:53.018 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:49:53.024 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:49:53.024 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867103633411(String)
2025-08-16 14:49:53.025 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:49:53.033 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:49:53.033 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867170742274(String)
2025-08-16 14:49:53.033 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:49:53.038 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:49:53.038 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867170742275(String)
2025-08-16 14:49:53.038 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:49:53.042 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:49:53.042 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867170742276(String)
2025-08-16 14:49:53.043 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:49:53.048 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:49:53.048 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867237851138(String)
2025-08-16 14:49:53.049 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:49:53.055 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:49:53.055 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867246239745(String)
2025-08-16 14:49:53.056 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:49:53.060 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:49:53.060 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867246239746(String)
2025-08-16 14:49:53.060 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:49:53.065 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:49:53.065 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867246239747(String)
2025-08-16 14:49:53.066 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:49:53.069 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:49:53.070 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867309154305(String)
2025-08-16 14:49:53.070 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:49:53.074 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:49:53.074 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867309154306(String)
2025-08-16 14:49:53.075 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:49:53.080 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:49:53.080 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867309154307(String)
2025-08-16 14:49:53.081 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:49:53.084 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:49:53.084 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846081(String)
2025-08-16 14:49:53.085 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:49:53.090 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:49:53.091 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846082(String)
2025-08-16 14:49:53.091 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:49:53.096 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:49:53.096 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846083(String)
2025-08-16 14:49:53.097 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:49:53.102 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:49:53.102 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846084(String)
2025-08-16 14:49:53.102 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:49:53.108 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:49:53.108 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867460149249(String)
2025-08-16 14:49:53.109 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:49:53.115 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:49:53.115 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867460149250(String)
2025-08-16 14:49:53.116 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:49:53.120 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:49:53.120 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867460149251(String)
2025-08-16 14:49:53.120 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:49:53.124 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:49:53.124 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867535646721(String)
2025-08-16 14:49:53.125 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:49:53.129 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:49:53.129 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867535646722(String)
2025-08-16 14:49:53.130 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:49:53.134 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:49:53.135 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672451836186626(String)
2025-08-16 14:49:53.135 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:49:53.140 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:49:53.140 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672452326920193(String)
2025-08-16 14:49:53.140 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:49:53.144 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:49:53.144 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672452607938561(String)
2025-08-16 14:49:53.144 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:49:53.149 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:49:53.149 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672452817653761(String)
2025-08-16 14:49:53.149 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:49:53.152 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 14:49:53.152 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672453232889857(String)
2025-08-16 14:49:53.153 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 14:49:53.160 [wordCompass-scheduling-4] ERROR o.j.modules.km.kno.service.KnoBaseDetailService:507 - 同步KnoBaseDetail数据到Solr失败
org.springframework.dao.DataAccessResourceFailureException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
	at org.springframework.data.solr.core.SolrExceptionTranslator.translateExceptionIfPossible(SolrExceptionTranslator.java:79)
	at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:169)
	at org.springframework.data.solr.core.SolrTemplate.delete(SolrTemplate.java:249)
	at org.springframework.data.solr.core.SolrOperations.delete(SolrOperations.java:228)
	at org.springframework.data.solr.repository.support.SimpleSolrRepository.deleteAll(SimpleSolrRepository.java:212)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
	at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
	at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService.deleteAll(KnoBaseDetailSolrService.java:313)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$FastClassBySpringCGLIB$$1d6fbb9b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$EnhancerBySpringCGLIB$$daa8b9a9.deleteAll(<generated>)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailService.syncToSolr(KnoBaseDetailService.java:497)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
	at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
	at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:940)
	at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:903)
	at org.springframework.data.solr.core.SolrTemplate.lambda$delete$6(SolrTemplate.java:249)
	at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:167)
	... 65 common frames omitted
Caused by: java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 82 common frames omitted
2025-08-16 14:50:52.966 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755327052966, dbSize=848}
2025-08-16 14:50:52.967 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755327052967}
2025-08-16 14:51:52.964 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755327112964, dbSize=848}
2025-08-16 14:51:52.967 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755327112967}
2025-08-16 14:52:52.965 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755327172965, dbSize=848}
2025-08-16 14:52:52.966 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755327172966}
2025-08-16 14:53:52.964 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755327232964, dbSize=848}
2025-08-16 14:53:52.965 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755327232965}
2025-08-16 14:54:52.964 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755327292964, dbSize=848}
2025-08-16 14:54:52.966 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755327292966}
2025-08-16 14:55:52.964 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755327352964, dbSize=848}
2025-08-16 14:55:52.965 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755327352965}
2025-08-16 14:56:52.966 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755327412966, dbSize=848}
2025-08-16 14:56:52.970 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755327412970}
2025-08-16 14:57:52.964 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755327472964, dbSize=848}
2025-08-16 14:57:52.966 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755327472966}
2025-08-16 14:58:52.965 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755327532964, dbSize=848}
2025-08-16 14:58:52.967 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755327532967}
2025-08-16 14:59:52.964 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755327592964, dbSize=848}
2025-08-16 14:59:52.966 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755327592966}
2025-08-16 15:00:52.964 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755327652964, dbSize=848}
2025-08-16 15:00:52.966 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755327652966}
2025-08-16 15:01:52.964 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755327712964, dbSize=848}
2025-08-16 15:01:52.966 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755327712966}
2025-08-16 15:02:52.964 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755327772964, dbSize=848}
2025-08-16 15:02:52.965 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755327772965}
2025-08-16 15:03:52.965 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755327832965, dbSize=848}
2025-08-16 15:03:52.966 [wordCompass-scheduling-9] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755327832966}
2025-08-16 15:04:52.962 [wordCompass-scheduling-4] INFO  o.j.modules.km.kno.service.KnoBaseDetailService:490 - 开始同步KnoBaseDetail数据到Solr...
2025-08-16 15:04:52.963 [wordCompass-scheduling-8] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755327892963, dbSize=848}
2025-08-16 15:04:52.965 [wordCompass-scheduling-8] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755327892965}
2025-08-16 15:04:52.975 [wordCompass-scheduling-4] DEBUG o.j.m.km.kno.mapper.KnoBaseDetailMapper.selectList:137 - ==>  Preparing: SELECT id, title, content, ocr_content, srcipt, srcipt_simplify, category_id, parent_id, knowledge_id, keywords, view_count, status, source_type, creator_name, updater_name, create_time, update_time, organ_name, organ_id, single_image_file_path, from_detail_page_num, from_detail_page_id, order_num FROM kno_base_detail WHERE (status <> ?)
2025-08-16 15:04:52.975 [wordCompass-scheduling-4] DEBUG o.j.m.km.kno.mapper.KnoBaseDetailMapper.selectList:137 - ==> Parameters: 0(Integer)
2025-08-16 15:04:52.984 [wordCompass-scheduling-4] DEBUG o.j.m.km.kno.mapper.KnoBaseDetailMapper.selectList:137 - <==      Total: 28
2025-08-16 15:04:52.987 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:04:52.988 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867036524546(String)
2025-08-16 15:04:52.988 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:04:52.993 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:04:52.993 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867103633409(String)
2025-08-16 15:04:52.993 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:04:52.996 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:04:52.996 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867103633410(String)
2025-08-16 15:04:52.997 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:04:53.000 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:04:53.000 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867103633411(String)
2025-08-16 15:04:53.001 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:04:53.005 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:04:53.005 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867170742274(String)
2025-08-16 15:04:53.005 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:04:53.008 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:04:53.008 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867170742275(String)
2025-08-16 15:04:53.009 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:04:53.013 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:04:53.013 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867170742276(String)
2025-08-16 15:04:53.013 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:04:53.017 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:04:53.017 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867237851138(String)
2025-08-16 15:04:53.017 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:04:53.020 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:04:53.020 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867246239745(String)
2025-08-16 15:04:53.020 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:04:53.025 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:04:53.025 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867246239746(String)
2025-08-16 15:04:53.025 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:04:53.028 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:04:53.028 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867246239747(String)
2025-08-16 15:04:53.029 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:04:53.032 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:04:53.032 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867309154305(String)
2025-08-16 15:04:53.032 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:04:53.037 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:04:53.037 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867309154306(String)
2025-08-16 15:04:53.037 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:04:53.041 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:04:53.041 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867309154307(String)
2025-08-16 15:04:53.042 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:04:53.047 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:04:53.047 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846081(String)
2025-08-16 15:04:53.048 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:04:53.052 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:04:53.052 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846082(String)
2025-08-16 15:04:53.052 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:04:53.056 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:04:53.057 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846083(String)
2025-08-16 15:04:53.058 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:04:53.063 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:04:53.063 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846084(String)
2025-08-16 15:04:53.063 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:04:53.067 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:04:53.068 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867460149249(String)
2025-08-16 15:04:53.068 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:04:53.074 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:04:53.074 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867460149250(String)
2025-08-16 15:04:53.075 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:04:53.078 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:04:53.078 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867460149251(String)
2025-08-16 15:04:53.079 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:04:53.082 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:04:53.082 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867535646721(String)
2025-08-16 15:04:53.082 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:04:53.087 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:04:53.087 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867535646722(String)
2025-08-16 15:04:53.088 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:04:53.091 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:04:53.091 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672451836186626(String)
2025-08-16 15:04:53.092 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:04:53.096 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:04:53.096 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672452326920193(String)
2025-08-16 15:04:53.096 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:04:53.100 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:04:53.100 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672452607938561(String)
2025-08-16 15:04:53.101 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:04:53.104 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:04:53.104 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672452817653761(String)
2025-08-16 15:04:53.105 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:04:53.108 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:04:53.109 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672453232889857(String)
2025-08-16 15:04:53.109 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:04:53.125 [wordCompass-scheduling-4] ERROR o.j.modules.km.kno.service.KnoBaseDetailService:507 - 同步KnoBaseDetail数据到Solr失败
org.springframework.dao.DataAccessResourceFailureException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
	at org.springframework.data.solr.core.SolrExceptionTranslator.translateExceptionIfPossible(SolrExceptionTranslator.java:79)
	at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:169)
	at org.springframework.data.solr.core.SolrTemplate.delete(SolrTemplate.java:249)
	at org.springframework.data.solr.core.SolrOperations.delete(SolrOperations.java:228)
	at org.springframework.data.solr.repository.support.SimpleSolrRepository.deleteAll(SimpleSolrRepository.java:212)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
	at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
	at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService.deleteAll(KnoBaseDetailSolrService.java:313)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$FastClassBySpringCGLIB$$1d6fbb9b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$EnhancerBySpringCGLIB$$daa8b9a9.deleteAll(<generated>)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailService.syncToSolr(KnoBaseDetailService.java:497)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
	at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
	at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:940)
	at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:903)
	at org.springframework.data.solr.core.SolrTemplate.lambda$delete$6(SolrTemplate.java:249)
	at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:167)
	... 65 common frames omitted
Caused by: java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 82 common frames omitted
2025-08-16 15:05:52.964 [wordCompass-scheduling-8] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755327952964, dbSize=848}
2025-08-16 15:05:52.966 [wordCompass-scheduling-8] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755327952966}
2025-08-16 15:06:52.964 [wordCompass-scheduling-8] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755328012964, dbSize=848}
2025-08-16 15:06:52.966 [wordCompass-scheduling-8] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755328012966}
2025-08-16 15:07:52.964 [wordCompass-scheduling-8] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755328072964, dbSize=848}
2025-08-16 15:07:52.965 [wordCompass-scheduling-8] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755328072964}
2025-08-16 15:08:52.964 [wordCompass-scheduling-8] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755328132964, dbSize=848}
2025-08-16 15:08:52.966 [wordCompass-scheduling-8] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755328132966}
2025-08-16 15:09:52.964 [wordCompass-scheduling-8] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755328192964, dbSize=848}
2025-08-16 15:09:52.965 [wordCompass-scheduling-8] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755328192965}
2025-08-16 15:10:52.964 [wordCompass-scheduling-8] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755328252964, dbSize=848}
2025-08-16 15:10:52.965 [wordCompass-scheduling-8] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755328252965}
2025-08-16 15:11:52.964 [wordCompass-scheduling-8] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755328312964, dbSize=848}
2025-08-16 15:11:52.966 [wordCompass-scheduling-8] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755328312966}
2025-08-16 15:12:52.964 [wordCompass-scheduling-8] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755328372964, dbSize=848}
2025-08-16 15:12:52.965 [wordCompass-scheduling-8] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755328372965}
2025-08-16 15:13:52.964 [wordCompass-scheduling-8] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755328432964, dbSize=848}
2025-08-16 15:13:52.965 [wordCompass-scheduling-8] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755328432965}
2025-08-16 15:14:52.963 [wordCompass-scheduling-8] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755328492963, dbSize=848}
2025-08-16 15:14:52.966 [wordCompass-scheduling-8] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755328492966}
2025-08-16 15:15:52.964 [wordCompass-scheduling-8] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755328552964, dbSize=848}
2025-08-16 15:15:52.965 [wordCompass-scheduling-8] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755328552965}
2025-08-16 15:16:52.963 [wordCompass-scheduling-8] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755328612963, dbSize=848}
2025-08-16 15:16:52.966 [wordCompass-scheduling-8] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755328612966}
2025-08-16 15:17:52.964 [wordCompass-scheduling-8] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755328672964, dbSize=848}
2025-08-16 15:17:52.966 [wordCompass-scheduling-8] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755328672966}
2025-08-16 15:18:52.964 [wordCompass-scheduling-8] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755328732964, dbSize=848}
2025-08-16 15:18:52.966 [wordCompass-scheduling-8] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755328732966}
2025-08-16 15:19:52.963 [wordCompass-scheduling-4] INFO  o.j.modules.km.kno.service.KnoBaseDetailService:490 - 开始同步KnoBaseDetail数据到Solr...
2025-08-16 15:19:52.964 [wordCompass-scheduling-10] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755328792964, dbSize=848}
2025-08-16 15:19:52.967 [wordCompass-scheduling-10] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755328792967}
2025-08-16 15:19:52.977 [wordCompass-scheduling-4] DEBUG o.j.m.km.kno.mapper.KnoBaseDetailMapper.selectList:137 - ==>  Preparing: SELECT id, title, content, ocr_content, srcipt, srcipt_simplify, category_id, parent_id, knowledge_id, keywords, view_count, status, source_type, creator_name, updater_name, create_time, update_time, organ_name, organ_id, single_image_file_path, from_detail_page_num, from_detail_page_id, order_num FROM kno_base_detail WHERE (status <> ?)
2025-08-16 15:19:52.978 [wordCompass-scheduling-4] DEBUG o.j.m.km.kno.mapper.KnoBaseDetailMapper.selectList:137 - ==> Parameters: 0(Integer)
2025-08-16 15:19:52.985 [wordCompass-scheduling-4] DEBUG o.j.m.km.kno.mapper.KnoBaseDetailMapper.selectList:137 - <==      Total: 28
2025-08-16 15:19:52.991 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:19:52.992 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867036524546(String)
2025-08-16 15:19:52.992 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:19:52.998 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:19:52.998 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867103633409(String)
2025-08-16 15:19:52.998 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:19:53.005 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:19:53.005 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867103633410(String)
2025-08-16 15:19:53.005 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:19:53.010 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:19:53.010 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867103633411(String)
2025-08-16 15:19:53.011 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:19:53.017 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:19:53.017 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867170742274(String)
2025-08-16 15:19:53.018 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:19:53.022 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:19:53.022 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867170742275(String)
2025-08-16 15:19:53.024 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:19:53.028 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:19:53.028 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867170742276(String)
2025-08-16 15:19:53.028 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:19:53.034 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:19:53.034 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867237851138(String)
2025-08-16 15:19:53.035 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:19:53.042 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:19:53.042 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867246239745(String)
2025-08-16 15:19:53.043 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:19:53.050 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:19:53.050 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867246239746(String)
2025-08-16 15:19:53.051 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:19:53.058 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:19:53.058 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867246239747(String)
2025-08-16 15:19:53.059 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:19:53.064 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:19:53.064 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867309154305(String)
2025-08-16 15:19:53.065 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:19:53.073 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:19:53.074 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867309154306(String)
2025-08-16 15:19:53.075 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:19:53.081 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:19:53.081 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867309154307(String)
2025-08-16 15:19:53.081 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:19:53.087 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:19:53.087 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846081(String)
2025-08-16 15:19:53.087 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:19:53.096 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:19:53.096 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846082(String)
2025-08-16 15:19:53.097 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:19:53.103 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:19:53.103 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846083(String)
2025-08-16 15:19:53.103 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:19:53.108 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:19:53.108 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846084(String)
2025-08-16 15:19:53.109 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:19:53.115 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:19:53.115 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867460149249(String)
2025-08-16 15:19:53.115 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:19:53.119 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:19:53.120 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867460149250(String)
2025-08-16 15:19:53.120 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:19:53.126 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:19:53.126 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867460149251(String)
2025-08-16 15:19:53.126 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:19:53.129 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:19:53.129 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867535646721(String)
2025-08-16 15:19:53.130 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:19:53.134 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:19:53.134 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867535646722(String)
2025-08-16 15:19:53.134 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:19:53.138 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:19:53.138 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672451836186626(String)
2025-08-16 15:19:53.139 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:19:53.147 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:19:53.147 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672452326920193(String)
2025-08-16 15:19:53.148 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:19:53.152 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:19:53.152 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672452607938561(String)
2025-08-16 15:19:53.153 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:19:53.156 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:19:53.156 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672452817653761(String)
2025-08-16 15:19:53.157 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:19:53.161 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:19:53.161 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672453232889857(String)
2025-08-16 15:19:53.161 [wordCompass-scheduling-4] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:19:53.167 [wordCompass-scheduling-4] ERROR o.j.modules.km.kno.service.KnoBaseDetailService:507 - 同步KnoBaseDetail数据到Solr失败
org.springframework.dao.DataAccessResourceFailureException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
	at org.springframework.data.solr.core.SolrExceptionTranslator.translateExceptionIfPossible(SolrExceptionTranslator.java:79)
	at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:169)
	at org.springframework.data.solr.core.SolrTemplate.delete(SolrTemplate.java:249)
	at org.springframework.data.solr.core.SolrOperations.delete(SolrOperations.java:228)
	at org.springframework.data.solr.repository.support.SimpleSolrRepository.deleteAll(SimpleSolrRepository.java:212)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
	at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
	at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService.deleteAll(KnoBaseDetailSolrService.java:313)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$FastClassBySpringCGLIB$$1d6fbb9b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$EnhancerBySpringCGLIB$$daa8b9a9.deleteAll(<generated>)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailService.syncToSolr(KnoBaseDetailService.java:497)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
	at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
	at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:940)
	at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:903)
	at org.springframework.data.solr.core.SolrTemplate.lambda$delete$6(SolrTemplate.java:249)
	at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:167)
	... 65 common frames omitted
Caused by: java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 82 common frames omitted
2025-08-16 15:20:52.964 [wordCompass-scheduling-10] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755328852964, dbSize=848}
2025-08-16 15:20:52.966 [wordCompass-scheduling-10] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755328852966}
2025-08-16 15:21:52.964 [wordCompass-scheduling-10] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755328912964, dbSize=848}
2025-08-16 15:21:52.966 [wordCompass-scheduling-10] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755328912966}
2025-08-16 15:22:52.964 [wordCompass-scheduling-10] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755328972964, dbSize=848}
2025-08-16 15:22:52.966 [wordCompass-scheduling-10] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755328972966}
2025-08-16 15:23:52.964 [wordCompass-scheduling-10] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755329032964, dbSize=848}
2025-08-16 15:23:52.966 [wordCompass-scheduling-10] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755329032966}
2025-08-16 15:24:52.966 [wordCompass-scheduling-10] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755329092966, dbSize=848}
2025-08-16 15:24:52.969 [wordCompass-scheduling-10] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755329092969}
2025-08-16 15:25:52.964 [wordCompass-scheduling-10] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755329152964, dbSize=848}
2025-08-16 15:25:52.967 [wordCompass-scheduling-10] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755329152967}
2025-08-16 15:26:52.965 [wordCompass-scheduling-10] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755329212965, dbSize=848}
2025-08-16 15:26:52.967 [wordCompass-scheduling-10] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755329212967}
2025-08-16 15:27:52.964 [wordCompass-scheduling-10] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755329272964, dbSize=848}
2025-08-16 15:27:52.966 [wordCompass-scheduling-10] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755329272966}
2025-08-16 15:28:52.964 [wordCompass-scheduling-10] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755329332964, dbSize=848}
2025-08-16 15:28:52.966 [wordCompass-scheduling-10] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755329332966}
2025-08-16 15:29:52.965 [wordCompass-scheduling-10] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755329392965, dbSize=848}
2025-08-16 15:29:52.967 [wordCompass-scheduling-10] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755329392967}
2025-08-16 15:30:52.966 [wordCompass-scheduling-10] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755329452966, dbSize=848}
2025-08-16 15:30:52.967 [wordCompass-scheduling-10] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755329452967}
2025-08-16 15:31:52.964 [wordCompass-scheduling-10] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755329512964, dbSize=848}
2025-08-16 15:31:52.966 [wordCompass-scheduling-10] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755329512966}
2025-08-16 15:32:52.964 [wordCompass-scheduling-10] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755329572964, dbSize=848}
2025-08-16 15:32:52.965 [wordCompass-scheduling-10] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755329572965}
2025-08-16 15:33:52.964 [wordCompass-scheduling-10] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755329632963, dbSize=848}
2025-08-16 15:33:52.965 [wordCompass-scheduling-10] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755329632965}
2025-08-16 15:34:52.963 [wordCompass-scheduling-9] INFO  o.j.modules.km.kno.service.KnoBaseDetailService:490 - 开始同步KnoBaseDetail数据到Solr...
2025-08-16 15:34:52.964 [wordCompass-scheduling-4] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755329692964, dbSize=848}
2025-08-16 15:34:52.966 [wordCompass-scheduling-4] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755329692966}
2025-08-16 15:34:52.974 [wordCompass-scheduling-9] DEBUG o.j.m.km.kno.mapper.KnoBaseDetailMapper.selectList:137 - ==>  Preparing: SELECT id, title, content, ocr_content, srcipt, srcipt_simplify, category_id, parent_id, knowledge_id, keywords, view_count, status, source_type, creator_name, updater_name, create_time, update_time, organ_name, organ_id, single_image_file_path, from_detail_page_num, from_detail_page_id, order_num FROM kno_base_detail WHERE (status <> ?)
2025-08-16 15:34:52.974 [wordCompass-scheduling-9] DEBUG o.j.m.km.kno.mapper.KnoBaseDetailMapper.selectList:137 - ==> Parameters: 0(Integer)
2025-08-16 15:34:52.980 [wordCompass-scheduling-9] DEBUG o.j.m.km.kno.mapper.KnoBaseDetailMapper.selectList:137 - <==      Total: 28
2025-08-16 15:34:52.984 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:34:52.984 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867036524546(String)
2025-08-16 15:34:52.984 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:34:52.988 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:34:52.988 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867103633409(String)
2025-08-16 15:34:52.989 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:34:52.992 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:34:52.992 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867103633410(String)
2025-08-16 15:34:52.992 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:34:52.996 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:34:52.996 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867103633411(String)
2025-08-16 15:34:52.997 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:34:53.000 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:34:53.000 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867170742274(String)
2025-08-16 15:34:53.001 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:34:53.005 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:34:53.005 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867170742275(String)
2025-08-16 15:34:53.006 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:34:53.008 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:34:53.009 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867170742276(String)
2025-08-16 15:34:53.009 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:34:53.013 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:34:53.013 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867237851138(String)
2025-08-16 15:34:53.014 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:34:53.017 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:34:53.017 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867246239745(String)
2025-08-16 15:34:53.018 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:34:53.022 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:34:53.022 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867246239746(String)
2025-08-16 15:34:53.022 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:34:53.025 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:34:53.025 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867246239747(String)
2025-08-16 15:34:53.027 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:34:53.030 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:34:53.030 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867309154305(String)
2025-08-16 15:34:53.030 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:34:53.034 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:34:53.034 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867309154306(String)
2025-08-16 15:34:53.034 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:34:53.037 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:34:53.037 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867309154307(String)
2025-08-16 15:34:53.038 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:34:53.041 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:34:53.042 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846081(String)
2025-08-16 15:34:53.042 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:34:53.045 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:34:53.046 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846082(String)
2025-08-16 15:34:53.046 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:34:53.049 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:34:53.049 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846083(String)
2025-08-16 15:34:53.050 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:34:53.053 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:34:53.053 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846084(String)
2025-08-16 15:34:53.054 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:34:53.057 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:34:53.057 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867460149249(String)
2025-08-16 15:34:53.057 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:34:53.061 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:34:53.062 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867460149250(String)
2025-08-16 15:34:53.062 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:34:53.066 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:34:53.066 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867460149251(String)
2025-08-16 15:34:53.066 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:34:53.069 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:34:53.069 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867535646721(String)
2025-08-16 15:34:53.070 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:34:53.077 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:34:53.077 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867535646722(String)
2025-08-16 15:34:53.078 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:34:53.082 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:34:53.082 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672451836186626(String)
2025-08-16 15:34:53.083 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:34:53.085 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:34:53.086 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672452326920193(String)
2025-08-16 15:34:53.086 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:34:53.090 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:34:53.090 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672452607938561(String)
2025-08-16 15:34:53.091 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:34:53.094 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:34:53.094 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672452817653761(String)
2025-08-16 15:34:53.094 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:34:53.098 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:34:53.098 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672453232889857(String)
2025-08-16 15:34:53.099 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:34:53.113 [wordCompass-scheduling-9] ERROR o.j.modules.km.kno.service.KnoBaseDetailService:507 - 同步KnoBaseDetail数据到Solr失败
org.springframework.dao.DataAccessResourceFailureException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
	at org.springframework.data.solr.core.SolrExceptionTranslator.translateExceptionIfPossible(SolrExceptionTranslator.java:79)
	at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:169)
	at org.springframework.data.solr.core.SolrTemplate.delete(SolrTemplate.java:249)
	at org.springframework.data.solr.core.SolrOperations.delete(SolrOperations.java:228)
	at org.springframework.data.solr.repository.support.SimpleSolrRepository.deleteAll(SimpleSolrRepository.java:212)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
	at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
	at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService.deleteAll(KnoBaseDetailSolrService.java:313)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$FastClassBySpringCGLIB$$1d6fbb9b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$EnhancerBySpringCGLIB$$daa8b9a9.deleteAll(<generated>)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailService.syncToSolr(KnoBaseDetailService.java:497)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
	at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
	at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:940)
	at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:903)
	at org.springframework.data.solr.core.SolrTemplate.lambda$delete$6(SolrTemplate.java:249)
	at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:167)
	... 65 common frames omitted
Caused by: java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 82 common frames omitted
2025-08-16 15:35:52.964 [wordCompass-scheduling-4] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755329752964, dbSize=848}
2025-08-16 15:35:52.967 [wordCompass-scheduling-4] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755329752967}
2025-08-16 15:36:52.964 [wordCompass-scheduling-4] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755329812964, dbSize=848}
2025-08-16 15:36:52.967 [wordCompass-scheduling-4] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755329812967}
2025-08-16 15:37:52.964 [wordCompass-scheduling-4] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755329872964, dbSize=848}
2025-08-16 15:37:52.966 [wordCompass-scheduling-4] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755329872966}
2025-08-16 15:38:52.965 [wordCompass-scheduling-4] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755329932965, dbSize=848}
2025-08-16 15:38:52.966 [wordCompass-scheduling-4] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755329932966}
2025-08-16 15:39:52.972 [wordCompass-scheduling-4] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755329992972, dbSize=848}
2025-08-16 15:39:52.975 [wordCompass-scheduling-4] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755329992975}
2025-08-16 15:40:52.965 [wordCompass-scheduling-4] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755330052964, dbSize=848}
2025-08-16 15:40:52.967 [wordCompass-scheduling-4] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755330052967}
2025-08-16 15:41:52.965 [wordCompass-scheduling-4] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755330112965, dbSize=848}
2025-08-16 15:41:52.967 [wordCompass-scheduling-4] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755330112967}
2025-08-16 15:42:52.967 [wordCompass-scheduling-4] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755330172967, dbSize=848}
2025-08-16 15:42:52.970 [wordCompass-scheduling-4] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755330172970}
2025-08-16 15:43:52.964 [wordCompass-scheduling-4] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755330232964, dbSize=848}
2025-08-16 15:43:52.965 [wordCompass-scheduling-4] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755330232965}
2025-08-16 15:44:52.964 [wordCompass-scheduling-4] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755330292964, dbSize=848}
2025-08-16 15:44:52.965 [wordCompass-scheduling-4] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755330292965}
2025-08-16 15:45:52.964 [wordCompass-scheduling-4] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755330352964, dbSize=848}
2025-08-16 15:45:52.965 [wordCompass-scheduling-4] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755330352965}
2025-08-16 15:46:52.964 [wordCompass-scheduling-4] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755330412964, dbSize=848}
2025-08-16 15:46:52.965 [wordCompass-scheduling-4] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755330412965}
2025-08-16 15:47:52.964 [wordCompass-scheduling-4] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755330472964, dbSize=848}
2025-08-16 15:47:52.965 [wordCompass-scheduling-4] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755330472965}
2025-08-16 15:48:52.964 [wordCompass-scheduling-4] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755330532964, dbSize=848}
2025-08-16 15:48:52.965 [wordCompass-scheduling-4] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755330532965}
2025-08-16 15:49:52.962 [wordCompass-scheduling-9] INFO  o.j.modules.km.kno.service.KnoBaseDetailService:490 - 开始同步KnoBaseDetail数据到Solr...
2025-08-16 15:49:52.964 [wordCompass-scheduling-1] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755330592964, dbSize=848}
2025-08-16 15:49:52.965 [wordCompass-scheduling-1] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755330592965}
2025-08-16 15:49:52.973 [wordCompass-scheduling-9] DEBUG o.j.m.km.kno.mapper.KnoBaseDetailMapper.selectList:137 - ==>  Preparing: SELECT id, title, content, ocr_content, srcipt, srcipt_simplify, category_id, parent_id, knowledge_id, keywords, view_count, status, source_type, creator_name, updater_name, create_time, update_time, organ_name, organ_id, single_image_file_path, from_detail_page_num, from_detail_page_id, order_num FROM kno_base_detail WHERE (status <> ?)
2025-08-16 15:49:52.974 [wordCompass-scheduling-9] DEBUG o.j.m.km.kno.mapper.KnoBaseDetailMapper.selectList:137 - ==> Parameters: 0(Integer)
2025-08-16 15:49:52.979 [wordCompass-scheduling-9] DEBUG o.j.m.km.kno.mapper.KnoBaseDetailMapper.selectList:137 - <==      Total: 28
2025-08-16 15:49:52.984 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:49:52.985 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867036524546(String)
2025-08-16 15:49:52.986 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:49:52.989 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:49:52.990 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867103633409(String)
2025-08-16 15:49:52.990 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:49:52.994 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:49:52.994 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867103633410(String)
2025-08-16 15:49:52.996 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:49:52.999 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:49:52.999 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867103633411(String)
2025-08-16 15:49:53.000 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:49:53.006 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:49:53.006 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867170742274(String)
2025-08-16 15:49:53.006 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:49:53.010 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:49:53.010 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867170742275(String)
2025-08-16 15:49:53.011 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:49:53.014 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:49:53.014 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867170742276(String)
2025-08-16 15:49:53.014 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:49:53.019 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:49:53.019 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867237851138(String)
2025-08-16 15:49:53.019 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:49:53.022 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:49:53.022 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867246239745(String)
2025-08-16 15:49:53.024 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:49:53.027 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:49:53.027 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867246239746(String)
2025-08-16 15:49:53.028 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:49:53.032 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:49:53.032 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867246239747(String)
2025-08-16 15:49:53.032 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:49:53.036 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:49:53.036 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867309154305(String)
2025-08-16 15:49:53.037 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:49:53.040 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:49:53.040 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867309154306(String)
2025-08-16 15:49:53.040 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:49:53.044 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:49:53.044 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867309154307(String)
2025-08-16 15:49:53.044 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:49:53.048 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:49:53.048 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846081(String)
2025-08-16 15:49:53.049 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:49:53.053 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:49:53.053 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846082(String)
2025-08-16 15:49:53.054 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:49:53.057 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:49:53.057 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846083(String)
2025-08-16 15:49:53.058 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:49:53.061 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:49:53.062 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846084(String)
2025-08-16 15:49:53.062 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:49:53.066 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:49:53.066 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867460149249(String)
2025-08-16 15:49:53.067 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:49:53.069 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:49:53.070 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867460149250(String)
2025-08-16 15:49:53.071 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:49:53.074 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:49:53.074 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867460149251(String)
2025-08-16 15:49:53.074 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:49:53.078 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:49:53.078 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867535646721(String)
2025-08-16 15:49:53.078 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:49:53.082 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:49:53.082 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867535646722(String)
2025-08-16 15:49:53.082 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:49:53.086 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:49:53.086 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672451836186626(String)
2025-08-16 15:49:53.087 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:49:53.090 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:49:53.090 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672452326920193(String)
2025-08-16 15:49:53.090 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:49:53.094 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:49:53.094 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672452607938561(String)
2025-08-16 15:49:53.094 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:49:53.099 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:49:53.099 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672452817653761(String)
2025-08-16 15:49:53.100 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:49:53.104 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 15:49:53.104 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672453232889857(String)
2025-08-16 15:49:53.105 [wordCompass-scheduling-9] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 15:49:53.119 [wordCompass-scheduling-9] ERROR o.j.modules.km.kno.service.KnoBaseDetailService:507 - 同步KnoBaseDetail数据到Solr失败
org.springframework.dao.DataAccessResourceFailureException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
	at org.springframework.data.solr.core.SolrExceptionTranslator.translateExceptionIfPossible(SolrExceptionTranslator.java:79)
	at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:169)
	at org.springframework.data.solr.core.SolrTemplate.delete(SolrTemplate.java:249)
	at org.springframework.data.solr.core.SolrOperations.delete(SolrOperations.java:228)
	at org.springframework.data.solr.repository.support.SimpleSolrRepository.deleteAll(SimpleSolrRepository.java:212)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
	at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
	at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService.deleteAll(KnoBaseDetailSolrService.java:313)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$FastClassBySpringCGLIB$$1d6fbb9b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$EnhancerBySpringCGLIB$$daa8b9a9.deleteAll(<generated>)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailService.syncToSolr(KnoBaseDetailService.java:497)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
	at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
	at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:940)
	at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:903)
	at org.springframework.data.solr.core.SolrTemplate.lambda$delete$6(SolrTemplate.java:249)
	at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:167)
	... 65 common frames omitted
Caused by: java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 82 common frames omitted
2025-08-16 15:50:52.964 [wordCompass-scheduling-1] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755330652964, dbSize=848}
2025-08-16 15:50:52.965 [wordCompass-scheduling-1] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755330652965}
2025-08-16 15:51:52.964 [wordCompass-scheduling-1] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755330712964, dbSize=848}
2025-08-16 15:51:52.969 [wordCompass-scheduling-1] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755330712969}
2025-08-16 15:52:52.972 [wordCompass-scheduling-1] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755330772972, dbSize=848}
2025-08-16 15:52:52.976 [wordCompass-scheduling-1] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755330772976}
2025-08-16 15:53:52.969 [wordCompass-scheduling-1] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755330832969, dbSize=848}
2025-08-16 15:53:52.970 [wordCompass-scheduling-1] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755330832970}
2025-08-16 15:54:52.974 [wordCompass-scheduling-1] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755330892974, dbSize=848}
2025-08-16 15:54:52.974 [wordCompass-scheduling-1] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755330892974}
2025-08-16 15:55:52.965 [wordCompass-scheduling-1] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755330952965, dbSize=848}
2025-08-16 15:55:52.967 [wordCompass-scheduling-1] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755330952967}
2025-08-16 15:56:52.971 [wordCompass-scheduling-1] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755331012971, dbSize=848}
2025-08-16 15:56:52.972 [wordCompass-scheduling-1] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755331012972}
2025-08-16 15:57:52.976 [wordCompass-scheduling-1] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755331072976, dbSize=848}
2025-08-16 15:57:52.978 [wordCompass-scheduling-1] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755331072978}
2025-08-16 15:58:52.972 [wordCompass-scheduling-1] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755331132972, dbSize=848}
2025-08-16 15:58:52.975 [wordCompass-scheduling-1] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755331132975}
2025-08-16 15:59:52.970 [wordCompass-scheduling-1] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755331192970, dbSize=848}
2025-08-16 15:59:52.970 [wordCompass-scheduling-1] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755331192970}
2025-08-16 16:00:52.973 [wordCompass-scheduling-1] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755331252973, dbSize=848}
2025-08-16 16:00:52.977 [wordCompass-scheduling-1] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755331252977}
2025-08-16 16:01:52.963 [wordCompass-scheduling-1] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755331312963, dbSize=848}
2025-08-16 16:01:52.963 [wordCompass-scheduling-1] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755331312963}
2025-08-16 16:02:52.975 [wordCompass-scheduling-1] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755331372975, dbSize=848}
2025-08-16 16:02:52.977 [wordCompass-scheduling-1] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755331372977}
2025-08-16 16:03:52.974 [wordCompass-scheduling-1] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755331432974, dbSize=848}
2025-08-16 16:03:52.974 [wordCompass-scheduling-1] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755331432974}
2025-08-16 16:04:52.967 [wordCompass-scheduling-8] INFO  o.j.modules.km.kno.service.KnoBaseDetailService:490 - 开始同步KnoBaseDetail数据到Solr...
2025-08-16 16:04:52.967 [wordCompass-scheduling-6] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755331492967, dbSize=848}
2025-08-16 16:04:52.967 [wordCompass-scheduling-6] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755331492967}
2025-08-16 16:04:52.980 [wordCompass-scheduling-8] DEBUG o.j.m.km.kno.mapper.KnoBaseDetailMapper.selectList:137 - ==>  Preparing: SELECT id, title, content, ocr_content, srcipt, srcipt_simplify, category_id, parent_id, knowledge_id, keywords, view_count, status, source_type, creator_name, updater_name, create_time, update_time, organ_name, organ_id, single_image_file_path, from_detail_page_num, from_detail_page_id, order_num FROM kno_base_detail WHERE (status <> ?)
2025-08-16 16:04:52.980 [wordCompass-scheduling-8] DEBUG o.j.m.km.kno.mapper.KnoBaseDetailMapper.selectList:137 - ==> Parameters: 0(Integer)
2025-08-16 16:04:52.986 [wordCompass-scheduling-8] DEBUG o.j.m.km.kno.mapper.KnoBaseDetailMapper.selectList:137 - <==      Total: 28
2025-08-16 16:04:52.993 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 16:04:52.994 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867036524546(String)
2025-08-16 16:04:52.994 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 16:04:52.997 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 16:04:52.997 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867103633409(String)
2025-08-16 16:04:52.997 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 16:04:53.003 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 16:04:53.003 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867103633410(String)
2025-08-16 16:04:53.007 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 16:04:53.008 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 16:04:53.008 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867103633411(String)
2025-08-16 16:04:53.013 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 16:04:53.017 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 16:04:53.017 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867170742274(String)
2025-08-16 16:04:53.017 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 16:04:53.024 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 16:04:53.024 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867170742275(String)
2025-08-16 16:04:53.024 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 16:04:53.029 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 16:04:53.029 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867170742276(String)
2025-08-16 16:04:53.029 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 16:04:53.038 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 16:04:53.038 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867237851138(String)
2025-08-16 16:04:53.038 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 16:04:53.043 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 16:04:53.043 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867246239745(String)
2025-08-16 16:04:53.044 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 16:04:53.049 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 16:04:53.049 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867246239746(String)
2025-08-16 16:04:53.050 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 16:04:53.053 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 16:04:53.053 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867246239747(String)
2025-08-16 16:04:53.053 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 16:04:53.059 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 16:04:53.059 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867309154305(String)
2025-08-16 16:04:53.060 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 16:04:53.063 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 16:04:53.063 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867309154306(String)
2025-08-16 16:04:53.067 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 16:04:53.070 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 16:04:53.070 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867309154307(String)
2025-08-16 16:04:53.070 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 16:04:53.074 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 16:04:53.074 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846081(String)
2025-08-16 16:04:53.074 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 16:04:53.081 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 16:04:53.081 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846082(String)
2025-08-16 16:04:53.082 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 16:04:53.087 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 16:04:53.088 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846083(String)
2025-08-16 16:04:53.088 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 16:04:53.090 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 16:04:53.090 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846084(String)
2025-08-16 16:04:53.090 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 16:04:53.099 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 16:04:53.099 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867460149249(String)
2025-08-16 16:04:53.100 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 16:04:53.103 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 16:04:53.104 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867460149250(String)
2025-08-16 16:04:53.104 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 16:04:53.110 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 16:04:53.110 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867460149251(String)
2025-08-16 16:04:53.110 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 16:04:53.114 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 16:04:53.114 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867535646721(String)
2025-08-16 16:04:53.114 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 16:04:53.118 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 16:04:53.118 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867535646722(String)
2025-08-16 16:04:53.118 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 16:04:53.121 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 16:04:53.124 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672451836186626(String)
2025-08-16 16:04:53.124 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 16:04:53.128 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 16:04:53.128 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672452326920193(String)
2025-08-16 16:04:53.129 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 16:04:53.132 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 16:04:53.132 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672452607938561(String)
2025-08-16 16:04:53.132 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 16:04:53.136 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 16:04:53.136 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672452817653761(String)
2025-08-16 16:04:53.136 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 16:04:53.142 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-16 16:04:53.142 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672453232889857(String)
2025-08-16 16:04:53.142 [wordCompass-scheduling-8] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-16 16:04:53.146 [wordCompass-scheduling-8] ERROR o.j.modules.km.kno.service.KnoBaseDetailService:507 - 同步KnoBaseDetail数据到Solr失败
org.springframework.dao.DataAccessResourceFailureException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
	at org.springframework.data.solr.core.SolrExceptionTranslator.translateExceptionIfPossible(SolrExceptionTranslator.java:79)
	at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:169)
	at org.springframework.data.solr.core.SolrTemplate.delete(SolrTemplate.java:249)
	at org.springframework.data.solr.core.SolrOperations.delete(SolrOperations.java:228)
	at org.springframework.data.solr.repository.support.SimpleSolrRepository.deleteAll(SimpleSolrRepository.java:212)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
	at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
	at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService.deleteAll(KnoBaseDetailSolrService.java:313)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$FastClassBySpringCGLIB$$1d6fbb9b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$EnhancerBySpringCGLIB$$daa8b9a9.deleteAll(<generated>)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailService.syncToSolr(KnoBaseDetailService.java:497)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
	at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
	at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:940)
	at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:903)
	at org.springframework.data.solr.core.SolrTemplate.lambda$delete$6(SolrTemplate.java:249)
	at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:167)
	... 65 common frames omitted
Caused by: java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 82 common frames omitted
2025-08-16 16:05:52.973 [wordCompass-scheduling-6] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755331552973, dbSize=848}
2025-08-16 16:05:52.974 [wordCompass-scheduling-6] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755331552974}
2025-08-16 16:06:52.971 [wordCompass-scheduling-6] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755331612971, dbSize=848}
2025-08-16 16:06:52.972 [wordCompass-scheduling-6] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755331612972}
2025-08-16 16:07:32.017 [http-nio-8080-exec-4] DEBUG org.jeecg.common.aspect.DictAspect:65 - 获取JSON数据 耗时：155ms
2025-08-16 16:07:32.019 [http-nio-8080-exec-4] DEBUG org.jeecg.common.aspect.DictAspect:69 - 注入字典到JSON数据  耗时0ms
2025-08-16 16:07:33.286 [http-nio-8080-exec-10] DEBUG org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-08-16 16:07:33.335 [http-nio-8080-exec-6] DEBUG org.jeecg.common.aspect.DictAspect:65 - 获取JSON数据 耗时：45ms
2025-08-16 16:07:33.336 [http-nio-8080-exec-6] DEBUG org.jeecg.common.aspect.DictAspect:69 - 注入字典到JSON数据  耗时0ms
2025-08-16 16:07:42.758 [http-nio-8080-exec-7] DEBUG org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-08-16 16:07:44.948 [http-nio-8080-exec-8] DEBUG org.jeecg.common.aspect.DictAspect:65 - 获取JSON数据 耗时：127ms
2025-08-16 16:07:44.948 [http-nio-8080-exec-8] DEBUG org.jeecg.common.aspect.DictAspect:69 - 注入字典到JSON数据  耗时0ms
2025-08-16 16:07:46.346 [http-nio-8080-exec-9] DEBUG org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-08-16 16:07:46.397 [http-nio-8080-exec-5] DEBUG org.jeecg.common.aspect.DictAspect:65 - 获取JSON数据 耗时：47ms
2025-08-16 16:07:46.398 [http-nio-8080-exec-5] DEBUG org.jeecg.common.aspect.DictAspect:69 - 注入字典到JSON数据  耗时0ms
2025-08-16 16:07:52.974 [wordCompass-scheduling-6] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755331672974, dbSize=848}
2025-08-16 16:07:52.974 [wordCompass-scheduling-6] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755331672974}
2025-08-16 16:08:41.736 [http-nio-8080-exec-1] DEBUG org.jeecg.modules.message.websocket.WebSocket:111 - 【系统 WebSocket】收到客户端消息:ping
2025-08-16 16:08:43.735 [http-nio-8080-exec-2] DEBUG org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-08-16 16:08:49.753 [http-nio-8080-exec-3] DEBUG org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-08-16 16:08:51.746 [http-nio-8080-exec-4] DEBUG org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-08-16 16:08:52.973 [wordCompass-scheduling-6] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755331732973, dbSize=848}
2025-08-16 16:08:52.974 [wordCompass-scheduling-6] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755331732974}
2025-08-16 16:08:54.151 [http-nio-8080-exec-10] DEBUG org.jeecg.common.aspect.DictAspect:65 - 获取JSON数据 耗时：143ms
2025-08-16 16:08:54.151 [http-nio-8080-exec-10] DEBUG org.jeecg.common.aspect.DictAspect:69 - 注入字典到JSON数据  耗时0ms
2025-08-16 16:08:56.363 [http-nio-8080-exec-6] DEBUG org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-08-16 16:08:56.438 [http-nio-8080-exec-7] DEBUG org.jeecg.common.aspect.DictAspect:65 - 获取JSON数据 耗时：74ms
2025-08-16 16:08:56.438 [http-nio-8080-exec-7] DEBUG org.jeecg.common.aspect.DictAspect:69 - 注入字典到JSON数据  耗时0ms
2025-08-16 16:09:51.734 [http-nio-8080-exec-8] DEBUG org.jeecg.modules.message.websocket.WebSocket:111 - 【系统 WebSocket】收到客户端消息:ping
2025-08-16 16:09:52.747 [http-nio-8080-exec-9] DEBUG org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-08-16 16:09:52.971 [wordCompass-scheduling-6] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755331792971, dbSize=848}
2025-08-16 16:09:52.971 [wordCompass-scheduling-6] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755331792971}
2025-08-16 16:09:58.753 [http-nio-8080-exec-5] DEBUG org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-08-16 16:10:52.967 [wordCompass-scheduling-6] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755331852967, dbSize=848}
2025-08-16 16:10:52.967 [wordCompass-scheduling-6] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755331852967}
2025-08-16 16:10:54.739 [http-nio-8080-exec-1] DEBUG org.jeecg.modules.message.websocket.WebSocket:111 - 【系统 WebSocket】收到客户端消息:ping
2025-08-16 16:10:56.742 [http-nio-8080-exec-2] DEBUG org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-08-16 16:11:02.753 [http-nio-8080-exec-3] DEBUG org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-08-16 16:11:52.975 [wordCompass-scheduling-6] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755331912975, dbSize=848}
2025-08-16 16:11:52.975 [wordCompass-scheduling-6] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755331912975}
2025-08-16 16:11:58.738 [http-nio-8080-exec-4] DEBUG org.jeecg.modules.message.websocket.WebSocket:111 - 【系统 WebSocket】收到客户端消息:ping
2025-08-16 16:12:00.732 [http-nio-8080-exec-10] DEBUG org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-08-16 16:12:06.749 [http-nio-8080-exec-6] DEBUG org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-08-16 16:12:52.966 [wordCompass-scheduling-6] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755331972966, dbSize=848}
2025-08-16 16:12:52.968 [wordCompass-scheduling-6] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755331972968}
2025-08-16 16:13:02.736 [http-nio-8080-exec-7] DEBUG org.jeecg.modules.message.websocket.WebSocket:111 - 【系统 WebSocket】收到客户端消息:ping
2025-08-16 16:13:04.744 [http-nio-8080-exec-8] DEBUG org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-08-16 16:13:10.756 [http-nio-8080-exec-9] DEBUG org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-08-16 16:13:52.965 [wordCompass-scheduling-6] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755332032965, dbSize=848}
2025-08-16 16:13:52.966 [wordCompass-scheduling-6] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755332032966}
2025-08-16 16:14:06.746 [http-nio-8080-exec-5] DEBUG org.jeecg.modules.message.websocket.WebSocket:111 - 【系统 WebSocket】收到客户端消息:ping
2025-08-16 16:14:08.744 [http-nio-8080-exec-1] DEBUG org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-08-16 16:14:14.746 [http-nio-8080-exec-2] DEBUG org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-08-16 16:14:52.964 [wordCompass-scheduling-6] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755332092964, dbSize=848}
2025-08-16 16:14:52.966 [wordCompass-scheduling-6] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755332092966}
2025-08-16 16:15:10.740 [http-nio-8080-exec-3] DEBUG org.jeecg.modules.message.websocket.WebSocket:111 - 【系统 WebSocket】收到客户端消息:ping
2025-08-16 16:15:12.746 [http-nio-8080-exec-4] DEBUG org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-08-16 16:15:18.745 [http-nio-8080-exec-10] DEBUG org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-08-16 16:15:52.964 [wordCompass-scheduling-6] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755332152964, dbSize=848}
2025-08-16 16:15:52.966 [wordCompass-scheduling-6] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755332152966}
2025-08-16 16:16:14.743 [http-nio-8080-exec-6] DEBUG org.jeecg.modules.message.websocket.WebSocket:111 - 【系统 WebSocket】收到客户端消息:ping
2025-08-16 16:16:16.738 [http-nio-8080-exec-7] DEBUG org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-08-16 16:16:22.752 [http-nio-8080-exec-8] DEBUG org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-08-16 16:16:52.964 [wordCompass-scheduling-6] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755332212964, dbSize=848}
2025-08-16 16:16:52.965 [wordCompass-scheduling-6] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755332212965}
2025-08-16 16:17:18.733 [http-nio-8080-exec-9] DEBUG org.jeecg.modules.message.websocket.WebSocket:111 - 【系统 WebSocket】收到客户端消息:ping
2025-08-16 16:17:20.733 [http-nio-8080-exec-5] DEBUG org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-08-16 16:17:26.748 [http-nio-8080-exec-1] DEBUG org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-08-16 16:17:52.964 [wordCompass-scheduling-6] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755332272964, dbSize=848}
2025-08-16 16:17:52.965 [wordCompass-scheduling-6] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755332272965}
2025-08-16 16:18:22.733 [http-nio-8080-exec-2] DEBUG org.jeecg.modules.message.websocket.WebSocket:111 - 【系统 WebSocket】收到客户端消息:ping
2025-08-16 16:18:24.746 [http-nio-8080-exec-3] DEBUG org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-08-16 16:18:30.744 [http-nio-8080-exec-4] DEBUG org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-08-16 16:18:52.964 [wordCompass-scheduling-6] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755332332964, dbSize=848}
2025-08-16 16:18:52.966 [wordCompass-scheduling-6] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1541312, create_time=1755332332966}
2025-08-16 16:19:26.732 [http-nio-8080-exec-10] DEBUG org.jeecg.modules.message.websocket.WebSocket:111 - 【系统 WebSocket】收到客户端消息:ping
2025-08-16 16:19:28.745 [http-nio-8080-exec-6] DEBUG org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
