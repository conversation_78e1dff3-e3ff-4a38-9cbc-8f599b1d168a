<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
  <head>
    <title>Logback Log Messages</title>
<style  type="text/css">
table { margin-left: 2em; margin-right: 2em; border-left: 2px solid #AAA; }
TR.even { background: #FFFFFF; }
TR.odd { background: #EAEAEA; }
TR.warn TD.Level, TR.error TD.Level, TR.fatal TD.Level {font-weight: bold; color: #FF4040 }
TD { padding-right: 1ex; padding-left: 1ex; border-right: 2px solid #AAA; }
TD.Time, TD.Date { text-align: right; font-family: courier, monospace; font-size: smaller; }
TD.Thread { text-align: left; }
TD.Level { text-align: right; }
TD.Logger { text-align: left; }
TR.header { background: #596ED5; color: #FFF; font-weight: bold; font-size: larger; }
TD.Exception { background: #A2AEE8; font-family: courier, monospace;}
</style>

  </head>
<body>
<hr/>
<p>Log session start time Sat Aug 16 12:49:36 CST 2025</p><p></p>

<table cellspacing="0">
<tr class="header">
<td class="Level">Level</td>
<td class="Date">Date</td>
<td class="Message">Message</td>
<td class="MethodOfCaller">MethodOfCaller</td>
<td class="FileOfCaller">FileOfCaller</td>
<td class="LineOfCaller">LineOfCaller</td>
</tr>


<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:36,232</td>
<td class="Message">HV000001: Hibernate Validator 6.2.5.Final</td>
<td class="MethodOfCaller">&lt;clinit&gt;</td>
<td class="FileOfCaller">Version.java</td>
<td class="LineOfCaller">21</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:36,299</td>
<td class="Message">Starting WordCompassApplication using Java 17.0.6 on DESKTOP-EU3ACCV with PID 31256 (E:\my-work-2025\work-wordcompass\wordCompass\service\wordCompass-server\build\classes\java\main started by Administrator in E:\my-work-2025\work-wordcompass\wordCompass)</td>
<td class="MethodOfCaller">logStarting</td>
<td class="FileOfCaller">StartupInfoLogger.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:36,301</td>
<td class="Message">The following 1 profile is active: &quot;dev&quot;</td>
<td class="MethodOfCaller">logStartupProfileInfo</td>
<td class="FileOfCaller">SpringApplication.java</td>
<td class="LineOfCaller">637</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:38,210</td>
<td class="Message">Multiple Spring Data modules found, entering strict repository configuration mode</td>
<td class="MethodOfCaller">multipleStoresDetected</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">262</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:38,212</td>
<td class="Message">Bootstrapping Spring Data Solr repositories in DEFAULT mode.</td>
<td class="MethodOfCaller">registerRepositoriesIn</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">132</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:38,408</td>
<td class="Message">Finished Spring Data repository scanning in 182 ms. Found 1 Solr repository interfaces.</td>
<td class="MethodOfCaller">registerRepositoriesIn</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">201</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:38,930</td>
<td class="Message">Multiple Spring Data modules found, entering strict repository configuration mode</td>
<td class="MethodOfCaller">multipleStoresDetected</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">262</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:38,931</td>
<td class="Message">Bootstrapping Spring Data Redis repositories in DEFAULT mode.</td>
<td class="MethodOfCaller">registerRepositoriesIn</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">132</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:39,125</td>
<td class="Message">Spring Data Redis - Could not safely identify store assignment for repository candidate interface org.jeecg.modules.km.kno.repository.KnoBaseDetailVoRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository</td>
<td class="MethodOfCaller">isStrictRepositoryCandidate</td>
<td class="FileOfCaller">RepositoryConfigurationExtensionSupport.java</td>
<td class="LineOfCaller">349</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:39,125</td>
<td class="Message">Finished Spring Data repository scanning in 184 ms. Found 0 Redis repository interfaces.</td>
<td class="MethodOfCaller">registerRepositoriesIn</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">201</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:39,574</td>
<td class="Message">Bean &#39;spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties&#39; of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:39,578</td>
<td class="Message">Bean &#39;org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration&#39; of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:39,601</td>
<td class="Message">Bean &#39;org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration&#39; of type [org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:39,602</td>
<td class="Message">Bean &#39;org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusMetricsExportAutoConfiguration&#39; of type [org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusMetricsExportAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:39,605</td>
<td class="Message">Bean &#39;management.metrics.export.prometheus-org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusProperties&#39; of type [org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:39,607</td>
<td class="Message">Bean &#39;prometheusConfig&#39; of type [org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusPropertiesConfigAdapter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:39,610</td>
<td class="Message">Bean &#39;collectorRegistry&#39; of type [io.prometheus.client.CollectorRegistry] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:39,611</td>
<td class="Message">Bean &#39;org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration&#39; of type [org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:39,611</td>
<td class="Message">Bean &#39;micrometerClock&#39; of type [io.micrometer.core.instrument.Clock$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:39,634</td>
<td class="Message">Bean &#39;prometheusMeterRegistry&#39; of type [io.micrometer.prometheus.PrometheusMeterRegistry] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:39,638</td>
<td class="Message">Bean &#39;micrometerOptions&#39; of type [io.lettuce.core.metrics.MicrometerOptions] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:39,639</td>
<td class="Message">Bean &#39;lettuceMetrics&#39; of type [org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration$$Lambda$633/0x000000080120b5a0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:39,751</td>
<td class="Message">Bean &#39;lettuceClientResources&#39; of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:39,864</td>
<td class="Message">Bean &#39;redisConnectionFactory&#39; of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:39,871</td>
<td class="Message">Bean &#39;jeecgBaseConfig&#39; of type [org.jeecg.config.JeecgBaseConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:39,874</td>
<td class="Message">Bean &#39;shiroConfig&#39; of type [org.jeecg.config.shiro.ShiroConfig$$EnhancerBySpringCGLIB$$3bdd0ae3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:40,165</td>
<td class="Message">Bean &#39;spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties&#39; of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:40,174</td>
<td class="Message">Bean &#39;com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration&#39; of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration$$EnhancerBySpringCGLIB$$5e127a3e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:40,189</td>
<td class="Message">Bean &#39;dsProcessor&#39; of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:40,240</td>
<td class="Message">Bean &#39;shiroRealm&#39; of type [org.jeecg.config.shiro.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:40,308</td>
<td class="Message">===============(1)创建缓存管理器RedisCacheManager</td>
<td class="MethodOfCaller">redisCacheManager</td>
<td class="FileOfCaller">ShiroConfig.java</td>
<td class="LineOfCaller">247</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:40,310</td>
<td class="Message">===============(2)创建RedisManager,连接Redis..</td>
<td class="MethodOfCaller">redisManager</td>
<td class="FileOfCaller">ShiroConfig.java</td>
<td class="LineOfCaller">265</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:40,313</td>
<td class="Message">Bean &#39;redisManager&#39; of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:40,319</td>
<td class="Message">Bean &#39;securityManager&#39; of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:40,341</td>
<td class="Message">Bean &#39;authorizationAttributeSourceAdvisor&#39; of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:40,363</td>
<td class="Message">Bean &#39;org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration&#39; of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$efb9a09a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:40,367</td>
<td class="Message">Bean &#39;eventBus&#39; of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:40,711</td>
<td class="Message">Tomcat initialized with port(s): 8080 (http)</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">TomcatWebServer.java</td>
<td class="LineOfCaller">108</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:40,721</td>
<td class="Message">Initializing ProtocolHandler [&quot;http-nio-8080&quot;]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:40,722</td>
<td class="Message">Starting service [Tomcat]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:40,722</td>
<td class="Message">Starting Servlet engine: [Apache Tomcat/9.0.73]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:40,991</td>
<td class="Message">Initializing Spring embedded WebApplicationContext</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:40,991</td>
<td class="Message">Root WebApplicationContext: initialization completed in 4614 ms</td>
<td class="MethodOfCaller">prepareWebApplicationContext</td>
<td class="FileOfCaller">ServletWebServerApplicationContext.java</td>
<td class="LineOfCaller">292</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:42,044</td>
<td class="Message">{dataSource-1,master} inited</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">DruidDataSource.java</td>
<td class="LineOfCaller">1010</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:42,046</td>
<td class="Message">dynamic-datasource - add a datasource named [master] success</td>
<td class="MethodOfCaller">addDataSource</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">154</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:42,046</td>
<td class="Message">dynamic-datasource initial loaded [1] datasource,primary datasource named [master]</td>
<td class="MethodOfCaller">afterPropertiesSet</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">237</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:43,512</td>
<td class="Message"> --- redis config init --- </td>
<td class="MethodOfCaller">redisTemplate</td>
<td class="FileOfCaller">RedisConfig.java</td>
<td class="LineOfCaller">56</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:46,279</td>
<td class="Message">知识服务RestTemplate初始化完成，连接超时: 30000ms，读取超时: 600000ms，最大重试次数: 3，重试间隔: 1000ms</td>
<td class="MethodOfCaller">initKnowledgeRestTemplate</td>
<td class="FileOfCaller">KnowledgeScriptService.java</td>
<td class="LineOfCaller">66</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:46,363</td>
<td class="Message">向量服务RestTemplate初始化完成，连接超时: 30000ms，读取超时: 300000ms</td>
<td class="MethodOfCaller">initVectorRestTemplate</td>
<td class="FileOfCaller">VectorStoreService.java</td>
<td class="LineOfCaller">52</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:46,939</td>
<td class="Message">Using default implementation for ThreadExecutor</td>
<td class="MethodOfCaller">instantiate</td>
<td class="FileOfCaller">StdSchedulerFactory.java</td>
<td class="LineOfCaller">1220</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:46,942</td>
<td class="Message">Job execution threads will use class loader of thread: main</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">SimpleThreadPool.java</td>
<td class="LineOfCaller">268</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:46,953</td>
<td class="Message">Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl</td>
<td class="MethodOfCaller">&lt;init&gt;</td>
<td class="FileOfCaller">SchedulerSignalerImpl.java</td>
<td class="LineOfCaller">61</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:46,953</td>
<td class="Message">Quartz Scheduler v.2.3.2 created.</td>
<td class="MethodOfCaller">&lt;init&gt;</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">229</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:46,955</td>
<td class="Message">Using db table-based data access locking (synchronization).</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">JobStoreSupport.java</td>
<td class="LineOfCaller">672</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:46,957</td>
<td class="Message">JobStoreCMT initialized.</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">JobStoreCMT.java</td>
<td class="LineOfCaller">145</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:46,957</td>
<td class="Message">Scheduler meta-data: Quartz Scheduler (v2.3.2) &#39;MyScheduler&#39; with instanceId &#39;DESKTOP-EU3ACCV1755319786941&#39;
  Scheduler class: &#39;org.quartz.core.QuartzScheduler&#39; - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool &#39;org.quartz.simpl.SimpleThreadPool&#39; - with 10 threads.
  Using job-store &#39;org.springframework.scheduling.quartz.LocalDataSourceJobStore&#39; - which supports persistence. and is clustered.
</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">294</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:46,958</td>
<td class="Message">Quartz scheduler &#39;MyScheduler&#39; initialized from an externally provided properties instance.</td>
<td class="MethodOfCaller">instantiate</td>
<td class="FileOfCaller">StdSchedulerFactory.java</td>
<td class="LineOfCaller">1374</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:46,958</td>
<td class="Message">Quartz scheduler version: 2.3.2</td>
<td class="MethodOfCaller">instantiate</td>
<td class="FileOfCaller">StdSchedulerFactory.java</td>
<td class="LineOfCaller">1378</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:46,958</td>
<td class="Message">JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@3652ce04</td>
<td class="MethodOfCaller">setJobFactory</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">2293</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:48,966</td>
<td class="Message">Exposing 2 endpoint(s) beneath base path &#39;/actuator&#39;</td>
<td class="MethodOfCaller">&lt;init&gt;</td>
<td class="FileOfCaller">EndpointLinksResolver.java</td>
<td class="LineOfCaller">58</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:49,091</td>
<td class="Message"> Init CodeGenerate Config [ Get Db Config From application.yml ] </td>
<td class="MethodOfCaller">initCodeGenerateDbConfig</td>
<td class="FileOfCaller">CodeGenerateDbConfig.java</td>
<td class="LineOfCaller">46</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:50,553</td>
<td class="Message">Starting ProtocolHandler [&quot;http-nio-8080&quot;]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:50,584</td>
<td class="Message">Tomcat started on port(s): 8080 (http) with context path &#39;/wordCompassApi&#39;</td>
<td class="MethodOfCaller">start</td>
<td class="FileOfCaller">TomcatWebServer.java</td>
<td class="LineOfCaller">220</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:52,946</td>
<td class="Message">Will start Quartz Scheduler [MyScheduler] in 1 seconds</td>
<td class="MethodOfCaller">startScheduler</td>
<td class="FileOfCaller">SchedulerFactoryBean.java</td>
<td class="LineOfCaller">734</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:52,962</td>
<td class="Message">开始同步KnoBaseDetail数据到Solr...</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">490</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:52,967</td>
<td class="Message">Started WordCompassApplication in 17.559 seconds (JVM running for 20.138)</td>
<td class="MethodOfCaller">logStarted</td>
<td class="FileOfCaller">StartupInfoLogger.java</td>
<td class="LineOfCaller">61</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:52,972</td>
<td class="Message">--getKeysSize--: {create_time=1755319792972, dbSize=846}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:52,976</td>
<td class="Message">--getMemoryInfo--: {used_memory=1576864, create_time=1755319792976}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:52,979</td>
<td class="Message"> Init Code Generate Template [ 检测如果是JAR启动环境，Copy模板到config目录 ] </td>
<td class="MethodOfCaller">onApplicationEvent</td>
<td class="FileOfCaller">CodeTemplateInitListener.java</td>
<td class="LineOfCaller">29</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:52,982</td>
<td class="Message">
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8080/wordCompassApi/
	External: 	http://*************:8080/wordCompassApi/
	Swagger文档: 	http://*************:8080/wordCompassApi/doc.html
----------------------------------------------------------</td>
<td class="MethodOfCaller">main</td>
<td class="FileOfCaller">WordCompassApplication.java</td>
<td class="LineOfCaller">39</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,134</td>
<td class="Message">==&gt;  Preparing: SELECT id, title, content, ocr_content, srcipt, srcipt_simplify, category_id, parent_id, knowledge_id, keywords, view_count, status, source_type, creator_name, updater_name, create_time, update_time, organ_name, organ_id, single_image_file_path, from_detail_page_num, from_detail_page_id, order_num FROM kno_base_detail WHERE (status &lt;&gt; ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,225</td>
<td class="Message">==&gt; Parameters: 0(Integer)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,278</td>
<td class="Message">&lt;==      Total: 28</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,291</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,293</td>
<td class="Message">==&gt; Parameters: 1949666867036524546(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,294</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,301</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,301</td>
<td class="Message">==&gt; Parameters: 1949666867103633409(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,302</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,308</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,310</td>
<td class="Message">==&gt; Parameters: 1949666867103633410(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,311</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,314</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,315</td>
<td class="Message">==&gt; Parameters: 1949666867103633411(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,315</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,321</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,322</td>
<td class="Message">==&gt; Parameters: 1949666867170742274(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,322</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,327</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,327</td>
<td class="Message">==&gt; Parameters: 1949666867170742275(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,328</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,332</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,334</td>
<td class="Message">==&gt; Parameters: 1949666867170742276(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,335</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,339</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,340</td>
<td class="Message">==&gt; Parameters: 1949666867237851138(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,341</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,346</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,346</td>
<td class="Message">==&gt; Parameters: 1949666867246239745(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,348</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,353</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,353</td>
<td class="Message">==&gt; Parameters: 1949666867246239746(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,354</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,358</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,360</td>
<td class="Message">==&gt; Parameters: 1949666867246239747(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,361</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,365</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,365</td>
<td class="Message">==&gt; Parameters: 1949666867309154305(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,367</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,370</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,371</td>
<td class="Message">==&gt; Parameters: 1949666867309154306(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,372</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,376</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,377</td>
<td class="Message">==&gt; Parameters: 1949666867309154307(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,377</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,383</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,384</td>
<td class="Message">==&gt; Parameters: 1949666867388846081(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,386</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,396</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,396</td>
<td class="Message">==&gt; Parameters: 1949666867388846082(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,397</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,401</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,401</td>
<td class="Message">==&gt; Parameters: 1949666867388846083(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,403</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,410</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,411</td>
<td class="Message">==&gt; Parameters: 1949666867388846084(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,412</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,417</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,418</td>
<td class="Message">==&gt; Parameters: 1949666867460149249(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,420</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,424</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,425</td>
<td class="Message">==&gt; Parameters: 1949666867460149250(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,426</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,433</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,435</td>
<td class="Message">==&gt; Parameters: 1949666867460149251(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,436</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,444</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,444</td>
<td class="Message">==&gt; Parameters: 1949666867535646721(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,446</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,453</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,453</td>
<td class="Message">==&gt; Parameters: 1949666867535646722(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,454</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,461</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,461</td>
<td class="Message">==&gt; Parameters: 1949672451836186626(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,463</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,469</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,469</td>
<td class="Message">==&gt; Parameters: 1949672452326920193(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,471</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,476</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,476</td>
<td class="Message">==&gt; Parameters: 1949672452607938561(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,478</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,484</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,484</td>
<td class="Message">==&gt; Parameters: 1949672452817653761(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,485</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,492</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,493</td>
<td class="Message">==&gt; Parameters: 1949672453232889857(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:49:53,494</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-08-16 12:49:53,613</td>
<td class="Message">同步KnoBaseDetail数据到Solr失败</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">507</td>
</tr>
<tr><td class="Exception" colspan="6">org.springframework.dao.DataAccessResourceFailureException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrExceptionTranslator.translateExceptionIfPossible(SolrExceptionTranslator.java:79)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:169)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.delete(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrOperations.delete(SolrOperations.java:228)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.repository.support.SimpleSolrRepository.deleteAll(SimpleSolrRepository.java:212)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService.deleteAll(KnoBaseDetailSolrService.java:313)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$FastClassBySpringCGLIB$$1d6fbb9b.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$EnhancerBySpringCGLIB$$daa8b9a9.deleteAll(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailService.syncToSolr(KnoBaseDetailService.java:497)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.Thread.run(Thread.java:833)
<br />Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:940)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:903)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.lambda$delete$6(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:167)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 65 common frames omitted
<br />Caused by: java.net.ConnectException: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnect(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.Socket.connect(Socket.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 82 common frames omitted
</td></tr>
<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:53,950</td>
<td class="Message">Starting Quartz Scheduler now, after delay of 1 seconds</td>
<td class="MethodOfCaller">run</td>
<td class="FileOfCaller">SchedulerFactoryBean.java</td>
<td class="LineOfCaller">750</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:54,058</td>
<td class="Message">ClusterManager: detected 1 failed or restarted instances.</td>
<td class="MethodOfCaller">logWarnIfNonZero</td>
<td class="FileOfCaller">JobStoreSupport.java</td>
<td class="LineOfCaller">3644</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:54,059</td>
<td class="Message">ClusterManager: Scanning for instance &quot;DESKTOP-EU3ACCV1755251243489&quot;&#39;s failed in-progress jobs.</td>
<td class="MethodOfCaller">clusterRecover</td>
<td class="FileOfCaller">JobStoreSupport.java</td>
<td class="LineOfCaller">3503</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:54,066</td>
<td class="Message">Scheduler MyScheduler_$_DESKTOP-EU3ACCV1755319786941 started.</td>
<td class="MethodOfCaller">start</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">547</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:50:26,981</td>
<td class="Message">A cookie header was received [Hm_lvt_0febd9e3cacb3f627ddac64d52caac39=1755243411,1755246569,1755248930,1755319815;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:50:26,994</td>
<td class="Message">Initializing Spring DispatcherServlet &#39;dispatcherServlet&#39;</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:50:26,994</td>
<td class="Message">Initializing Servlet &#39;dispatcherServlet&#39;</td>
<td class="MethodOfCaller">initServletBean</td>
<td class="FileOfCaller">FrameworkServlet.java</td>
<td class="LineOfCaller">525</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:50:26,999</td>
<td class="Message">Completed initialization in 5 ms</td>
<td class="MethodOfCaller">initServletBean</td>
<td class="FileOfCaller">FrameworkServlet.java</td>
<td class="LineOfCaller">547</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:50:27,176</td>
<td class="Message">获取验证码，Redis key = a3420689587752d348c76d0baf25c3fd，checkCode = tfb8</td>
<td class="MethodOfCaller">randomImage</td>
<td class="FileOfCaller">LoginController.java</td>
<td class="LineOfCaller">542</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:50:27,565</td>
<td class="Message">获取JSON数据 耗时：424ms</td>
<td class="MethodOfCaller">doAround</td>
<td class="FileOfCaller">DictAspect.java</td>
<td class="LineOfCaller">65</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:50:27,565</td>
<td class="Message">注入字典到JSON数据  耗时0ms</td>
<td class="MethodOfCaller">doAround</td>
<td class="FileOfCaller">DictAspect.java</td>
<td class="LineOfCaller">69</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:50:52,966</td>
<td class="Message">--getKeysSize--: {create_time=1755319852966, dbSize=847}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:50:52,973</td>
<td class="Message">--getMemoryInfo--: {used_memory=1540160, create_time=1755319852972}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-08-16 12:51:17,368</td>
<td class="Message">Unable to make field private final java.lang.Class java.lang.invoke.SerializedLambda.capturingClass accessible: module java.base does not &quot;opens java.lang.invoke&quot; to unnamed module @65e2dbf3</td>
<td class="MethodOfCaller">&lt;clinit&gt;</td>
<td class="FileOfCaller">ReflectLambdaMeta.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:51:17,480</td>
<td class="Message"> 登录接口用户的租户ID = 1000</td>
<td class="MethodOfCaller">setLoginTenant</td>
<td class="FileOfCaller">SysUserServiceImpl.java</td>
<td class="LineOfCaller">901</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:51:17,519</td>
<td class="Message">redis remove key:sys:cache:encrypt:user::admin</td>
<td class="MethodOfCaller">remove</td>
<td class="FileOfCaller">JeecgRedisCacheWriter.java</td>
<td class="LineOfCaller">113</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:51:17,545</td>
<td class="Message">==&gt;  Preparing: insert into sys_log (id, log_type, log_content, method, operate_type, request_url, request_type, request_param, ip, userid, username, cost_time, create_time,create_by, tenant_id) values( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:51:17,546</td>
<td class="Message">==&gt; Parameters: 1956579494112583682(String), 1(Integer), 用户名: admin,登录成功！(String), null, null, null, null, null, 0:0:0:0:0:0:0:1(String), admin(String), 管理员(String), null, 2025-08-16 12:51:17.544(Timestamp), null, null</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:51:17,553</td>
<td class="Message">&lt;==    Updates: 1</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:51:17,553</td>
<td class="Message">获取JSON数据 耗时：215ms</td>
<td class="MethodOfCaller">doAround</td>
<td class="FileOfCaller">DictAspect.java</td>
<td class="LineOfCaller">65</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:51:17,553</td>
<td class="Message">注入字典到JSON数据  耗时0ms</td>
<td class="MethodOfCaller">doAround</td>
<td class="FileOfCaller">DictAspect.java</td>
<td class="LineOfCaller">69</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:51:17,663</td>
<td class="Message">加密操作，Aspect程序耗时：8ms</td>
<td class="MethodOfCaller">around</td>
<td class="FileOfCaller">SensitiveDataAspect.java</td>
<td class="LineOfCaller">76</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:51:17,886</td>
<td class="Message">-------登录加载系统字典-----{ol_form_biz_type=[DictModel(value=demo, text=官方示例, color=null, jsonObject=null), DictModel(value=bpm, text=流程表单, color=null, jsonObject=null), DictModel(value=temp, text=测试表单, color=null, jsonObject=null), DictModel(value=bdfl_include, text=导入表单, color=null, jsonObject=null)], position_rank=[DictModel(value=1, text=员级, color=null, jsonObject=null), DictModel(value=2, text=助级, color=null, jsonObject=null), DictModel(value=3, text=中级, color=null, jsonObject=null), DictModel(value=4, text=副高级, color=null, jsonObject=null), DictModel(value=5, text=正高级, color=null, jsonObject=null)], rule_conditions=[DictModel(value=&gt;, text=大于, color=null, jsonObject=null), DictModel(value=&lt;, text=小于, color=null, jsonObject=null), DictModel(value=!=, text=不等于, color=null, jsonObject=null), DictModel(value==, text=等于, color=null, jsonObject=null), DictModel(value=&gt;=, text=大于等于, color=null, jsonObject=null), DictModel(value=&lt;=, text=小于等于, color=null, jsonObject=null), DictModel(value=LEFT_LIKE, text=左模糊, color=null, jsonObject=null), DictModel(value=RIGHT_LIKE, text=右模糊, color=null, jsonObject=null), DictModel(value=LIKE, text=模糊, color=null, jsonObject=null), DictModel(value=IN, text=包含, color=null, jsonObject=null), DictModel(value=USE_SQL_RULES, text=自定义SQL表达式, color=null, jsonObject=null)], ceshi_online=[DictModel(value=00, text=000, color=null, jsonObject=null), DictModel(value=3, text=easyui, color=null, jsonObject=null), DictModel(value=1, text=booostrap, color=null, jsonObject=null)], online_graph_data_type=[DictModel(value=sql, text=SQL, color=null, jsonObject=null), DictModel(value=json, text=JSON, color=null, jsonObject=null), DictModel(value=api, text=API, color=null, jsonObject=null)], online_graph_display_template=[DictModel(value=tab, text=Tab风格, color=null, jsonObject=null), DictModel(value=single, text=单排布局, color=null, jsonObject=null), DictModel(value=double, text=双排布局, color=null, jsonObject=null), DictModel(value=combination, text=组合布局, color=null, jsonObject=null)], company_rank=[DictModel(value=1, text=总裁/总经理/CEO, color=null, jsonObject=null), DictModel(value=2, text=副总裁/副总经理/VP, color=null, jsonObject=null), DictModel(value=3, text=总监/主管/经理, color=null, jsonObject=null), DictModel(value=4, text=员工/专员/执行, color=null, jsonObject=null), DictModel(value=5, text=其他, color=null, jsonObject=null)], user_type=[DictModel(value=333, text=333, color=null, jsonObject=null)], messageType=[DictModel(value=system, text=系统消息, color=null, jsonObject=null), DictModel(value=email, text=邮件消息, color=null, jsonObject=null), DictModel(value=dingtalk, text=钉钉消息, color=null, jsonObject=null), DictModel(value=wechat_enterprise, text=企业微信, color=null, jsonObject=null)], remindMode=[DictModel(value=1, text=邮件提醒, color=null, jsonObject=null), DictModel(value=2, text=短信提醒, color=null, jsonObject=null), DictModel(value=4, text=系统消息, color=null, jsonObject=null)], yn=[DictModel(value=1, text=是, color=null, jsonObject=null), DictModel(value=0, text=否, color=null, jsonObject=null)], tenant_status=[DictModel(value=1, text=正常, color=null, jsonObject=null), DictModel(value=0, text=冻结, color=null, jsonObject=null)], taskStatus=[DictModel(value=1, text=创建, color=null, jsonObject=null), DictModel(value=2, text=发布, color=null, jsonObject=null), DictModel(value=3, text=撤销, color=null, jsonObject=null)], deviceType=[DictModel(value=1, text=测试, color=null, jsonObject=null), DictModel(value=2, text=评估, color=null, jsonObject=null)], rangeDate=[DictModel(value=jt, text=今天, color=null, jsonObject=null), DictModel(value=zt, text=昨天, color=null, jsonObject=null), DictModel(value=qt, text=前天, color=null, jsonObject=null), DictModel(value=bz, text=本周, color=null, jsonObject=null), DictModel(value=sz, text=上周, color=null, jsonObject=null), DictModel(value=by, text=本月, color=null, jsonObject=null), DictModel(value=sy, text=上月, color=null, jsonObject=null), DictModel(value=7day, text=7日, color=null, jsonObject=null), DictModel(value=zdy, text=自定义日期, color=null, jsonObject=null)], del_flag=[DictModel(value=1, text=已删除, color=null, jsonObject=null), DictModel(value=0, text=正常, color=null, jsonObject=null)], messageHref=[DictModel(value=bpm, text=/task/myHandleTaskInfo, color=null, jsonObject=null), DictModel(value=bpm_msg_node, text=, color=null, jsonObject=null), DictModel(value=bpm_cc, text=/task/myHandleTaskInfo, color=null, jsonObject=null), DictModel(value=bpm_task, text=/task/myHandleTaskInfo, color=null, jsonObject=null), DictModel(value=email, text=/eoa/email, color=null, jsonObject=null)], cgform_table_type=[DictModel(value=1, text=单表, color=null, jsonObject=null), DictModel(value=2, text=主表, color=null, jsonObject=null), DictModel(value=3, text=附表, color=null, jsonObject=null)], is_open=[DictModel(value=Y, text=是, color=null, jsonObject=null), DictModel(value=N, text=否, color=null, jsonObject=null)], msg_category=[DictModel(value=1, text=通知公告, color=null, jsonObject=null), DictModel(value=2, text=系统消息, color=null, jsonObject=null)], org_category=[DictModel(value=3, text=岗位, color=null, jsonObject=null), DictModel(value=1, text=公司, color=null, jsonObject=null), DictModel(value=2, text=部门, color=null, jsonObject=null), DictModel(value=4, text=分公司, color=null, jsonObject=null)], priority=[DictModel(value=H, text=高, color=null, jsonObject=null), DictModel(value=M, text=中, color=null, jsonObject=null), DictModel(value=L, text=低, color=null, jsonObject=null)], dict_item_status=[DictModel(value=1, text=启用, color=null, jsonObject=null), DictModel(value=0, text=不启用, color=null, jsonObject=null)], company_department=[DictModel(value=1, text=总经办, color=null, jsonObject=null), DictModel(value=2, text=技术/IT/研发, color=null, jsonObject=null), DictModel(value=3, text=产品/设计, color=null, jsonObject=null), DictModel(value=4, text=销售/市场/运营, color=null, jsonObject=null), DictModel(value=5, text=人事/财务/行政, color=null, jsonObject=null), DictModel(value=6, text=资源/仓储/采购, color=null, jsonObject=null), DictModel(value=7, text=其他, color=null, jsonObject=null)], activiti_sync=[DictModel(value=1, text=同步, color=null, jsonObject=null), DictModel(value=0, text=不同步, color=null, jsonObject=null)], msgSendStatus=[DictModel(value=0, text=未发送, color=null, jsonObject=null), DictModel(value=1, text=发送成功, color=null, jsonObject=null), DictModel(value=2, text=发送失败, color=null, jsonObject=null)], msg_type=[DictModel(value=USER, text=指定用户, color=null, jsonObject=null), DictModel(value=ALL, text=全体用户, color=null, jsonObject=null)], eoa_plan_type=[DictModel(value=1, text=日常记录, color=null, jsonObject=null), DictModel(value=2, text=本周工作, color=null, jsonObject=null), DictModel(value=3, text=下周计划, color=null, jsonObject=null)], company_size=[DictModel(value=1, text=20人以下, color=null, jsonObject=null), DictModel(value=2, text=21-99人, color=null, jsonObject=null), DictModel(value=3, text=100-499人, color=null, jsonObject=null), DictModel(value=4, text=500-999人, color=null, jsonObject=null), DictModel(value=5, text=1000-9999人, color=null, jsonObject=null), DictModel(value=6, text=10000人以上, color=null, jsonObject=null)], status=[DictModel(value=1, text=正常, color=null, jsonObject=null), DictModel(value=2, text=冻结, color=null, jsonObject=null)], msgType=[DictModel(value=1, text=文本, color=null, jsonObject=null), DictModel(value=2, text=富文本, color=null, jsonObject=null)], eoa_plan_status=[DictModel(value=0, text=未开始, color=null, jsonObject=null), DictModel(value=1, text=进行中, color=null, jsonObject=null), DictModel(value=2, text=已完成, color=null, jsonObject=null)], database_type=[DictModel(value=1, text=MySQL5.5, color=null, jsonObject=null), DictModel(value=4, text=MYSQL5.7+, color=null, jsonObject=null), DictModel(value=2, text=Oracle, color=null, jsonObject=null), DictModel(value=3, text=SQLServer, color=null, jsonObject=null), DictModel(value=6, text=postgresql, color=null, jsonObject=null), DictModel(value=5, text=marialDB, color=null, jsonObject=null), DictModel(value=7, text=达梦, color=null, jsonObject=null), DictModel(value=8, text=人大金仓, color=null, jsonObject=null), DictModel(value=9, text=神通, color=null, jsonObject=null), DictModel(value=10, text=SQLite, color=null, jsonObject=null), DictModel(value=11, text=DB2, color=null, jsonObject=null), DictModel(value=12, text=Hsqldb, color=null, jsonObject=null), DictModel(value=13, text=Derby, color=null, jsonObject=null), DictModel(value=14, text=H2, color=null, jsonObject=null), DictModel(value=15, text=其他数据库, color=null, jsonObject=null)], log_type=[DictModel(value=2, text=操作日志, color=null, jsonObject=null), DictModel(value=1, text=登录日志, color=null, jsonObject=null)], send_status=[DictModel(value=0, text=未发布, color=null, jsonObject=null), DictModel(value=1, text=已发布, color=null, jsonObject=null), DictModel(value=2, text=已撤销, color=null, jsonObject=null)], eoa_cms_menu_type=[DictModel(value=1, text=列表, color=null, jsonObject=null), DictModel(value=2, text=链接, color=null, jsonObject=null)], bpm_process_type=[DictModel(value=test, text=测试流程, color=null, jsonObject=null), DictModel(value=oa, text=OA办公, color=null, jsonObject=null), DictModel(value=business, text=业务办理, color=null, jsonObject=null)], form_perms_type=[DictModel(value=1, text=可见(未授权不可见), color=null, jsonObject=null), DictModel(value=2, text=可编辑(未授权禁用), color=null, jsonObject=null)], valid_status=[DictModel(value=0, text=无效, color=null, jsonObject=null), DictModel(value=1, text=有效, color=null, jsonObject=null)], urgent_level=[DictModel(value=1, text=一般, color=null, jsonObject=null), DictModel(value=2, text=重要, color=null, jsonObject=null), DictModel(value=3, text=紧急, color=null, jsonObject=null)], user_status=[DictModel(value=1, text=正常, color=null, jsonObject=null), DictModel(value=2, text=冻结, color=null, jsonObject=null)], operate_type=[DictModel(value=1, text=查询, color=null, jsonObject=null), DictModel(value=2, text=添加, color=null, jsonObject=null), DictModel(value=3, text=修改, color=null, jsonObject=null), DictModel(value=4, text=删除, color=null, jsonObject=null), DictModel(value=5, text=导入, color=null, jsonObject=null), DictModel(value=6, text=导出, color=null, jsonObject=null)], quartz_status=[DictModel(value=0, text=正常, color=null, jsonObject=null), DictModel(value=-1, text=停止, color=null, jsonObject=null)], menu_type=[DictModel(value=2, text=按钮权限, color=null, jsonObject=null), DictModel(value=1, text=子菜单, color=null, jsonObject=null), DictModel(value=0, text=一级菜单, color=null, jsonObject=null)], sex=[DictModel(value=1, text=男, color=null, jsonObject=null), DictModel(value=2, text=女, color=null, jsonObject=null)], perms_type=[DictModel(value=1, text=显示, color=null, jsonObject=null), DictModel(value=2, text=禁用, color=null, jsonObject=null)], global_perms_type=[DictModel(value=1, text=可见/可访问(授权后可见/可访问), color=null, jsonObject=null), DictModel(value=2, text=可编辑(未授权时禁用), color=null, jsonObject=null)], online_graph_type=[DictModel(value=bar, text=柱状图, color=null, jsonObject=null), DictModel(value=line, text=曲线图, color=null, jsonObject=null), DictModel(value=pie, text=饼图, color=null, jsonObject=null), DictModel(value=table, text=数据列表, color=null, jsonObject=null)], trade=[DictModel(value=1, text=信息传输、软件和信息技术服务业, color=null, jsonObject=null), DictModel(value=2, text=制造业, color=null, jsonObject=null), DictModel(value=3, text=租赁和商务服务业, color=null, jsonObject=null), DictModel(value=4, text=教育, color=null, jsonObject=null), DictModel(value=5, text=金融业, color=null, jsonObject=null), DictModel(value=6, text=建筑业, color=null, jsonObject=null), DictModel(value=7, text=科学研究和技术服务业, color=null, jsonObject=null), DictModel(value=8, text=批发和零售业, color=null, jsonObject=null), DictModel(value=9, text=住宿和餐饮业, color=null, jsonObject=null), DictModel(value=10, text=电子商务, color=null, jsonObject=null), DictModel(value=11, text=线下零售与服务业, color=null, jsonObject=null), DictModel(value=12, text=文化、体育和娱乐业, color=null, jsonObject=null), DictModel(value=13, text=房地产业, color=null, jsonObject=null), DictModel(value=14, text=交通运输、仓储和邮政业, color=null, jsonObject=null), DictModel(value=15, text=卫生和社会工作, color=null, jsonObject=null), DictModel(value=16, text=公共管理、社会保障和社会组织, color=null, jsonObject=null), DictModel(value=18, text=电力、热力、燃气及水生产和供应业, color=null, jsonObject=null), DictModel(value=19, text=水利、环境和公共设施管理业, color=null, jsonObject=null), DictModel(value=20, text=居民服务、修理和其他服务业, color=null, jsonObject=null), DictModel(value=21, text=政府机构, color=null, jsonObject=null), DictModel(value=22, text=农、林、牧、渔业, color=null, jsonObject=null), DictModel(value=23, text=采矿业, color=null, jsonObject=null), DictModel(value=24, text=国际组织, color=null, jsonObject=null), DictModel(value=25, text=其他, color=null, jsonObject=null)], bpm_status=[DictModel(value=1, text=待提交, color=null, jsonObject=null), DictModel(value=2, text=处理中, color=null, jsonObject=null), DictModel(value=3, text=已完成, color=null, jsonObject=null), DictModel(value=4, text=已作废, color=null, jsonObject=null)], depart_status=[DictModel(value=0, text=不启用, color=null, jsonObject=null), DictModel(value=1, text=启用, color=null, jsonObject=null)]}</td>
<td class="MethodOfCaller">queryAllDictItems</td>
<td class="FileOfCaller">SysDictServiceImpl.java</td>
<td class="LineOfCaller">184</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:51:17,889</td>
<td class="Message">获取JSON数据 耗时：200ms</td>
<td class="MethodOfCaller">doAround</td>
<td class="FileOfCaller">DictAspect.java</td>
<td class="LineOfCaller">65</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:51:17,889</td>
<td class="Message">注入字典到JSON数据  耗时0ms</td>
<td class="MethodOfCaller">doAround</td>
<td class="FileOfCaller">DictAspect.java</td>
<td class="LineOfCaller">69</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:51:18,796</td>
<td class="Message">获取JSON数据 耗时：212ms</td>
<td class="MethodOfCaller">doAround</td>
<td class="FileOfCaller">DictAspect.java</td>
<td class="LineOfCaller">65</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:51:18,796</td>
<td class="Message">注入字典到JSON数据  耗时0ms</td>
<td class="MethodOfCaller">doAround</td>
<td class="FileOfCaller">DictAspect.java</td>
<td class="LineOfCaller">69</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:51:23,388</td>
<td class="Message">【系统 WebSocket】有新的连接，总数为:1</td>
<td class="MethodOfCaller">onOpen</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">43</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:51:23,453</td>
<td class="Message">获取JSON数据 耗时：77ms</td>
<td class="MethodOfCaller">doAround</td>
<td class="FileOfCaller">DictAspect.java</td>
<td class="LineOfCaller">65</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:51:23,453</td>
<td class="Message">注入字典到JSON数据  耗时0ms</td>
<td class="MethodOfCaller">doAround</td>
<td class="FileOfCaller">DictAspect.java</td>
<td class="LineOfCaller">69</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:51:52,738</td>
<td class="Message">查询磁盘信息:6个</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">117</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:51:52,769</td>
<td class="Message">{name=OS (C:), rest=186166153216, restPPT=56, max=429700067328}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:51:52,776</td>
<td class="Message">{name=本地磁盘 (D:), rest=138221797376, restPPT=48, max=268607250432}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:51:52,781</td>
<td class="Message">{name=本地磁盘 (E:), rest=57584033792, restPPT=61, max=151161372672}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:51:52,786</td>
<td class="Message">{name=本地磁盘 (F:), rest=69867331584, restPPT=53, max=150828929024}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:51:52,793</td>
<td class="Message">{name=本地磁盘 (G:), rest=137231179776, restPPT=72, max=500170412032}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:51:52,799</td>
<td class="Message">{name=本地磁盘 (H:), rest=260168253440, restPPT=47, max=500030472192}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:51:52,801</td>
<td class="Message">获取JSON数据 耗时：309ms</td>
<td class="MethodOfCaller">doAround</td>
<td class="FileOfCaller">DictAspect.java</td>
<td class="LineOfCaller">65</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:51:52,801</td>
<td class="Message">注入字典到JSON数据  耗时0ms</td>
<td class="MethodOfCaller">doAround</td>
<td class="FileOfCaller">DictAspect.java</td>
<td class="LineOfCaller">69</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:51:52,977</td>
<td class="Message">--getKeysSize--: {create_time=1755319912977, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:51:52,982</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755319912982}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:52:18,740</td>
<td class="Message">【系统 WebSocket】收到客户端消息:ping</td>
<td class="MethodOfCaller">onMessage</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:52:20,748</td>
<td class="Message">【系统 WebSocket】连接断开，总数为:0</td>
<td class="MethodOfCaller">onClose</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">52</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:52:26,758</td>
<td class="Message">【系统 WebSocket】有新的连接，总数为:1</td>
<td class="MethodOfCaller">onOpen</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">43</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:52:52,972</td>
<td class="Message">--getKeysSize--: {create_time=1755319972972, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:52:52,975</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755319972975}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:53:22,747</td>
<td class="Message">【系统 WebSocket】收到客户端消息:ping</td>
<td class="MethodOfCaller">onMessage</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:53:24,744</td>
<td class="Message">【系统 WebSocket】连接断开，总数为:0</td>
<td class="MethodOfCaller">onClose</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">52</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:53:29,765</td>
<td class="Message">【系统 WebSocket】有新的连接，总数为:1</td>
<td class="MethodOfCaller">onOpen</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">43</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:53:31,587</td>
<td class="Message">======获取全部菜单数据=====耗时:39毫秒</td>
<td class="MethodOfCaller">list</td>
<td class="FileOfCaller">SysPermissionController.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:53:31,589</td>
<td class="Message">获取JSON数据 耗时：41ms</td>
<td class="MethodOfCaller">doAround</td>
<td class="FileOfCaller">DictAspect.java</td>
<td class="LineOfCaller">65</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:53:31,589</td>
<td class="Message">注入字典到JSON数据  耗时0ms</td>
<td class="MethodOfCaller">doAround</td>
<td class="FileOfCaller">DictAspect.java</td>
<td class="LineOfCaller">69</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:53:46,748</td>
<td class="Message">【系统 WebSocket】连接断开，总数为:0</td>
<td class="MethodOfCaller">onClose</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">52</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:53:47,653</td>
<td class="Message">获取JSON数据 耗时：106ms</td>
<td class="MethodOfCaller">doAround</td>
<td class="FileOfCaller">DictAspect.java</td>
<td class="LineOfCaller">65</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:53:47,653</td>
<td class="Message">注入字典到JSON数据  耗时0ms</td>
<td class="MethodOfCaller">doAround</td>
<td class="FileOfCaller">DictAspect.java</td>
<td class="LineOfCaller">69</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:53:48,105</td>
<td class="Message">【系统 WebSocket】有新的连接，总数为:1</td>
<td class="MethodOfCaller">onOpen</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">43</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:53:48,173</td>
<td class="Message">获取JSON数据 耗时：68ms</td>
<td class="MethodOfCaller">doAround</td>
<td class="FileOfCaller">DictAspect.java</td>
<td class="LineOfCaller">65</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:53:48,173</td>
<td class="Message">注入字典到JSON数据  耗时0ms</td>
<td class="MethodOfCaller">doAround</td>
<td class="FileOfCaller">DictAspect.java</td>
<td class="LineOfCaller">69</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:53:52,970</td>
<td class="Message">--getKeysSize--: {create_time=1755320032970, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:53:52,970</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755320032970}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:54:43,118</td>
<td class="Message">【系统 WebSocket】收到客户端消息:ping</td>
<td class="MethodOfCaller">onMessage</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:54:44,121</td>
<td class="Message">【系统 WebSocket】连接断开，总数为:0</td>
<td class="MethodOfCaller">onClose</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">52</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:54:49,149</td>
<td class="Message">【系统 WebSocket】有新的连接，总数为:1</td>
<td class="MethodOfCaller">onOpen</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">43</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:54:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755320092964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:54:52,966</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755320092966}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:55:44,165</td>
<td class="Message">【系统 WebSocket】收到客户端消息:ping</td>
<td class="MethodOfCaller">onMessage</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:55:45,174</td>
<td class="Message">【系统 WebSocket】连接断开，总数为:0</td>
<td class="MethodOfCaller">onClose</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">52</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:55:50,194</td>
<td class="Message">【系统 WebSocket】有新的连接，总数为:1</td>
<td class="MethodOfCaller">onOpen</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">43</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:55:52,967</td>
<td class="Message">--getKeysSize--: {create_time=1755320152967, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:55:52,968</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755320152968}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:56:45,206</td>
<td class="Message">【系统 WebSocket】收到客户端消息:ping</td>
<td class="MethodOfCaller">onMessage</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:56:46,217</td>
<td class="Message">【系统 WebSocket】连接断开，总数为:0</td>
<td class="MethodOfCaller">onClose</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">52</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:56:51,235</td>
<td class="Message">【系统 WebSocket】有新的连接，总数为:1</td>
<td class="MethodOfCaller">onOpen</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">43</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:56:52,977</td>
<td class="Message">--getKeysSize--: {create_time=1755320212977, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:56:52,977</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755320212977}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:57:46,241</td>
<td class="Message">【系统 WebSocket】收到客户端消息:ping</td>
<td class="MethodOfCaller">onMessage</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:57:47,244</td>
<td class="Message">【系统 WebSocket】连接断开，总数为:0</td>
<td class="MethodOfCaller">onClose</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">52</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:57:52,759</td>
<td class="Message">【系统 WebSocket】有新的连接，总数为:1</td>
<td class="MethodOfCaller">onOpen</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">43</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:57:52,965</td>
<td class="Message">--getKeysSize--: {create_time=1755320272965, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:57:52,968</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755320272968}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:57:58,743</td>
<td class="Message">【系统 WebSocket】连接断开，总数为:0</td>
<td class="MethodOfCaller">onClose</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">52</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:58:00,077</td>
<td class="Message">获取JSON数据 耗时：151ms</td>
<td class="MethodOfCaller">doAround</td>
<td class="FileOfCaller">DictAspect.java</td>
<td class="LineOfCaller">65</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:58:00,078</td>
<td class="Message">注入字典到JSON数据  耗时0ms</td>
<td class="MethodOfCaller">doAround</td>
<td class="FileOfCaller">DictAspect.java</td>
<td class="LineOfCaller">69</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:58:00,535</td>
<td class="Message">【系统 WebSocket】有新的连接，总数为:1</td>
<td class="MethodOfCaller">onOpen</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">43</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:58:00,591</td>
<td class="Message">获取JSON数据 耗时：56ms</td>
<td class="MethodOfCaller">doAround</td>
<td class="FileOfCaller">DictAspect.java</td>
<td class="LineOfCaller">65</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:58:00,591</td>
<td class="Message">注入字典到JSON数据  耗时0ms</td>
<td class="MethodOfCaller">doAround</td>
<td class="FileOfCaller">DictAspect.java</td>
<td class="LineOfCaller">69</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:58:05,234</td>
<td class="Message">【系统 WebSocket】连接断开，总数为:0</td>
<td class="MethodOfCaller">onClose</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">52</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:58:05,870</td>
<td class="Message">获取JSON数据 耗时：93ms</td>
<td class="MethodOfCaller">doAround</td>
<td class="FileOfCaller">DictAspect.java</td>
<td class="LineOfCaller">65</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:58:05,870</td>
<td class="Message">注入字典到JSON数据  耗时0ms</td>
<td class="MethodOfCaller">doAround</td>
<td class="FileOfCaller">DictAspect.java</td>
<td class="LineOfCaller">69</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:58:06,224</td>
<td class="Message">【系统 WebSocket】有新的连接，总数为:1</td>
<td class="MethodOfCaller">onOpen</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">43</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:58:06,290</td>
<td class="Message">获取JSON数据 耗时：64ms</td>
<td class="MethodOfCaller">doAround</td>
<td class="FileOfCaller">DictAspect.java</td>
<td class="LineOfCaller">65</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:58:06,291</td>
<td class="Message">注入字典到JSON数据  耗时0ms</td>
<td class="MethodOfCaller">doAround</td>
<td class="FileOfCaller">DictAspect.java</td>
<td class="LineOfCaller">69</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:58:11,064</td>
<td class="Message">查询磁盘信息:6个</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">117</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:58:11,070</td>
<td class="Message">{name=OS (C:), rest=185906327552, restPPT=56, max=429700067328}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:58:11,076</td>
<td class="Message">{name=本地磁盘 (D:), rest=138221797376, restPPT=48, max=268607250432}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:58:11,079</td>
<td class="Message">{name=本地磁盘 (E:), rest=57583968256, restPPT=61, max=151161372672}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:58:11,085</td>
<td class="Message">{name=本地磁盘 (F:), rest=69867331584, restPPT=53, max=150828929024}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:58:11,090</td>
<td class="Message">{name=本地磁盘 (G:), rest=137231179776, restPPT=72, max=500170412032}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:58:11,092</td>
<td class="Message">{name=本地磁盘 (H:), rest=260168253440, restPPT=47, max=500030472192}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:58:11,092</td>
<td class="Message">获取JSON数据 耗时：29ms</td>
<td class="MethodOfCaller">doAround</td>
<td class="FileOfCaller">DictAspect.java</td>
<td class="LineOfCaller">65</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:58:11,092</td>
<td class="Message">注入字典到JSON数据  耗时0ms</td>
<td class="MethodOfCaller">doAround</td>
<td class="FileOfCaller">DictAspect.java</td>
<td class="LineOfCaller">69</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:58:52,975</td>
<td class="Message">--getKeysSize--: {create_time=1755320332975, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:58:52,977</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755320332977}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:01,742</td>
<td class="Message">【系统 WebSocket】收到客户端消息:ping</td>
<td class="MethodOfCaller">onMessage</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:03,738</td>
<td class="Message">【系统 WebSocket】连接断开，总数为:0</td>
<td class="MethodOfCaller">onClose</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">52</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:09,755</td>
<td class="Message">【系统 WebSocket】有新的连接，总数为:1</td>
<td class="MethodOfCaller">onOpen</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">43</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,294</td>
<td class="Message">==&gt;  Preparing: SELECT * FROM kno_business_type WHERE parent_id = ? ORDER BY sort ASC, create_time ASC</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,295</td>
<td class="Message">==&gt; Parameters: 0(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,297</td>
<td class="Message">&lt;==      Total: 5</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,301</td>
<td class="Message">==&gt;  Preparing: SELECT * FROM kno_business_type WHERE parent_id = ? ORDER BY sort ASC, create_time ASC</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,301</td>
<td class="Message">==&gt; Parameters: 1949423093518999554(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,303</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,306</td>
<td class="Message">==&gt;  Preparing: SELECT * FROM kno_business_type WHERE parent_id = ? ORDER BY sort ASC, create_time ASC</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,307</td>
<td class="Message">==&gt; Parameters: 1948584181056360449(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,308</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,311</td>
<td class="Message">==&gt;  Preparing: SELECT * FROM kno_business_type WHERE parent_id = ? ORDER BY sort ASC, create_time ASC</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,311</td>
<td class="Message">==&gt; Parameters: 1949423158035783681(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,312</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,315</td>
<td class="Message">==&gt;  Preparing: SELECT * FROM kno_business_type WHERE parent_id = ? ORDER BY sort ASC, create_time ASC</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,316</td>
<td class="Message">==&gt; Parameters: 1949431759345311745(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,317</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,320</td>
<td class="Message">==&gt;  Preparing: SELECT * FROM kno_business_type WHERE parent_id = ? ORDER BY sort ASC, create_time ASC</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,320</td>
<td class="Message">==&gt; Parameters: 1949653561743642626(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,321</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,321</td>
<td class="Message">获取JSON数据 耗时：44ms</td>
<td class="MethodOfCaller">doAround</td>
<td class="FileOfCaller">DictAspect.java</td>
<td class="LineOfCaller">65</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,321</td>
<td class="Message">注入字典到JSON数据  耗时0ms</td>
<td class="MethodOfCaller">doAround</td>
<td class="FileOfCaller">DictAspect.java</td>
<td class="LineOfCaller">69</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,406</td>
<td class="Message">找到根机构：总公司, orgCategory: 1</td>
<td class="MethodOfCaller">findRootOrganRecursive</td>
<td class="FileOfCaller">KnoDeptService.java</td>
<td class="LineOfCaller">85</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,411</td>
<td class="Message">==&gt;  Preparing: SELECT COUNT(*) AS total FROM kno_base WHERE 1 = 1</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,413</td>
<td class="Message">==&gt; Parameters: </td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,417</td>
<td class="Message">&lt;==      Total: 1</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,419</td>
<td class="Message">==&gt;  Preparing: SELECT * FROM kno_base WHERE 1 = 1 ORDER BY create_time DESC LIMIT ?</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,419</td>
<td class="Message">==&gt; Parameters: 10(Long)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,420</td>
<td class="Message">&lt;==      Total: 7</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,422</td>
<td class="Message">==&gt;  Preparing: SELECT * FROM kno_base_detail WHERE knowledge_id = ?</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,424</td>
<td class="Message">==&gt; Parameters: 1950022388302213122(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,426</td>
<td class="Message">&lt;==      Total: 2</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,437</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,438</td>
<td class="Message">==&gt; Parameters: 1950022388302213122(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,439</td>
<td class="Message">&lt;==      Total: 1</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,443</td>
<td class="Message">==&gt;  Preparing: SELECT id, name, create_time FROM kno_tag WHERE id IN (?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,444</td>
<td class="Message">==&gt; Parameters: 3(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,446</td>
<td class="Message">&lt;==      Total: 1</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,447</td>
<td class="Message">==&gt;  Preparing: SELECT * FROM kno_base_detail WHERE knowledge_id = ?</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,447</td>
<td class="Message">==&gt; Parameters: 1949789424754593794(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,448</td>
<td class="Message">&lt;==      Total: 1</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,453</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,453</td>
<td class="Message">==&gt; Parameters: 1949789424754593794(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,453</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,456</td>
<td class="Message">==&gt;  Preparing: SELECT * FROM kno_base_detail WHERE knowledge_id = ?</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,456</td>
<td class="Message">==&gt; Parameters: 1949789248434442241(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,457</td>
<td class="Message">&lt;==      Total: 2</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,462</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,462</td>
<td class="Message">==&gt; Parameters: 1949789248434442241(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,463</td>
<td class="Message">&lt;==      Total: 1</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,466</td>
<td class="Message">==&gt;  Preparing: SELECT id, name, create_time FROM kno_tag WHERE id IN (?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,467</td>
<td class="Message">==&gt; Parameters: 3(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,467</td>
<td class="Message">&lt;==      Total: 1</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,468</td>
<td class="Message">==&gt;  Preparing: SELECT * FROM kno_base_detail WHERE knowledge_id = ?</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,468</td>
<td class="Message">==&gt; Parameters: 1949789097791819777(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,469</td>
<td class="Message">&lt;==      Total: 2</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,475</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,475</td>
<td class="Message">==&gt; Parameters: 1949789097791819777(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,476</td>
<td class="Message">&lt;==      Total: 1</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,480</td>
<td class="Message">==&gt;  Preparing: SELECT id, name, create_time FROM kno_tag WHERE id IN (?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,480</td>
<td class="Message">==&gt; Parameters: 3(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,481</td>
<td class="Message">&lt;==      Total: 1</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,482</td>
<td class="Message">==&gt;  Preparing: SELECT * FROM kno_base_detail WHERE knowledge_id = ?</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,482</td>
<td class="Message">==&gt; Parameters: 1949788930195820545(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,482</td>
<td class="Message">&lt;==      Total: 1</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,488</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,489</td>
<td class="Message">==&gt; Parameters: 1949788930195820545(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,490</td>
<td class="Message">&lt;==      Total: 2</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,493</td>
<td class="Message">==&gt;  Preparing: SELECT id, name, create_time FROM kno_tag WHERE id IN (?, ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,494</td>
<td class="Message">==&gt; Parameters: 2(String), 3(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,494</td>
<td class="Message">&lt;==      Total: 2</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,496</td>
<td class="Message">==&gt;  Preparing: SELECT * FROM kno_base_detail WHERE knowledge_id = ?</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,496</td>
<td class="Message">==&gt; Parameters: 1949672449877446657(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,498</td>
<td class="Message">&lt;==      Total: 5</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,503</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,503</td>
<td class="Message">==&gt; Parameters: 1949672449877446657(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,503</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,506</td>
<td class="Message">==&gt;  Preparing: SELECT * FROM kno_base_detail WHERE knowledge_id = ?</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,506</td>
<td class="Message">==&gt; Parameters: 1949666866969415681(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,513</td>
<td class="Message">&lt;==      Total: 23</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,520</td>
<td class="Message">==&gt;  Preparing: SELECT id, name, parent_id, level, sort, status, create_time, update_time FROM kno_category WHERE id = ?</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,521</td>
<td class="Message">==&gt; Parameters: (String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,522</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,526</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,527</td>
<td class="Message">==&gt; Parameters: 1949666866969415681(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,527</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,528</td>
<td class="Message">获取JSON数据 耗时：144ms</td>
<td class="MethodOfCaller">doAround</td>
<td class="FileOfCaller">DictAspect.java</td>
<td class="LineOfCaller">65</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,528</td>
<td class="Message"> __ 进入字典翻译切面 DictAspect —— </td>
<td class="MethodOfCaller">parseDictText</td>
<td class="FileOfCaller">DictAspect.java</td>
<td class="LineOfCaller">112</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,615</td>
<td class="Message">translateManyDict.dictCodes:code</td>
<td class="MethodOfCaller">translateAllDict</td>
<td class="FileOfCaller">DictAspect.java</td>
<td class="LineOfCaller">363</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,615</td>
<td class="Message">translateManyDict.values:1,2,3</td>
<td class="MethodOfCaller">translateAllDict</td>
<td class="FileOfCaller">DictAspect.java</td>
<td class="LineOfCaller">364</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,623</td>
<td class="Message">translateManyDict.result:{}</td>
<td class="MethodOfCaller">translateAllDict</td>
<td class="FileOfCaller">DictAspect.java</td>
<td class="LineOfCaller">366</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:31,644</td>
<td class="Message">注入字典到JSON数据  耗时116ms</td>
<td class="MethodOfCaller">doAround</td>
<td class="FileOfCaller">DictAspect.java</td>
<td class="LineOfCaller">69</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:59:33,349</td>
<td class="Message">查询磁盘信息:6个</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">117</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:59:33,353</td>
<td class="Message">{name=OS (C:), rest=185903562752, restPPT=56, max=429700067328}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:59:33,365</td>
<td class="Message">{name=本地磁盘 (D:), rest=138221797376, restPPT=48, max=268607250432}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:59:33,370</td>
<td class="Message">{name=本地磁盘 (E:), rest=57583833088, restPPT=61, max=151161372672}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:59:33,374</td>
<td class="Message">{name=本地磁盘 (F:), rest=69867331584, restPPT=53, max=150828929024}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:59:33,376</td>
<td class="Message">{name=本地磁盘 (G:), rest=137231179776, restPPT=72, max=500170412032}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:59:33,380</td>
<td class="Message">{name=本地磁盘 (H:), rest=260168253440, restPPT=47, max=500030472192}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:33,380</td>
<td class="Message">获取JSON数据 耗时：31ms</td>
<td class="MethodOfCaller">doAround</td>
<td class="FileOfCaller">DictAspect.java</td>
<td class="LineOfCaller">65</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:33,380</td>
<td class="Message">注入字典到JSON数据  耗时0ms</td>
<td class="MethodOfCaller">doAround</td>
<td class="FileOfCaller">DictAspect.java</td>
<td class="LineOfCaller">69</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:52,965</td>
<td class="Message">--getKeysSize--: {create_time=1755320392965, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 12:59:52,966</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755320392966}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:00:05,746</td>
<td class="Message">【系统 WebSocket】收到客户端消息:ping</td>
<td class="MethodOfCaller">onMessage</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:00:07,735</td>
<td class="Message">【系统 WebSocket】连接断开，总数为:0</td>
<td class="MethodOfCaller">onClose</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">52</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:00:13,755</td>
<td class="Message">【系统 WebSocket】有新的连接，总数为:1</td>
<td class="MethodOfCaller">onOpen</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">43</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:00:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755320452964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:00:52,966</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755320452966}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:01:09,743</td>
<td class="Message">【系统 WebSocket】收到客户端消息:ping</td>
<td class="MethodOfCaller">onMessage</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:01:11,747</td>
<td class="Message">【系统 WebSocket】连接断开，总数为:0</td>
<td class="MethodOfCaller">onClose</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">52</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:01:17,744</td>
<td class="Message">【系统 WebSocket】有新的连接，总数为:1</td>
<td class="MethodOfCaller">onOpen</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">43</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:01:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755320512964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:01:52,965</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755320512965}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:02:13,736</td>
<td class="Message">【系统 WebSocket】收到客户端消息:ping</td>
<td class="MethodOfCaller">onMessage</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:02:15,734</td>
<td class="Message">【系统 WebSocket】连接断开，总数为:0</td>
<td class="MethodOfCaller">onClose</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">52</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:02:21,751</td>
<td class="Message">【系统 WebSocket】有新的连接，总数为:1</td>
<td class="MethodOfCaller">onOpen</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">43</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:02:52,963</td>
<td class="Message">--getKeysSize--: {create_time=1755320572963, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:02:52,965</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755320572965}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:03:17,736</td>
<td class="Message">【系统 WebSocket】收到客户端消息:ping</td>
<td class="MethodOfCaller">onMessage</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:03:19,741</td>
<td class="Message">【系统 WebSocket】连接断开，总数为:0</td>
<td class="MethodOfCaller">onClose</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">52</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:03:25,747</td>
<td class="Message">【系统 WebSocket】有新的连接，总数为:1</td>
<td class="MethodOfCaller">onOpen</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">43</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:03:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755320632964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:03:52,965</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755320632965}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:21,737</td>
<td class="Message">【系统 WebSocket】收到客户端消息:ping</td>
<td class="MethodOfCaller">onMessage</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:23,734</td>
<td class="Message">【系统 WebSocket】连接断开，总数为:0</td>
<td class="MethodOfCaller">onClose</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">52</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:29,748</td>
<td class="Message">【系统 WebSocket】有新的连接，总数为:1</td>
<td class="MethodOfCaller">onOpen</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">43</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 13:04:52,963</td>
<td class="Message">开始同步KnoBaseDetail数据到Solr...</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">490</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755320692964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:52,966</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755320692966}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:52,973</td>
<td class="Message">==&gt;  Preparing: SELECT id, title, content, ocr_content, srcipt, srcipt_simplify, category_id, parent_id, knowledge_id, keywords, view_count, status, source_type, creator_name, updater_name, create_time, update_time, organ_name, organ_id, single_image_file_path, from_detail_page_num, from_detail_page_id, order_num FROM kno_base_detail WHERE (status &lt;&gt; ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:52,973</td>
<td class="Message">==&gt; Parameters: 0(Integer)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:52,979</td>
<td class="Message">&lt;==      Total: 28</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:52,983</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:52,983</td>
<td class="Message">==&gt; Parameters: 1949666867036524546(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:52,984</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:52,988</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:52,988</td>
<td class="Message">==&gt; Parameters: 1949666867103633409(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:52,989</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:52,993</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:52,994</td>
<td class="Message">==&gt; Parameters: 1949666867103633410(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:52,994</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:52,998</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:52,998</td>
<td class="Message">==&gt; Parameters: 1949666867103633411(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:52,998</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,004</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,004</td>
<td class="Message">==&gt; Parameters: 1949666867170742274(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,004</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,007</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,008</td>
<td class="Message">==&gt; Parameters: 1949666867170742275(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,008</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,011</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,011</td>
<td class="Message">==&gt; Parameters: 1949666867170742276(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,011</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,015</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,015</td>
<td class="Message">==&gt; Parameters: 1949666867237851138(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,017</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,020</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,020</td>
<td class="Message">==&gt; Parameters: 1949666867246239745(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,021</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,024</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,024</td>
<td class="Message">==&gt; Parameters: 1949666867246239746(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,024</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,027</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,028</td>
<td class="Message">==&gt; Parameters: 1949666867246239747(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,028</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,031</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,031</td>
<td class="Message">==&gt; Parameters: 1949666867309154305(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,032</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,037</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,037</td>
<td class="Message">==&gt; Parameters: 1949666867309154306(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,037</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,041</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,041</td>
<td class="Message">==&gt; Parameters: 1949666867309154307(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,042</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,045</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,045</td>
<td class="Message">==&gt; Parameters: 1949666867388846081(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,046</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,049</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,049</td>
<td class="Message">==&gt; Parameters: 1949666867388846082(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,049</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,053</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,053</td>
<td class="Message">==&gt; Parameters: 1949666867388846083(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,055</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,058</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,058</td>
<td class="Message">==&gt; Parameters: 1949666867388846084(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,058</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,063</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,063</td>
<td class="Message">==&gt; Parameters: 1949666867460149249(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,064</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,067</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,067</td>
<td class="Message">==&gt; Parameters: 1949666867460149250(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,069</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,072</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,072</td>
<td class="Message">==&gt; Parameters: 1949666867460149251(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,074</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,078</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,078</td>
<td class="Message">==&gt; Parameters: 1949666867535646721(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,079</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,084</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,084</td>
<td class="Message">==&gt; Parameters: 1949666867535646722(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,084</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,088</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,090</td>
<td class="Message">==&gt; Parameters: 1949672451836186626(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,090</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,096</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,096</td>
<td class="Message">==&gt; Parameters: 1949672452326920193(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,096</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,101</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,101</td>
<td class="Message">==&gt; Parameters: 1949672452607938561(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,103</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,108</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,108</td>
<td class="Message">==&gt; Parameters: 1949672452817653761(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,110</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,115</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,116</td>
<td class="Message">==&gt; Parameters: 1949672453232889857(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:04:53,117</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-08-16 13:04:53,121</td>
<td class="Message">同步KnoBaseDetail数据到Solr失败</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">507</td>
</tr>
<tr><td class="Exception" colspan="6">org.springframework.dao.DataAccessResourceFailureException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrExceptionTranslator.translateExceptionIfPossible(SolrExceptionTranslator.java:79)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:169)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.delete(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrOperations.delete(SolrOperations.java:228)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.repository.support.SimpleSolrRepository.deleteAll(SimpleSolrRepository.java:212)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService.deleteAll(KnoBaseDetailSolrService.java:313)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$FastClassBySpringCGLIB$$1d6fbb9b.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$EnhancerBySpringCGLIB$$daa8b9a9.deleteAll(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailService.syncToSolr(KnoBaseDetailService.java:497)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.Thread.run(Thread.java:833)
<br />Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:940)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:903)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.lambda$delete$6(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:167)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 65 common frames omitted
<br />Caused by: java.net.ConnectException: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnect(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.Socket.connect(Socket.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 82 common frames omitted
</td></tr>
<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:05:25,744</td>
<td class="Message">【系统 WebSocket】收到客户端消息:ping</td>
<td class="MethodOfCaller">onMessage</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:05:27,736</td>
<td class="Message">【系统 WebSocket】连接断开，总数为:0</td>
<td class="MethodOfCaller">onClose</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">52</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:05:33,744</td>
<td class="Message">【系统 WebSocket】有新的连接，总数为:1</td>
<td class="MethodOfCaller">onOpen</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">43</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:05:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755320752964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:05:52,966</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755320752966}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:06:29,733</td>
<td class="Message">【系统 WebSocket】收到客户端消息:ping</td>
<td class="MethodOfCaller">onMessage</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:06:31,738</td>
<td class="Message">【系统 WebSocket】连接断开，总数为:0</td>
<td class="MethodOfCaller">onClose</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">52</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:06:37,753</td>
<td class="Message">【系统 WebSocket】有新的连接，总数为:1</td>
<td class="MethodOfCaller">onOpen</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">43</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:06:52,965</td>
<td class="Message">--getKeysSize--: {create_time=1755320812965, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:06:52,966</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755320812966}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:07:33,737</td>
<td class="Message">【系统 WebSocket】收到客户端消息:ping</td>
<td class="MethodOfCaller">onMessage</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:07:35,741</td>
<td class="Message">【系统 WebSocket】连接断开，总数为:0</td>
<td class="MethodOfCaller">onClose</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">52</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:07:41,747</td>
<td class="Message">【系统 WebSocket】有新的连接，总数为:1</td>
<td class="MethodOfCaller">onOpen</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">43</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:07:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755320872964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:07:52,965</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755320872965}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:08:37,744</td>
<td class="Message">【系统 WebSocket】收到客户端消息:ping</td>
<td class="MethodOfCaller">onMessage</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:08:39,735</td>
<td class="Message">【系统 WebSocket】连接断开，总数为:0</td>
<td class="MethodOfCaller">onClose</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">52</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:08:52,963</td>
<td class="Message">--getKeysSize--: {create_time=1755320932963, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:08:52,965</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755320932965}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:09:52,963</td>
<td class="Message">--getKeysSize--: {create_time=1755320992963, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:09:52,965</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755320992965}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:10:52,965</td>
<td class="Message">--getKeysSize--: {create_time=1755321052965, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:10:52,969</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755321052969}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:11:52,966</td>
<td class="Message">--getKeysSize--: {create_time=1755321112966, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:11:52,969</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755321112969}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:12:52,963</td>
<td class="Message">--getKeysSize--: {create_time=1755321172963, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:12:52,966</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755321172966}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:13:46,925</td>
<td class="Message">获取JSON数据 耗时：83ms</td>
<td class="MethodOfCaller">doAround</td>
<td class="FileOfCaller">DictAspect.java</td>
<td class="LineOfCaller">65</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:13:46,925</td>
<td class="Message">注入字典到JSON数据  耗时0ms</td>
<td class="MethodOfCaller">doAround</td>
<td class="FileOfCaller">DictAspect.java</td>
<td class="LineOfCaller">69</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:13:52,051</td>
<td class="Message">【系统 WebSocket】有新的连接，总数为:1</td>
<td class="MethodOfCaller">onOpen</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">43</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:13:52,094</td>
<td class="Message">获取JSON数据 耗时：37ms</td>
<td class="MethodOfCaller">doAround</td>
<td class="FileOfCaller">DictAspect.java</td>
<td class="LineOfCaller">65</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:13:52,094</td>
<td class="Message">注入字典到JSON数据  耗时0ms</td>
<td class="MethodOfCaller">doAround</td>
<td class="FileOfCaller">DictAspect.java</td>
<td class="LineOfCaller">69</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:13:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755321232964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:13:52,965</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755321232965}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:14:47,741</td>
<td class="Message">【系统 WebSocket】收到客户端消息:ping</td>
<td class="MethodOfCaller">onMessage</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:14:49,746</td>
<td class="Message">【系统 WebSocket】连接断开，总数为:0</td>
<td class="MethodOfCaller">onClose</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">52</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:14:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755321292964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:14:52,966</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755321292966}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:14:55,753</td>
<td class="Message">【系统 WebSocket】有新的连接，总数为:1</td>
<td class="MethodOfCaller">onOpen</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">43</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:15:51,744</td>
<td class="Message">【系统 WebSocket】收到客户端消息:ping</td>
<td class="MethodOfCaller">onMessage</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:15:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755321352964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:15:52,965</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755321352965}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:15:53,739</td>
<td class="Message">【系统 WebSocket】连接断开，总数为:0</td>
<td class="MethodOfCaller">onClose</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">52</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:15:59,756</td>
<td class="Message">【系统 WebSocket】有新的连接，总数为:1</td>
<td class="MethodOfCaller">onOpen</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">43</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:16:52,965</td>
<td class="Message">--getKeysSize--: {create_time=1755321412965, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:16:52,967</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755321412967}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:16:55,741</td>
<td class="Message">【系统 WebSocket】收到客户端消息:ping</td>
<td class="MethodOfCaller">onMessage</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:16:57,741</td>
<td class="Message">【系统 WebSocket】连接断开，总数为:0</td>
<td class="MethodOfCaller">onClose</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">52</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:17:03,777</td>
<td class="Message">【系统 WebSocket】有新的连接，总数为:1</td>
<td class="MethodOfCaller">onOpen</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">43</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:17:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755321472964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:17:52,965</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755321472965}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:17:59,738</td>
<td class="Message">【系统 WebSocket】收到客户端消息:ping</td>
<td class="MethodOfCaller">onMessage</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:18:01,733</td>
<td class="Message">【系统 WebSocket】连接断开，总数为:0</td>
<td class="MethodOfCaller">onClose</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">52</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:18:07,750</td>
<td class="Message">【系统 WebSocket】有新的连接，总数为:1</td>
<td class="MethodOfCaller">onOpen</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">43</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:18:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755321532964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:18:52,965</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755321532965}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:03,736</td>
<td class="Message">【系统 WebSocket】收到客户端消息:ping</td>
<td class="MethodOfCaller">onMessage</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:05,732</td>
<td class="Message">【系统 WebSocket】连接断开，总数为:0</td>
<td class="MethodOfCaller">onClose</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">52</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:11,745</td>
<td class="Message">【系统 WebSocket】有新的连接，总数为:1</td>
<td class="MethodOfCaller">onOpen</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">43</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 13:19:52,963</td>
<td class="Message">开始同步KnoBaseDetail数据到Solr...</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">490</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755321592964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:52,966</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755321592966}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:52,974</td>
<td class="Message">==&gt;  Preparing: SELECT id, title, content, ocr_content, srcipt, srcipt_simplify, category_id, parent_id, knowledge_id, keywords, view_count, status, source_type, creator_name, updater_name, create_time, update_time, organ_name, organ_id, single_image_file_path, from_detail_page_num, from_detail_page_id, order_num FROM kno_base_detail WHERE (status &lt;&gt; ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:52,974</td>
<td class="Message">==&gt; Parameters: 0(Integer)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:52,979</td>
<td class="Message">&lt;==      Total: 28</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:52,984</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:52,984</td>
<td class="Message">==&gt; Parameters: 1949666867036524546(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:52,985</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:52,989</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:52,989</td>
<td class="Message">==&gt; Parameters: 1949666867103633409(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:52,989</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:52,993</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:52,993</td>
<td class="Message">==&gt; Parameters: 1949666867103633410(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:52,994</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:52,998</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:52,998</td>
<td class="Message">==&gt; Parameters: 1949666867103633411(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:52,998</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,002</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,002</td>
<td class="Message">==&gt; Parameters: 1949666867170742274(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,002</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,005</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,006</td>
<td class="Message">==&gt; Parameters: 1949666867170742275(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,006</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,010</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,010</td>
<td class="Message">==&gt; Parameters: 1949666867170742276(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,011</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,014</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,014</td>
<td class="Message">==&gt; Parameters: 1949666867237851138(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,015</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,019</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,019</td>
<td class="Message">==&gt; Parameters: 1949666867246239745(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,020</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,023</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,023</td>
<td class="Message">==&gt; Parameters: 1949666867246239746(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,024</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,027</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,028</td>
<td class="Message">==&gt; Parameters: 1949666867246239747(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,028</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,032</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,032</td>
<td class="Message">==&gt; Parameters: 1949666867309154305(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,034</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,037</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,039</td>
<td class="Message">==&gt; Parameters: 1949666867309154306(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,039</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,042</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,042</td>
<td class="Message">==&gt; Parameters: 1949666867309154307(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,043</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,047</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,047</td>
<td class="Message">==&gt; Parameters: 1949666867388846081(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,048</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,052</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,052</td>
<td class="Message">==&gt; Parameters: 1949666867388846082(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,053</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,057</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,057</td>
<td class="Message">==&gt; Parameters: 1949666867388846083(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,058</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,062</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,062</td>
<td class="Message">==&gt; Parameters: 1949666867388846084(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,062</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,065</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,065</td>
<td class="Message">==&gt; Parameters: 1949666867460149249(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,067</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,070</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,070</td>
<td class="Message">==&gt; Parameters: 1949666867460149250(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,071</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,074</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,074</td>
<td class="Message">==&gt; Parameters: 1949666867460149251(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,074</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,078</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,078</td>
<td class="Message">==&gt; Parameters: 1949666867535646721(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,078</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,083</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,083</td>
<td class="Message">==&gt; Parameters: 1949666867535646722(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,083</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,087</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,088</td>
<td class="Message">==&gt; Parameters: 1949672451836186626(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,088</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,092</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,092</td>
<td class="Message">==&gt; Parameters: 1949672452326920193(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,092</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,096</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,096</td>
<td class="Message">==&gt; Parameters: 1949672452607938561(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,096</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,100</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,100</td>
<td class="Message">==&gt; Parameters: 1949672452817653761(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,100</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,105</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,105</td>
<td class="Message">==&gt; Parameters: 1949672453232889857(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:19:53,105</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-08-16 13:19:53,120</td>
<td class="Message">同步KnoBaseDetail数据到Solr失败</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">507</td>
</tr>
<tr><td class="Exception" colspan="6">org.springframework.dao.DataAccessResourceFailureException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrExceptionTranslator.translateExceptionIfPossible(SolrExceptionTranslator.java:79)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:169)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.delete(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrOperations.delete(SolrOperations.java:228)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.repository.support.SimpleSolrRepository.deleteAll(SimpleSolrRepository.java:212)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService.deleteAll(KnoBaseDetailSolrService.java:313)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$FastClassBySpringCGLIB$$1d6fbb9b.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$EnhancerBySpringCGLIB$$daa8b9a9.deleteAll(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailService.syncToSolr(KnoBaseDetailService.java:497)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.Thread.run(Thread.java:833)
<br />Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:940)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:903)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.lambda$delete$6(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:167)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 65 common frames omitted
<br />Caused by: java.net.ConnectException: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnect(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.Socket.connect(Socket.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 82 common frames omitted
</td></tr>
<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:20:07,740</td>
<td class="Message">【系统 WebSocket】收到客户端消息:ping</td>
<td class="MethodOfCaller">onMessage</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:20:09,747</td>
<td class="Message">【系统 WebSocket】连接断开，总数为:0</td>
<td class="MethodOfCaller">onClose</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">52</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:20:15,756</td>
<td class="Message">【系统 WebSocket】有新的连接，总数为:1</td>
<td class="MethodOfCaller">onOpen</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">43</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:20:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755321652964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:20:52,965</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755321652965}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:21:11,736</td>
<td class="Message">【系统 WebSocket】收到客户端消息:ping</td>
<td class="MethodOfCaller">onMessage</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:21:13,740</td>
<td class="Message">【系统 WebSocket】连接断开，总数为:0</td>
<td class="MethodOfCaller">onClose</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">52</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:21:19,749</td>
<td class="Message">【系统 WebSocket】有新的连接，总数为:1</td>
<td class="MethodOfCaller">onOpen</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">43</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:21:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755321712964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:21:52,966</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755321712966}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:22:15,741</td>
<td class="Message">【系统 WebSocket】收到客户端消息:ping</td>
<td class="MethodOfCaller">onMessage</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:22:17,743</td>
<td class="Message">【系统 WebSocket】连接断开，总数为:0</td>
<td class="MethodOfCaller">onClose</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">52</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:22:23,752</td>
<td class="Message">【系统 WebSocket】有新的连接，总数为:1</td>
<td class="MethodOfCaller">onOpen</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">43</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:22:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755321772964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:22:52,965</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755321772965}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:23:19,735</td>
<td class="Message">【系统 WebSocket】收到客户端消息:ping</td>
<td class="MethodOfCaller">onMessage</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:23:21,738</td>
<td class="Message">【系统 WebSocket】连接断开，总数为:0</td>
<td class="MethodOfCaller">onClose</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">52</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:23:27,753</td>
<td class="Message">【系统 WebSocket】有新的连接，总数为:1</td>
<td class="MethodOfCaller">onOpen</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">43</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:23:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755321832964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:23:52,965</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755321832965}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:24:23,731</td>
<td class="Message">【系统 WebSocket】收到客户端消息:ping</td>
<td class="MethodOfCaller">onMessage</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:24:25,741</td>
<td class="Message">【系统 WebSocket】连接断开，总数为:0</td>
<td class="MethodOfCaller">onClose</td>
<td class="FileOfCaller">WebSocket.java</td>
<td class="LineOfCaller">52</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:24:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755321892964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:24:52,966</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755321892966}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:25:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755321952964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:25:52,965</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755321952965}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:26:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755322012964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:26:52,966</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755322012966}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:27:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755322072964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:27:52,966</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755322072966}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:28:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755322132964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:28:52,966</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755322132966}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:29:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755322192964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:29:52,966</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755322192966}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:30:52,965</td>
<td class="Message">--getKeysSize--: {create_time=1755322252965, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:30:52,967</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755322252967}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:31:52,967</td>
<td class="Message">--getKeysSize--: {create_time=1755322312967, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:31:52,969</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755322312969}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:32:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755322372964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:32:52,966</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755322372966}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:33:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755322432964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:33:52,965</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755322432965}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 13:34:52,963</td>
<td class="Message">开始同步KnoBaseDetail数据到Solr...</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">490</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755322492964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:52,967</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755322492965}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:52,980</td>
<td class="Message">==&gt;  Preparing: SELECT id, title, content, ocr_content, srcipt, srcipt_simplify, category_id, parent_id, knowledge_id, keywords, view_count, status, source_type, creator_name, updater_name, create_time, update_time, organ_name, organ_id, single_image_file_path, from_detail_page_num, from_detail_page_id, order_num FROM kno_base_detail WHERE (status &lt;&gt; ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:52,980</td>
<td class="Message">==&gt; Parameters: 0(Integer)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:52,987</td>
<td class="Message">&lt;==      Total: 28</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:52,992</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:52,993</td>
<td class="Message">==&gt; Parameters: 1949666867036524546(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:52,993</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:52,999</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:52,999</td>
<td class="Message">==&gt; Parameters: 1949666867103633409(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:52,999</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,005</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,006</td>
<td class="Message">==&gt; Parameters: 1949666867103633410(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,007</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,014</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,014</td>
<td class="Message">==&gt; Parameters: 1949666867103633411(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,015</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,021</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,021</td>
<td class="Message">==&gt; Parameters: 1949666867170742274(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,022</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,028</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,028</td>
<td class="Message">==&gt; Parameters: 1949666867170742275(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,029</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,034</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,034</td>
<td class="Message">==&gt; Parameters: 1949666867170742276(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,034</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,041</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,041</td>
<td class="Message">==&gt; Parameters: 1949666867237851138(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,041</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,046</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,046</td>
<td class="Message">==&gt; Parameters: 1949666867246239745(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,047</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,051</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,051</td>
<td class="Message">==&gt; Parameters: 1949666867246239746(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,051</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,057</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,057</td>
<td class="Message">==&gt; Parameters: 1949666867246239747(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,057</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,062</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,062</td>
<td class="Message">==&gt; Parameters: 1949666867309154305(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,063</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,065</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,067</td>
<td class="Message">==&gt; Parameters: 1949666867309154306(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,067</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,072</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,072</td>
<td class="Message">==&gt; Parameters: 1949666867309154307(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,072</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,077</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,077</td>
<td class="Message">==&gt; Parameters: 1949666867388846081(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,078</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,082</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,082</td>
<td class="Message">==&gt; Parameters: 1949666867388846082(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,084</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,088</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,088</td>
<td class="Message">==&gt; Parameters: 1949666867388846083(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,088</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,095</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,095</td>
<td class="Message">==&gt; Parameters: 1949666867388846084(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,096</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,100</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,100</td>
<td class="Message">==&gt; Parameters: 1949666867460149249(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,101</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,108</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,108</td>
<td class="Message">==&gt; Parameters: 1949666867460149250(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,109</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,115</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,115</td>
<td class="Message">==&gt; Parameters: 1949666867460149251(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,116</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,120</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,120</td>
<td class="Message">==&gt; Parameters: 1949666867535646721(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,121</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,126</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,126</td>
<td class="Message">==&gt; Parameters: 1949666867535646722(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,126</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,131</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,131</td>
<td class="Message">==&gt; Parameters: 1949672451836186626(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,131</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,136</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,136</td>
<td class="Message">==&gt; Parameters: 1949672452326920193(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,136</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,140</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,140</td>
<td class="Message">==&gt; Parameters: 1949672452607938561(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,141</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,148</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,148</td>
<td class="Message">==&gt; Parameters: 1949672452817653761(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,149</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,153</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,153</td>
<td class="Message">==&gt; Parameters: 1949672453232889857(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:34:53,154</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-08-16 13:34:53,167</td>
<td class="Message">同步KnoBaseDetail数据到Solr失败</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">507</td>
</tr>
<tr><td class="Exception" colspan="6">org.springframework.dao.DataAccessResourceFailureException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrExceptionTranslator.translateExceptionIfPossible(SolrExceptionTranslator.java:79)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:169)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.delete(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrOperations.delete(SolrOperations.java:228)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.repository.support.SimpleSolrRepository.deleteAll(SimpleSolrRepository.java:212)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService.deleteAll(KnoBaseDetailSolrService.java:313)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$FastClassBySpringCGLIB$$1d6fbb9b.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$EnhancerBySpringCGLIB$$daa8b9a9.deleteAll(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailService.syncToSolr(KnoBaseDetailService.java:497)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.Thread.run(Thread.java:833)
<br />Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:940)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:903)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.lambda$delete$6(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:167)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 65 common frames omitted
<br />Caused by: java.net.ConnectException: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnect(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.Socket.connect(Socket.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 82 common frames omitted
</td></tr>
<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:35:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755322552964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:35:52,967</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755322552967}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:36:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755322612964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:36:52,967</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755322612967}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:37:52,965</td>
<td class="Message">--getKeysSize--: {create_time=1755322672965, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:37:52,966</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755322672966}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:38:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755322732964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:38:52,966</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755322732966}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:39:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755322792964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:39:52,966</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755322792966}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:40:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755322852964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:40:52,966</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755322852966}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:41:52,965</td>
<td class="Message">--getKeysSize--: {create_time=1755322912965, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:41:52,967</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755322912967}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:42:52,965</td>
<td class="Message">--getKeysSize--: {create_time=1755322972965, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:42:52,967</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755322972967}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:43:52,965</td>
<td class="Message">--getKeysSize--: {create_time=1755323032965, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:43:52,967</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755323032967}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:44:52,967</td>
<td class="Message">--getKeysSize--: {create_time=1755323092967, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:44:52,970</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755323092970}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:45:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755323152964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:45:52,966</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755323152966}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:46:52,965</td>
<td class="Message">--getKeysSize--: {create_time=1755323212965, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:46:52,969</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755323212969}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:47:52,966</td>
<td class="Message">--getKeysSize--: {create_time=1755323272966, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:47:52,968</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755323272968}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:48:52,965</td>
<td class="Message">--getKeysSize--: {create_time=1755323332965, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:48:52,967</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755323332967}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 13:49:52,963</td>
<td class="Message">开始同步KnoBaseDetail数据到Solr...</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">490</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755323392964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:52,966</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755323392966}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:52,975</td>
<td class="Message">==&gt;  Preparing: SELECT id, title, content, ocr_content, srcipt, srcipt_simplify, category_id, parent_id, knowledge_id, keywords, view_count, status, source_type, creator_name, updater_name, create_time, update_time, organ_name, organ_id, single_image_file_path, from_detail_page_num, from_detail_page_id, order_num FROM kno_base_detail WHERE (status &lt;&gt; ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:52,975</td>
<td class="Message">==&gt; Parameters: 0(Integer)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:52,982</td>
<td class="Message">&lt;==      Total: 28</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:52,987</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:52,988</td>
<td class="Message">==&gt; Parameters: 1949666867036524546(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:52,988</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:52,993</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:52,994</td>
<td class="Message">==&gt; Parameters: 1949666867103633409(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:52,994</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,001</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,001</td>
<td class="Message">==&gt; Parameters: 1949666867103633410(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,002</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,007</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,007</td>
<td class="Message">==&gt; Parameters: 1949666867103633411(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,008</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,011</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,011</td>
<td class="Message">==&gt; Parameters: 1949666867170742274(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,013</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,018</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,018</td>
<td class="Message">==&gt; Parameters: 1949666867170742275(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,019</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,022</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,022</td>
<td class="Message">==&gt; Parameters: 1949666867170742276(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,022</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,026</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,027</td>
<td class="Message">==&gt; Parameters: 1949666867237851138(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,027</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,031</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,031</td>
<td class="Message">==&gt; Parameters: 1949666867246239745(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,032</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,036</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,037</td>
<td class="Message">==&gt; Parameters: 1949666867246239746(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,037</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,042</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,042</td>
<td class="Message">==&gt; Parameters: 1949666867246239747(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,043</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,048</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,048</td>
<td class="Message">==&gt; Parameters: 1949666867309154305(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,049</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,052</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,053</td>
<td class="Message">==&gt; Parameters: 1949666867309154306(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,053</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,057</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,057</td>
<td class="Message">==&gt; Parameters: 1949666867309154307(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,057</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,061</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,061</td>
<td class="Message">==&gt; Parameters: 1949666867388846081(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,061</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,066</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,066</td>
<td class="Message">==&gt; Parameters: 1949666867388846082(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,066</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,069</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,069</td>
<td class="Message">==&gt; Parameters: 1949666867388846083(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,069</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,074</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,075</td>
<td class="Message">==&gt; Parameters: 1949666867388846084(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,075</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,080</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,081</td>
<td class="Message">==&gt; Parameters: 1949666867460149249(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,081</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,086</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,086</td>
<td class="Message">==&gt; Parameters: 1949666867460149250(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,086</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,090</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,090</td>
<td class="Message">==&gt; Parameters: 1949666867460149251(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,091</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,094</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,094</td>
<td class="Message">==&gt; Parameters: 1949666867535646721(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,096</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,099</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,099</td>
<td class="Message">==&gt; Parameters: 1949666867535646722(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,100</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,103</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,104</td>
<td class="Message">==&gt; Parameters: 1949672451836186626(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,104</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,108</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,109</td>
<td class="Message">==&gt; Parameters: 1949672452326920193(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,109</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,113</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,113</td>
<td class="Message">==&gt; Parameters: 1949672452607938561(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,114</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,117</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,117</td>
<td class="Message">==&gt; Parameters: 1949672452817653761(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,117</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,121</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,122</td>
<td class="Message">==&gt; Parameters: 1949672453232889857(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:49:53,122</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-08-16 13:49:53,126</td>
<td class="Message">同步KnoBaseDetail数据到Solr失败</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">507</td>
</tr>
<tr><td class="Exception" colspan="6">org.springframework.dao.DataAccessResourceFailureException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrExceptionTranslator.translateExceptionIfPossible(SolrExceptionTranslator.java:79)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:169)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.delete(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrOperations.delete(SolrOperations.java:228)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.repository.support.SimpleSolrRepository.deleteAll(SimpleSolrRepository.java:212)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService.deleteAll(KnoBaseDetailSolrService.java:313)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$FastClassBySpringCGLIB$$1d6fbb9b.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$EnhancerBySpringCGLIB$$daa8b9a9.deleteAll(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailService.syncToSolr(KnoBaseDetailService.java:497)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.Thread.run(Thread.java:833)
<br />Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:940)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:903)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.lambda$delete$6(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:167)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 65 common frames omitted
<br />Caused by: java.net.ConnectException: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnect(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.Socket.connect(Socket.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 82 common frames omitted
</td></tr>
<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:50:52,965</td>
<td class="Message">--getKeysSize--: {create_time=1755323452965, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:50:52,966</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755323452966}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:51:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755323512964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:51:52,965</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755323512965}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:52:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755323572964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:52:52,966</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755323572966}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:53:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755323632964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:53:52,968</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755323632968}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:54:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755323692964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:54:52,966</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755323692966}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:55:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755323752964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:55:52,966</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755323752966}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:56:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755323812964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:56:52,966</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755323812966}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:57:52,965</td>
<td class="Message">--getKeysSize--: {create_time=1755323872965, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:57:52,968</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755323872968}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:58:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755323932964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:58:52,966</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755323932966}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:59:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755323992964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 13:59:52,966</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755323992966}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:00:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755324052964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:00:52,966</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755324052966}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:01:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755324112964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:01:52,965</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755324112965}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:02:52,965</td>
<td class="Message">--getKeysSize--: {create_time=1755324172965, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:02:52,968</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755324172968}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:03:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755324232964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:03:52,965</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755324232965}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 14:04:52,963</td>
<td class="Message">开始同步KnoBaseDetail数据到Solr...</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">490</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755324292964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:52,966</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755324292966}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:52,972</td>
<td class="Message">==&gt;  Preparing: SELECT id, title, content, ocr_content, srcipt, srcipt_simplify, category_id, parent_id, knowledge_id, keywords, view_count, status, source_type, creator_name, updater_name, create_time, update_time, organ_name, organ_id, single_image_file_path, from_detail_page_num, from_detail_page_id, order_num FROM kno_base_detail WHERE (status &lt;&gt; ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:52,974</td>
<td class="Message">==&gt; Parameters: 0(Integer)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:52,980</td>
<td class="Message">&lt;==      Total: 28</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:52,984</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:52,984</td>
<td class="Message">==&gt; Parameters: 1949666867036524546(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:52,985</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:52,988</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:52,989</td>
<td class="Message">==&gt; Parameters: 1949666867103633409(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:52,990</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:52,993</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:52,993</td>
<td class="Message">==&gt; Parameters: 1949666867103633410(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:52,994</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:52,997</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,000</td>
<td class="Message">==&gt; Parameters: 1949666867103633411(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,000</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,004</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,005</td>
<td class="Message">==&gt; Parameters: 1949666867170742274(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,005</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,008</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,008</td>
<td class="Message">==&gt; Parameters: 1949666867170742275(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,009</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,012</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,012</td>
<td class="Message">==&gt; Parameters: 1949666867170742276(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,012</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,015</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,015</td>
<td class="Message">==&gt; Parameters: 1949666867237851138(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,017</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,019</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,019</td>
<td class="Message">==&gt; Parameters: 1949666867246239745(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,020</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,023</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,024</td>
<td class="Message">==&gt; Parameters: 1949666867246239746(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,024</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,027</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,027</td>
<td class="Message">==&gt; Parameters: 1949666867246239747(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,028</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,031</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,031</td>
<td class="Message">==&gt; Parameters: 1949666867309154305(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,032</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,035</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,035</td>
<td class="Message">==&gt; Parameters: 1949666867309154306(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,036</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,040</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,040</td>
<td class="Message">==&gt; Parameters: 1949666867309154307(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,041</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,044</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,044</td>
<td class="Message">==&gt; Parameters: 1949666867388846081(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,046</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,050</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,051</td>
<td class="Message">==&gt; Parameters: 1949666867388846082(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,051</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,055</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,056</td>
<td class="Message">==&gt; Parameters: 1949666867388846083(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,057</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,061</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,061</td>
<td class="Message">==&gt; Parameters: 1949666867388846084(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,061</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,066</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,066</td>
<td class="Message">==&gt; Parameters: 1949666867460149249(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,067</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,071</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,071</td>
<td class="Message">==&gt; Parameters: 1949666867460149250(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,072</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,077</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,077</td>
<td class="Message">==&gt; Parameters: 1949666867460149251(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,078</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,081</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,081</td>
<td class="Message">==&gt; Parameters: 1949666867535646721(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,082</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,086</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,086</td>
<td class="Message">==&gt; Parameters: 1949666867535646722(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,087</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,091</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,092</td>
<td class="Message">==&gt; Parameters: 1949672451836186626(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,092</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,096</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,096</td>
<td class="Message">==&gt; Parameters: 1949672452326920193(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,097</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,101</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,101</td>
<td class="Message">==&gt; Parameters: 1949672452607938561(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,102</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,105</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,105</td>
<td class="Message">==&gt; Parameters: 1949672452817653761(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,106</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,110</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,110</td>
<td class="Message">==&gt; Parameters: 1949672453232889857(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:04:53,110</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-08-16 14:04:53,114</td>
<td class="Message">同步KnoBaseDetail数据到Solr失败</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">507</td>
</tr>
<tr><td class="Exception" colspan="6">org.springframework.dao.DataAccessResourceFailureException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrExceptionTranslator.translateExceptionIfPossible(SolrExceptionTranslator.java:79)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:169)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.delete(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrOperations.delete(SolrOperations.java:228)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.repository.support.SimpleSolrRepository.deleteAll(SimpleSolrRepository.java:212)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService.deleteAll(KnoBaseDetailSolrService.java:313)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$FastClassBySpringCGLIB$$1d6fbb9b.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$EnhancerBySpringCGLIB$$daa8b9a9.deleteAll(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailService.syncToSolr(KnoBaseDetailService.java:497)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.Thread.run(Thread.java:833)
<br />Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:940)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:903)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.lambda$delete$6(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:167)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 65 common frames omitted
<br />Caused by: java.net.ConnectException: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnect(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.Socket.connect(Socket.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 82 common frames omitted
</td></tr>
<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:05:52,979</td>
<td class="Message">--getKeysSize--: {create_time=1755324352979, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:05:52,979</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755324352979}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:06:52,975</td>
<td class="Message">--getKeysSize--: {create_time=1755324412975, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:06:52,975</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755324412975}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:07:52,968</td>
<td class="Message">--getKeysSize--: {create_time=1755324472968, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:07:52,970</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755324472970}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:08:52,974</td>
<td class="Message">--getKeysSize--: {create_time=1755324532974, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:08:52,974</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755324532974}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:09:52,975</td>
<td class="Message">--getKeysSize--: {create_time=1755324592975, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:09:52,975</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755324592975}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:10:52,967</td>
<td class="Message">--getKeysSize--: {create_time=1755324652967, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:10:52,970</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755324652970}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:11:52,965</td>
<td class="Message">--getKeysSize--: {create_time=1755324712965, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:11:52,967</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755324712967}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:12:52,971</td>
<td class="Message">--getKeysSize--: {create_time=1755324772971, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:12:52,973</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755324772973}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:13:52,974</td>
<td class="Message">--getKeysSize--: {create_time=1755324832974, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:13:52,976</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755324832976}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:14:52,971</td>
<td class="Message">--getKeysSize--: {create_time=1755324892971, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:14:52,974</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755324892974}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:15:52,972</td>
<td class="Message">--getKeysSize--: {create_time=1755324952972, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:15:52,973</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755324952973}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:16:52,962</td>
<td class="Message">--getKeysSize--: {create_time=1755325012962, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:16:52,962</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755325012962}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:17:52,967</td>
<td class="Message">--getKeysSize--: {create_time=1755325072967, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:17:52,970</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755325072970}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:18:52,974</td>
<td class="Message">--getKeysSize--: {create_time=1755325132974, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:18:52,974</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755325132974}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 14:19:52,964</td>
<td class="Message">开始同步KnoBaseDetail数据到Solr...</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">490</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:52,966</td>
<td class="Message">--getKeysSize--: {create_time=1755325192966, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:52,967</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755325192967}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:52,980</td>
<td class="Message">==&gt;  Preparing: SELECT id, title, content, ocr_content, srcipt, srcipt_simplify, category_id, parent_id, knowledge_id, keywords, view_count, status, source_type, creator_name, updater_name, create_time, update_time, organ_name, organ_id, single_image_file_path, from_detail_page_num, from_detail_page_id, order_num FROM kno_base_detail WHERE (status &lt;&gt; ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:52,980</td>
<td class="Message">==&gt; Parameters: 0(Integer)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:52,990</td>
<td class="Message">&lt;==      Total: 28</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:52,996</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:52,996</td>
<td class="Message">==&gt; Parameters: 1949666867036524546(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:52,996</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,003</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,003</td>
<td class="Message">==&gt; Parameters: 1949666867103633409(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,003</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,008</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,008</td>
<td class="Message">==&gt; Parameters: 1949666867103633410(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,008</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,013</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,013</td>
<td class="Message">==&gt; Parameters: 1949666867103633411(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,017</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,020</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,020</td>
<td class="Message">==&gt; Parameters: 1949666867170742274(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,022</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,029</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,030</td>
<td class="Message">==&gt; Parameters: 1949666867170742275(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,030</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,035</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,035</td>
<td class="Message">==&gt; Parameters: 1949666867170742276(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,035</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,040</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,040</td>
<td class="Message">==&gt; Parameters: 1949666867237851138(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,042</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,047</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,047</td>
<td class="Message">==&gt; Parameters: 1949666867246239745(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,048</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,053</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,053</td>
<td class="Message">==&gt; Parameters: 1949666867246239746(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,054</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,058</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,058</td>
<td class="Message">==&gt; Parameters: 1949666867246239747(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,059</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,063</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,063</td>
<td class="Message">==&gt; Parameters: 1949666867309154305(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,063</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,067</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,067</td>
<td class="Message">==&gt; Parameters: 1949666867309154306(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,070</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,070</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,074</td>
<td class="Message">==&gt; Parameters: 1949666867309154307(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,074</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,080</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,080</td>
<td class="Message">==&gt; Parameters: 1949666867388846081(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,082</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,086</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,086</td>
<td class="Message">==&gt; Parameters: 1949666867388846082(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,086</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,093</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,093</td>
<td class="Message">==&gt; Parameters: 1949666867388846083(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,096</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,102</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,102</td>
<td class="Message">==&gt; Parameters: 1949666867388846084(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,103</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,108</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,108</td>
<td class="Message">==&gt; Parameters: 1949666867460149249(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,108</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,113</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,117</td>
<td class="Message">==&gt; Parameters: 1949666867460149250(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,117</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,123</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,123</td>
<td class="Message">==&gt; Parameters: 1949666867460149251(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,123</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,128</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,128</td>
<td class="Message">==&gt; Parameters: 1949666867535646721(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,128</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,134</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,134</td>
<td class="Message">==&gt; Parameters: 1949666867535646722(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,134</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,139</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,139</td>
<td class="Message">==&gt; Parameters: 1949672451836186626(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,140</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,146</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,146</td>
<td class="Message">==&gt; Parameters: 1949672452326920193(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,146</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,149</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,149</td>
<td class="Message">==&gt; Parameters: 1949672452607938561(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,153</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,156</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,156</td>
<td class="Message">==&gt; Parameters: 1949672452817653761(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,156</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,161</td>
<td class="Message">==&gt;  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,161</td>
<td class="Message">==&gt; Parameters: 1949672453232889857(String)</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:19:53,163</td>
<td class="Message">&lt;==      Total: 0</td>
<td class="MethodOfCaller">debug</td>
<td class="FileOfCaller">BaseJdbcLogger.java</td>
<td class="LineOfCaller">137</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-08-16 14:19:53,165</td>
<td class="Message">同步KnoBaseDetail数据到Solr失败</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">507</td>
</tr>
<tr><td class="Exception" colspan="6">org.springframework.dao.DataAccessResourceFailureException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrExceptionTranslator.translateExceptionIfPossible(SolrExceptionTranslator.java:79)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:169)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.delete(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrOperations.delete(SolrOperations.java:228)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.repository.support.SimpleSolrRepository.deleteAll(SimpleSolrRepository.java:212)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService.deleteAll(KnoBaseDetailSolrService.java:313)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$FastClassBySpringCGLIB$$1d6fbb9b.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$EnhancerBySpringCGLIB$$daa8b9a9.deleteAll(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailService.syncToSolr(KnoBaseDetailService.java:497)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.Thread.run(Thread.java:833)
<br />Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:940)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:903)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.lambda$delete$6(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:167)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 65 common frames omitted
<br />Caused by: java.net.ConnectException: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnect(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.Socket.connect(Socket.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 82 common frames omitted
</td></tr>
<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:20:52,977</td>
<td class="Message">--getKeysSize--: {create_time=1755325252977, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:20:52,980</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755325252980}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:21:52,968</td>
<td class="Message">--getKeysSize--: {create_time=1755325312968, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:21:52,969</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755325312969}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:22:52,977</td>
<td class="Message">--getKeysSize--: {create_time=1755325372977, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:22:52,979</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755325372979}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:23:52,974</td>
<td class="Message">--getKeysSize--: {create_time=1755325432974, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:23:52,979</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755325432979}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:24:52,979</td>
<td class="Message">--getKeysSize--: {create_time=1755325492979, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:24:52,979</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755325492979}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:25:52,974</td>
<td class="Message">--getKeysSize--: {create_time=1755325552974, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:25:52,974</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755325552974}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:26:52,972</td>
<td class="Message">--getKeysSize--: {create_time=1755325612971, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:26:52,974</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755325612974}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:27:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755325672964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:27:52,964</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755325672964}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:28:52,974</td>
<td class="Message">--getKeysSize--: {create_time=1755325732974, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:28:52,974</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755325732974}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:29:52,979</td>
<td class="Message">--getKeysSize--: {create_time=1755325792979, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:29:52,980</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755325792980}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:30:52,977</td>
<td class="Message">--getKeysSize--: {create_time=1755325852977, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:30:52,980</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755325852980}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:31:52,972</td>
<td class="Message">--getKeysSize--: {create_time=1755325912972, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:31:52,974</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755325912974}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:32:52,965</td>
<td class="Message">--getKeysSize--: {create_time=1755325972965, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:32:52,967</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755325972967}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>

<tr class="debug odd">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:33:52,964</td>
<td class="Message">--getKeysSize--: {create_time=1755326032964, dbSize=848}</td>
<td class="MethodOfCaller">getKeysSize</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">74</td>
</tr>

<tr class="debug even">
<td class="Level">DEBUG</td>
<td class="Date">2025-08-16 14:33:52,965</td>
<td class="Message">--getMemoryInfo--: {used_memory=1541312, create_time=1755326032965}</td>
<td class="MethodOfCaller">getMemoryInfo</td>
<td class="FileOfCaller">RedisServiceImpl.java</td>
<td class="LineOfCaller">90</td>
</tr>
