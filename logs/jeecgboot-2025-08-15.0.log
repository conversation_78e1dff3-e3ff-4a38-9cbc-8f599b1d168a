2025-08-15 17:47:09.419 [background-preinit] INFO  org.hibernate.validator.internal.util.Version:21 - HV000001: Hibernate Validator 6.2.5.Final
2025-08-15 17:47:09.494 [main] INFO  org.jeecg.WordCompassApplication:55 - Starting DetectApplication using Java 17.0.6 on DESKTOP-EU3ACCV with PID 17780 (E:\my-work-2025\work-wordcompass\wordCompass\service\wordCompass-server\build\classes\java\main started by Administrator in E:\my-work-2025\work-wordcompass\wordCompass)
2025-08-15 17:47:09.495 [main] INFO  org.jeecg.WordCompassApplication:637 - The following 1 profile is active: "dev"
2025-08-15 17:47:11.499 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-15 17:47:11.501 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:132 - Bootstrapping Spring Data Solr repositories in DEFAULT mode.
2025-08-15 17:47:11.751 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:201 - Finished Spring Data repository scanning in 230 ms. Found 1 Solr repository interfaces.
2025-08-15 17:47:12.311 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:262 - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-15 17:47:12.314 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-15 17:47:12.530 [main] INFO  o.s.d.r.c.RepositoryConfigurationExtensionSupport:349 - Spring Data Redis - Could not safely identify store assignment for repository candidate interface org.jeecg.modules.km.kno.repository.KnoBaseDetailVoRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-08-15 17:47:12.531 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate:201 - Finished Spring Data repository scanning in 206 ms. Found 0 Redis repository interfaces.
2025-08-15 17:47:13.212 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties' of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-15 17:47:13.217 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration' of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-15 17:47:13.261 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration' of type [org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-15 17:47:13.264 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusMetricsExportAutoConfiguration' of type [org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusMetricsExportAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-15 17:47:13.269 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'management.metrics.export.prometheus-org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusProperties' of type [org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-15 17:47:13.272 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'prometheusConfig' of type [org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusPropertiesConfigAdapter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-15 17:47:13.278 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'collectorRegistry' of type [io.prometheus.client.CollectorRegistry] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-15 17:47:13.281 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration' of type [org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-15 17:47:13.282 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'micrometerClock' of type [io.micrometer.core.instrument.Clock$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-15 17:47:13.318 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'prometheusMeterRegistry' of type [io.micrometer.prometheus.PrometheusMeterRegistry] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-15 17:47:13.323 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'micrometerOptions' of type [io.lettuce.core.metrics.MicrometerOptions] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-15 17:47:13.324 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'lettuceMetrics' of type [org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration$$Lambda$645/0x000000080121b5a0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-15 17:47:13.497 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'lettuceClientResources' of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-15 17:47:13.711 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'redisConnectionFactory' of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-15 17:47:13.720 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'jeecgBaseConfig' of type [org.jeecg.config.JeecgBaseConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-15 17:47:13.725 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'shiroConfig' of type [org.jeecg.config.shiro.ShiroConfig$$EnhancerBySpringCGLIB$$34836bc2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-15 17:47:14.177 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-15 17:47:14.186 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration$$EnhancerBySpringCGLIB$$56b8db1d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-15 17:47:14.202 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-15 17:47:14.265 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'shiroRealm' of type [org.jeecg.config.shiro.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-15 17:47:14.340 [main] INFO  org.jeecg.config.shiro.ShiroConfig:247 - ===============(1)创建缓存管理器RedisCacheManager
2025-08-15 17:47:14.341 [main] INFO  org.jeecg.config.shiro.ShiroConfig:265 - ===============(2)创建RedisManager,连接Redis..
2025-08-15 17:47:14.346 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'redisManager' of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-15 17:47:14.353 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-15 17:47:14.380 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-15 17:47:14.405 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration' of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$e8600179] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-15 17:47:14.411 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'eventBus' of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-15 17:47:14.916 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:108 - Tomcat initialized with port(s): 8080 (http)
2025-08-15 17:47:14.933 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-15 17:47:14.935 [main] INFO  org.apache.catalina.core.StandardService:173 - Starting service [Tomcat]
2025-08-15 17:47:14.935 [main] INFO  org.apache.catalina.core.StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.73]
2025-08-15 17:47:15.400 [main] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/kmse]:173 - Initializing Spring embedded WebApplicationContext
2025-08-15 17:47:15.400 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext:292 - Root WebApplicationContext: initialization completed in 5821 ms
2025-08-15 17:47:17.073 [main] INFO  com.alibaba.druid.pool.DruidDataSource:1010 - {dataSource-1,master} inited
2025-08-15 17:47:17.075 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:154 - dynamic-datasource - add a datasource named [master] success
2025-08-15 17:47:17.076 [main] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:237 - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2025-08-15 17:47:18.892 [main] INFO  org.jeecg.common.modules.redis.config.RedisConfig:56 -  --- redis config init --- 
2025-08-15 17:47:21.819 [main] INFO  o.j.modules.km.kno.service.KnowledgeScriptService:66 - 知识服务RestTemplate初始化完成，连接超时: 30000ms，读取超时: 600000ms，最大重试次数: 3，重试间隔: 1000ms
2025-08-15 17:47:21.918 [main] INFO  o.jeecg.modules.km.kno.service.VectorStoreService:52 - 向量服务RestTemplate初始化完成，连接超时: 30000ms，读取超时: 300000ms
2025-08-15 17:47:23.486 [main] INFO  org.quartz.impl.StdSchedulerFactory:1220 - Using default implementation for ThreadExecutor
2025-08-15 17:47:23.491 [main] INFO  org.quartz.simpl.SimpleThreadPool:268 - Job execution threads will use class loader of thread: main
2025-08-15 17:47:23.551 [main] INFO  org.quartz.core.SchedulerSignalerImpl:61 - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-08-15 17:47:23.554 [main] INFO  org.quartz.core.QuartzScheduler:229 - Quartz Scheduler v.2.3.2 created.
2025-08-15 17:47:23.586 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:672 - Using db table-based data access locking (synchronization).
2025-08-15 17:47:23.612 [main] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:145 - JobStoreCMT initialized.
2025-08-15 17:47:23.617 [main] INFO  org.quartz.core.QuartzScheduler:294 - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'MyScheduler' with instanceId 'DESKTOP-EU3ACCV1755251243489'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2025-08-15 17:47:23.623 [main] INFO  org.quartz.impl.StdSchedulerFactory:1374 - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2025-08-15 17:47:23.624 [main] INFO  org.quartz.impl.StdSchedulerFactory:1378 - Quartz scheduler version: 2.3.2
2025-08-15 17:47:23.625 [main] INFO  org.quartz.core.QuartzScheduler:2293 - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@35be9318
2025-08-15 17:47:27.408 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver:58 - Exposing 2 endpoint(s) beneath base path '/actuator'
2025-08-15 17:47:27.549 [main] INFO  org.jeecg.config.init.CodeGenerateDbConfig:46 -  Init CodeGenerate Config [ Get Db Config From application.yml ] 
2025-08-15 17:47:29.125 [main] INFO  org.apache.coyote.http11.Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8080"]
2025-08-15 17:47:29.153 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer:220 - Tomcat started on port(s): 8080 (http) with context path '/kmse'
2025-08-15 17:47:31.618 [main] INFO  o.s.scheduling.quartz.SchedulerFactoryBean:734 - Will start Quartz Scheduler [MyScheduler] in 1 seconds
2025-08-15 17:47:31.637 [km-scheduling-1] INFO  o.j.modules.km.kno.service.KnoBaseDetailService:490 - 开始同步KnoBaseDetail数据到Solr...
2025-08-15 17:47:31.640 [main] INFO  org.jeecg.WordCompassApplication:61 - Started DetectApplication in 22.935 seconds (JVM running for 24.6)
2025-08-15 17:47:31.647 [km-scheduling-2] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755251251647, dbSize=847}
2025-08-15 17:47:31.653 [km-scheduling-2] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1578784, create_time=1755251251653}
2025-08-15 17:47:31.653 [main] INFO  org.jeecg.config.init.CodeTemplateInitListener:29 -  Init Code Generate Template [ 检测如果是JAR启动环境，Copy模板到config目录 ] 
2025-08-15 17:47:31.655 [main] INFO  org.jeecg.WordCompassApplication:39 -
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8080/kmse/
	External: 	http://**************:8080/kmse/
	Swagger文档: 	http://**************:8080/kmse/doc.html
----------------------------------------------------------
2025-08-15 17:47:31.807 [km-scheduling-1] DEBUG o.j.m.km.kno.mapper.KnoBaseDetailMapper.selectList:137 - ==>  Preparing: SELECT id, title, content, ocr_content, srcipt, srcipt_simplify, category_id, parent_id, knowledge_id, keywords, view_count, status, source_type, creator_name, updater_name, create_time, update_time, organ_name, organ_id, single_image_file_path, from_detail_page_num, from_detail_page_id, order_num FROM kno_base_detail WHERE (status <> ?)
2025-08-15 17:47:31.818 [RMI TCP Connection(7)-**************] INFO  o.a.c.c.ContainerBase.[Tomcat].[localhost].[/kmse]:173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-15 17:47:31.819 [RMI TCP Connection(7)-**************] INFO  org.springframework.web.servlet.DispatcherServlet:525 - Initializing Servlet 'dispatcherServlet'
2025-08-15 17:47:31.823 [RMI TCP Connection(7)-**************] INFO  org.springframework.web.servlet.DispatcherServlet:547 - Completed initialization in 4 ms
2025-08-15 17:47:31.957 [km-scheduling-1] DEBUG o.j.m.km.kno.mapper.KnoBaseDetailMapper.selectList:137 - ==> Parameters: 0(Integer)
2025-08-15 17:47:32.012 [RMI TCP Connection(10)-**************] WARN  o.s.boot.actuate.solr.SolrHealthIndicator:94 - Solr health check failed
org.apache.solr.client.solrj.SolrServerException: Server refused connection at: http://127.0.0.1:8099/solr-881
	at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:688)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
	at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
	at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:231)
	at org.springframework.boot.actuate.solr.SolrHealthIndicator$RootStatusCheck.getStatus(SolrHealthIndicator.java:116)
	at org.springframework.boot.actuate.solr.SolrHealthIndicator.initializeStatusCheck(SolrHealthIndicator.java:79)
	at org.springframework.boot.actuate.solr.SolrHealthIndicator.initializeStatusCheck(SolrHealthIndicator.java:67)
	at org.springframework.boot.actuate.solr.SolrHealthIndicator.doHealthCheck(SolrHealthIndicator.java:53)
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82)
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94)
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110)
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88)
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282)
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74)
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:124)
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:814)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:802)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1472)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1310)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1405)
	at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829)
	at java.base/jdk.internal.reflect.GeneratedMethodAccessor29.invoke(Unknown Source)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at java.rmi/sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:360)
	at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:200)
	at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:197)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:712)
	at java.rmi/sun.rmi.transport.Transport.serviceCall(Transport.java:196)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:587)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:828)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:705)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:704)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
	... 51 common frames omitted
Caused by: java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 61 common frames omitted
2025-08-15 17:47:32.035 [km-scheduling-1] DEBUG o.j.m.km.kno.mapper.KnoBaseDetailMapper.selectList:137 - <==      Total: 28
2025-08-15 17:47:32.052 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-15 17:47:32.053 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867036524546(String)
2025-08-15 17:47:32.057 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-15 17:47:32.067 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-15 17:47:32.068 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867103633409(String)
2025-08-15 17:47:32.068 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-15 17:47:32.077 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-15 17:47:32.078 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867103633410(String)
2025-08-15 17:47:32.079 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-15 17:47:32.084 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-15 17:47:32.085 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867103633411(String)
2025-08-15 17:47:32.087 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-15 17:47:32.095 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-15 17:47:32.095 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867170742274(String)
2025-08-15 17:47:32.096 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-15 17:47:32.104 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-15 17:47:32.104 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867170742275(String)
2025-08-15 17:47:32.105 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-15 17:47:32.113 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-15 17:47:32.114 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867170742276(String)
2025-08-15 17:47:32.115 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-15 17:47:32.122 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-15 17:47:32.123 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867237851138(String)
2025-08-15 17:47:32.124 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-15 17:47:32.131 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-15 17:47:32.132 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867246239745(String)
2025-08-15 17:47:32.133 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-15 17:47:32.140 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-15 17:47:32.141 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867246239746(String)
2025-08-15 17:47:32.143 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-15 17:47:32.150 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-15 17:47:32.151 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867246239747(String)
2025-08-15 17:47:32.152 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-15 17:47:32.158 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-15 17:47:32.158 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867309154305(String)
2025-08-15 17:47:32.159 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-15 17:47:32.165 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-15 17:47:32.165 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867309154306(String)
2025-08-15 17:47:32.166 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-15 17:47:32.172 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-15 17:47:32.172 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867309154307(String)
2025-08-15 17:47:32.173 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-15 17:47:32.182 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-15 17:47:32.183 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846081(String)
2025-08-15 17:47:32.186 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-15 17:47:32.197 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-15 17:47:32.198 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846082(String)
2025-08-15 17:47:32.199 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-15 17:47:32.204 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-15 17:47:32.204 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846083(String)
2025-08-15 17:47:32.206 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-15 17:47:32.217 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-15 17:47:32.218 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867388846084(String)
2025-08-15 17:47:32.219 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-15 17:47:32.224 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-15 17:47:32.225 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867460149249(String)
2025-08-15 17:47:32.226 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-15 17:47:32.233 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-15 17:47:32.234 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867460149250(String)
2025-08-15 17:47:32.235 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-15 17:47:32.243 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-15 17:47:32.243 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867460149251(String)
2025-08-15 17:47:32.245 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-15 17:47:32.251 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-15 17:47:32.251 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867535646721(String)
2025-08-15 17:47:32.252 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-15 17:47:32.259 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-15 17:47:32.259 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666867535646722(String)
2025-08-15 17:47:32.261 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-15 17:47:32.267 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-15 17:47:32.267 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672451836186626(String)
2025-08-15 17:47:32.268 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-15 17:47:32.274 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-15 17:47:32.275 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672452326920193(String)
2025-08-15 17:47:32.275 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-15 17:47:32.282 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-15 17:47:32.282 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672452607938561(String)
2025-08-15 17:47:32.283 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-15 17:47:32.290 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-15 17:47:32.290 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672452817653761(String)
2025-08-15 17:47:32.292 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-15 17:47:32.299 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_detail_id = ?)
2025-08-15 17:47:32.300 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672453232889857(String)
2025-08-15 17:47:32.300 [km-scheduling-1] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-15 17:47:32.357 [km-scheduling-1] ERROR o.j.modules.km.kno.service.KnoBaseDetailService:507 - 同步KnoBaseDetail数据到Solr失败
org.springframework.dao.DataAccessResourceFailureException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
	at org.springframework.data.solr.core.SolrExceptionTranslator.translateExceptionIfPossible(SolrExceptionTranslator.java:79)
	at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:169)
	at org.springframework.data.solr.core.SolrTemplate.delete(SolrTemplate.java:249)
	at org.springframework.data.solr.core.SolrOperations.delete(SolrOperations.java:228)
	at org.springframework.data.solr.repository.support.SimpleSolrRepository.deleteAll(SimpleSolrRepository.java:212)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
	at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
	at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService.deleteAll(KnoBaseDetailSolrService.java:313)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$FastClassBySpringCGLIB$$1d6fbb9b.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$EnhancerBySpringCGLIB$$ac026c6e.deleteAll(<generated>)
	at org.jeecg.modules.km.kno.service.KnoBaseDetailService.syncToSolr(KnoBaseDetailService.java:497)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:568)
	at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
	at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
	at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:833)
Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
	at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
	at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
	at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:940)
	at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:903)
	at org.springframework.data.solr.core.SolrTemplate.lambda$delete$6(SolrTemplate.java:249)
	at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:167)
	... 65 common frames omitted
Caused by: java.net.ConnectException: Connection refused: no further information
	at java.base/sun.nio.ch.Net.pollConnect(Native Method)
	at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
	at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
	at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
	at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
	at java.base/java.net.Socket.connect(Socket.java:633)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 82 common frames omitted
2025-08-15 17:47:32.621 [Quartz Scheduler [MyScheduler]] INFO  o.s.scheduling.quartz.SchedulerFactoryBean:750 - Starting Quartz Scheduler now, after delay of 1 seconds
2025-08-15 17:47:32.751 [Quartz Scheduler [MyScheduler]] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:3644 - ClusterManager: detected 1 failed or restarted instances.
2025-08-15 17:47:32.752 [Quartz Scheduler [MyScheduler]] INFO  o.s.scheduling.quartz.LocalDataSourceJobStore:3503 - ClusterManager: Scanning for instance "DESKTOP-EU3ACCV1755246544624"'s failed in-progress jobs.
2025-08-15 17:47:32.761 [Quartz Scheduler [MyScheduler]] INFO  org.quartz.core.QuartzScheduler:547 - Scheduler MyScheduler_$_DESKTOP-EU3ACCV1755251243489 started.
2025-08-15 17:47:45.989 [http-nio-8080-exec-1] INFO  org.apache.tomcat.util.http.parser.Cookie:173 - A cookie header was received [Hm_lvt_0febd9e3cacb3f627ddac64d52caac39=1754964440,1755243411,1755246569,1755248930;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-08-15 17:47:46.416 [http-nio-8080-exec-1] WARN  c.b.m.core.toolkit.support.ReflectLambdaMeta:41 - Unable to make field private final java.lang.Class java.lang.invoke.SerializedLambda.capturingClass accessible: module java.base does not "opens java.lang.invoke" to unnamed module @65e2dbf3
2025-08-15 17:47:46.476 [http-nio-8080-exec-1] DEBUG org.jeecg.common.aspect.DictAspect:65 - 获取JSON数据 耗时：246ms
2025-08-15 17:47:46.477 [http-nio-8080-exec-1] DEBUG org.jeecg.common.aspect.DictAspect:69 - 注入字典到JSON数据  耗时0ms
2025-08-15 17:47:50.889 [http-nio-8080-exec-2] DEBUG org.jeecg.modules.message.websocket.WebSocket:43 - 【系统 WebSocket】有新的连接，总数为:1
2025-08-15 17:47:50.965 [http-nio-8080-exec-7] DEBUG org.jeecg.common.aspect.DictAspect:65 - 获取JSON数据 耗时：78ms
2025-08-15 17:47:50.966 [http-nio-8080-exec-7] DEBUG org.jeecg.common.aspect.DictAspect:69 - 注入字典到JSON数据  耗时0ms
2025-08-15 17:47:59.044 [http-nio-8080-exec-4] DEBUG o.j.m.k.k.m.KnoBusinessTypeMapper.selectByParentId:137 - ==>  Preparing: SELECT * FROM kno_business_type WHERE parent_id = ? ORDER BY sort ASC, create_time ASC
2025-08-15 17:47:59.045 [http-nio-8080-exec-4] DEBUG o.j.m.k.k.m.KnoBusinessTypeMapper.selectByParentId:137 - ==> Parameters: 0(String)
2025-08-15 17:47:59.050 [http-nio-8080-exec-4] DEBUG o.j.m.k.k.m.KnoBusinessTypeMapper.selectByParentId:137 - <==      Total: 5
2025-08-15 17:47:59.056 [http-nio-8080-exec-4] DEBUG o.j.m.k.k.m.KnoBusinessTypeMapper.selectByParentId:137 - ==>  Preparing: SELECT * FROM kno_business_type WHERE parent_id = ? ORDER BY sort ASC, create_time ASC
2025-08-15 17:47:59.056 [http-nio-8080-exec-4] DEBUG o.j.m.k.k.m.KnoBusinessTypeMapper.selectByParentId:137 - ==> Parameters: 1949423093518999554(String)
2025-08-15 17:47:59.058 [http-nio-8080-exec-4] DEBUG o.j.m.k.k.m.KnoBusinessTypeMapper.selectByParentId:137 - <==      Total: 0
2025-08-15 17:47:59.062 [http-nio-8080-exec-4] DEBUG o.j.m.k.k.m.KnoBusinessTypeMapper.selectByParentId:137 - ==>  Preparing: SELECT * FROM kno_business_type WHERE parent_id = ? ORDER BY sort ASC, create_time ASC
2025-08-15 17:47:59.062 [http-nio-8080-exec-4] DEBUG o.j.m.k.k.m.KnoBusinessTypeMapper.selectByParentId:137 - ==> Parameters: 1948584181056360449(String)
2025-08-15 17:47:59.065 [http-nio-8080-exec-4] DEBUG o.j.m.k.k.m.KnoBusinessTypeMapper.selectByParentId:137 - <==      Total: 0
2025-08-15 17:47:59.068 [http-nio-8080-exec-4] DEBUG o.j.m.k.k.m.KnoBusinessTypeMapper.selectByParentId:137 - ==>  Preparing: SELECT * FROM kno_business_type WHERE parent_id = ? ORDER BY sort ASC, create_time ASC
2025-08-15 17:47:59.068 [http-nio-8080-exec-4] DEBUG o.j.m.k.k.m.KnoBusinessTypeMapper.selectByParentId:137 - ==> Parameters: 1949423158035783681(String)
2025-08-15 17:47:59.069 [http-nio-8080-exec-4] DEBUG o.j.m.k.k.m.KnoBusinessTypeMapper.selectByParentId:137 - <==      Total: 0
2025-08-15 17:47:59.074 [http-nio-8080-exec-4] DEBUG o.j.m.k.k.m.KnoBusinessTypeMapper.selectByParentId:137 - ==>  Preparing: SELECT * FROM kno_business_type WHERE parent_id = ? ORDER BY sort ASC, create_time ASC
2025-08-15 17:47:59.074 [http-nio-8080-exec-4] DEBUG o.j.m.k.k.m.KnoBusinessTypeMapper.selectByParentId:137 - ==> Parameters: 1949431759345311745(String)
2025-08-15 17:47:59.075 [http-nio-8080-exec-4] DEBUG o.j.m.k.k.m.KnoBusinessTypeMapper.selectByParentId:137 - <==      Total: 0
2025-08-15 17:47:59.079 [http-nio-8080-exec-4] DEBUG o.j.m.k.k.m.KnoBusinessTypeMapper.selectByParentId:137 - ==>  Preparing: SELECT * FROM kno_business_type WHERE parent_id = ? ORDER BY sort ASC, create_time ASC
2025-08-15 17:47:59.080 [http-nio-8080-exec-4] DEBUG o.j.m.k.k.m.KnoBusinessTypeMapper.selectByParentId:137 - ==> Parameters: 1949653561743642626(String)
2025-08-15 17:47:59.081 [http-nio-8080-exec-4] DEBUG o.j.m.k.k.m.KnoBusinessTypeMapper.selectByParentId:137 - <==      Total: 0
2025-08-15 17:47:59.081 [http-nio-8080-exec-4] DEBUG org.jeecg.common.aspect.DictAspect:65 - 获取JSON数据 耗时：55ms
2025-08-15 17:47:59.082 [http-nio-8080-exec-4] DEBUG org.jeecg.common.aspect.DictAspect:69 - 注入字典到JSON数据  耗时0ms
2025-08-15 17:47:59.203 [http-nio-8080-exec-10] DEBUG org.jeecg.modules.km.kno.service.KnoDeptService:85 - 找到根机构：总公司, orgCategory: 1
2025-08-15 17:47:59.209 [http-nio-8080-exec-10] DEBUG o.j.m.k.k.m.K.searchKnoBasePage_mpCount:137 - ==>  Preparing: SELECT COUNT(*) AS total FROM kno_base WHERE 1 = 1
2025-08-15 17:47:59.210 [http-nio-8080-exec-10] DEBUG o.j.m.k.k.m.K.searchKnoBasePage_mpCount:137 - ==> Parameters: 
2025-08-15 17:47:59.214 [http-nio-8080-exec-10] DEBUG o.j.m.k.k.m.K.searchKnoBasePage_mpCount:137 - <==      Total: 1
2025-08-15 17:47:59.215 [http-nio-8080-exec-10] DEBUG o.j.m.k.kno.mapper.KnoBaseMapper.searchKnoBasePage:137 - ==>  Preparing: SELECT * FROM kno_base WHERE 1 = 1 ORDER BY create_time DESC LIMIT ?
2025-08-15 17:47:59.216 [http-nio-8080-exec-10] DEBUG o.j.m.k.kno.mapper.KnoBaseMapper.searchKnoBasePage:137 - ==> Parameters: 10(Long)
2025-08-15 17:47:59.217 [http-nio-8080-exec-10] DEBUG o.j.m.k.kno.mapper.KnoBaseMapper.searchKnoBasePage:137 - <==      Total: 7
2025-08-15 17:47:59.220 [http-nio-8080-exec-10] DEBUG o.j.m.k.k.m.K.selectByKnowledgeId:137 - ==>  Preparing: SELECT * FROM kno_base_detail WHERE knowledge_id = ?
2025-08-15 17:47:59.221 [http-nio-8080-exec-10] DEBUG o.j.m.k.k.m.K.selectByKnowledgeId:137 - ==> Parameters: 1950022388302213122(String)
2025-08-15 17:47:59.222 [http-nio-8080-exec-10] DEBUG o.j.m.k.k.m.K.selectByKnowledgeId:137 - <==      Total: 2
2025-08-15 17:47:59.236 [http-nio-8080-exec-10] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_id = ?)
2025-08-15 17:47:59.236 [http-nio-8080-exec-10] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1950022388302213122(String)
2025-08-15 17:47:59.238 [http-nio-8080-exec-10] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 1
2025-08-15 17:47:59.243 [http-nio-8080-exec-10] DEBUG o.j.m.km.kno.mapper.KnoTagMapper.selectBatchIds:137 - ==>  Preparing: SELECT id, name, create_time FROM kno_tag WHERE id IN (?)
2025-08-15 17:47:59.244 [http-nio-8080-exec-10] DEBUG o.j.m.km.kno.mapper.KnoTagMapper.selectBatchIds:137 - ==> Parameters: 3(String)
2025-08-15 17:47:59.246 [http-nio-8080-exec-10] DEBUG o.j.m.km.kno.mapper.KnoTagMapper.selectBatchIds:137 - <==      Total: 1
2025-08-15 17:47:59.250 [http-nio-8080-exec-10] DEBUG o.j.m.k.k.m.K.selectByKnowledgeId:137 - ==>  Preparing: SELECT * FROM kno_base_detail WHERE knowledge_id = ?
2025-08-15 17:47:59.251 [http-nio-8080-exec-10] DEBUG o.j.m.k.k.m.K.selectByKnowledgeId:137 - ==> Parameters: 1949789424754593794(String)
2025-08-15 17:47:59.252 [http-nio-8080-exec-10] DEBUG o.j.m.k.k.m.K.selectByKnowledgeId:137 - <==      Total: 1
2025-08-15 17:47:59.259 [http-nio-8080-exec-10] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_id = ?)
2025-08-15 17:47:59.260 [http-nio-8080-exec-10] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949789424754593794(String)
2025-08-15 17:47:59.262 [http-nio-8080-exec-10] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-15 17:47:59.264 [http-nio-8080-exec-10] DEBUG o.j.m.k.k.m.K.selectByKnowledgeId:137 - ==>  Preparing: SELECT * FROM kno_base_detail WHERE knowledge_id = ?
2025-08-15 17:47:59.264 [http-nio-8080-exec-10] DEBUG o.j.m.k.k.m.K.selectByKnowledgeId:137 - ==> Parameters: 1949789248434442241(String)
2025-08-15 17:47:59.266 [http-nio-8080-exec-10] DEBUG o.j.m.k.k.m.K.selectByKnowledgeId:137 - <==      Total: 2
2025-08-15 17:47:59.273 [http-nio-8080-exec-10] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_id = ?)
2025-08-15 17:47:59.274 [http-nio-8080-exec-10] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949789248434442241(String)
2025-08-15 17:47:59.275 [http-nio-8080-exec-10] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 1
2025-08-15 17:47:59.279 [http-nio-8080-exec-10] DEBUG o.j.m.km.kno.mapper.KnoTagMapper.selectBatchIds:137 - ==>  Preparing: SELECT id, name, create_time FROM kno_tag WHERE id IN (?)
2025-08-15 17:47:59.280 [http-nio-8080-exec-10] DEBUG o.j.m.km.kno.mapper.KnoTagMapper.selectBatchIds:137 - ==> Parameters: 3(String)
2025-08-15 17:47:59.281 [http-nio-8080-exec-10] DEBUG o.j.m.km.kno.mapper.KnoTagMapper.selectBatchIds:137 - <==      Total: 1
2025-08-15 17:47:59.285 [http-nio-8080-exec-10] DEBUG o.j.m.k.k.m.K.selectByKnowledgeId:137 - ==>  Preparing: SELECT * FROM kno_base_detail WHERE knowledge_id = ?
2025-08-15 17:47:59.285 [http-nio-8080-exec-10] DEBUG o.j.m.k.k.m.K.selectByKnowledgeId:137 - ==> Parameters: 1949789097791819777(String)
2025-08-15 17:47:59.287 [http-nio-8080-exec-10] DEBUG o.j.m.k.k.m.K.selectByKnowledgeId:137 - <==      Total: 2
2025-08-15 17:47:59.297 [http-nio-8080-exec-10] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_id = ?)
2025-08-15 17:47:59.297 [http-nio-8080-exec-10] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949789097791819777(String)
2025-08-15 17:47:59.300 [http-nio-8080-exec-10] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 1
2025-08-15 17:47:59.305 [http-nio-8080-exec-10] DEBUG o.j.m.km.kno.mapper.KnoTagMapper.selectBatchIds:137 - ==>  Preparing: SELECT id, name, create_time FROM kno_tag WHERE id IN (?)
2025-08-15 17:47:59.305 [http-nio-8080-exec-10] DEBUG o.j.m.km.kno.mapper.KnoTagMapper.selectBatchIds:137 - ==> Parameters: 3(String)
2025-08-15 17:47:59.307 [http-nio-8080-exec-10] DEBUG o.j.m.km.kno.mapper.KnoTagMapper.selectBatchIds:137 - <==      Total: 1
2025-08-15 17:47:59.311 [http-nio-8080-exec-10] DEBUG o.j.m.k.k.m.K.selectByKnowledgeId:137 - ==>  Preparing: SELECT * FROM kno_base_detail WHERE knowledge_id = ?
2025-08-15 17:47:59.312 [http-nio-8080-exec-10] DEBUG o.j.m.k.k.m.K.selectByKnowledgeId:137 - ==> Parameters: 1949788930195820545(String)
2025-08-15 17:47:59.313 [http-nio-8080-exec-10] DEBUG o.j.m.k.k.m.K.selectByKnowledgeId:137 - <==      Total: 1
2025-08-15 17:47:59.322 [http-nio-8080-exec-10] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_id = ?)
2025-08-15 17:47:59.324 [http-nio-8080-exec-10] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949788930195820545(String)
2025-08-15 17:47:59.325 [http-nio-8080-exec-10] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 2
2025-08-15 17:47:59.330 [http-nio-8080-exec-10] DEBUG o.j.m.km.kno.mapper.KnoTagMapper.selectBatchIds:137 - ==>  Preparing: SELECT id, name, create_time FROM kno_tag WHERE id IN (?, ?)
2025-08-15 17:47:59.331 [http-nio-8080-exec-10] DEBUG o.j.m.km.kno.mapper.KnoTagMapper.selectBatchIds:137 - ==> Parameters: 2(String), 3(String)
2025-08-15 17:47:59.333 [http-nio-8080-exec-10] DEBUG o.j.m.km.kno.mapper.KnoTagMapper.selectBatchIds:137 - <==      Total: 2
2025-08-15 17:47:59.336 [http-nio-8080-exec-10] DEBUG o.j.m.k.k.m.K.selectByKnowledgeId:137 - ==>  Preparing: SELECT * FROM kno_base_detail WHERE knowledge_id = ?
2025-08-15 17:47:59.336 [http-nio-8080-exec-10] DEBUG o.j.m.k.k.m.K.selectByKnowledgeId:137 - ==> Parameters: 1949672449877446657(String)
2025-08-15 17:47:59.338 [http-nio-8080-exec-10] DEBUG o.j.m.k.k.m.K.selectByKnowledgeId:137 - <==      Total: 5
2025-08-15 17:47:59.345 [http-nio-8080-exec-10] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_id = ?)
2025-08-15 17:47:59.346 [http-nio-8080-exec-10] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949672449877446657(String)
2025-08-15 17:47:59.348 [http-nio-8080-exec-10] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-15 17:47:59.350 [http-nio-8080-exec-10] DEBUG o.j.m.k.k.m.K.selectByKnowledgeId:137 - ==>  Preparing: SELECT * FROM kno_base_detail WHERE knowledge_id = ?
2025-08-15 17:47:59.351 [http-nio-8080-exec-10] DEBUG o.j.m.k.k.m.K.selectByKnowledgeId:137 - ==> Parameters: 1949666866969415681(String)
2025-08-15 17:47:59.360 [http-nio-8080-exec-10] DEBUG o.j.m.k.k.m.K.selectByKnowledgeId:137 - <==      Total: 23
2025-08-15 17:47:59.367 [http-nio-8080-exec-10] DEBUG o.j.m.km.kno.mapper.KnoCategoryMapper.selectById:137 - ==>  Preparing: SELECT id, name, parent_id, level, sort, status, create_time, update_time FROM kno_category WHERE id = ?
2025-08-15 17:47:59.368 [http-nio-8080-exec-10] DEBUG o.j.m.km.kno.mapper.KnoCategoryMapper.selectById:137 - ==> Parameters: (String)
2025-08-15 17:47:59.372 [http-nio-8080-exec-10] DEBUG o.j.m.km.kno.mapper.KnoCategoryMapper.selectById:137 - <==      Total: 0
2025-08-15 17:47:59.378 [http-nio-8080-exec-10] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==>  Preparing: SELECT id, knowledge_id, knowledge_detail_id, tag_id, create_time FROM kno_tag_relation WHERE (knowledge_id = ?)
2025-08-15 17:47:59.378 [http-nio-8080-exec-10] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - ==> Parameters: 1949666866969415681(String)
2025-08-15 17:47:59.380 [http-nio-8080-exec-10] DEBUG o.j.m.k.kno.mapper.KnoTagRelationMapper.selectList:137 - <==      Total: 0
2025-08-15 17:47:59.381 [http-nio-8080-exec-10] DEBUG org.jeecg.common.aspect.DictAspect:65 - 获取JSON数据 耗时：232ms
2025-08-15 17:47:59.381 [http-nio-8080-exec-10] DEBUG org.jeecg.common.aspect.DictAspect:112 -  __ 进入字典翻译切面 DictAspect —— 
2025-08-15 17:47:59.479 [http-nio-8080-exec-10] DEBUG org.jeecg.common.aspect.DictAspect:363 - translateManyDict.dictCodes:code
2025-08-15 17:47:59.479 [http-nio-8080-exec-10] DEBUG org.jeecg.common.aspect.DictAspect:364 - translateManyDict.values:1,2,3
2025-08-15 17:47:59.572 [http-nio-8080-exec-10] DEBUG org.jeecg.common.aspect.DictAspect:366 - translateManyDict.result:{}
2025-08-15 17:47:59.597 [http-nio-8080-exec-10] DEBUG org.jeecg.common.aspect.DictAspect:69 - 注入字典到JSON数据  耗时216ms
2025-08-15 17:48:31.653 [km-scheduling-2] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:74 - --getKeysSize--: {create_time=1755251311653, dbSize=847}
2025-08-15 17:48:31.657 [km-scheduling-2] DEBUG o.j.modules.monitor.service.impl.RedisServiceImpl:90 - --getMemoryInfo--: {used_memory=1562816, create_time=1755251311657}
2025-08-15 17:48:34.721 [http-nio-8080-exec-6] DEBUG org.jeecg.modules.message.websocket.WebSocket:52 - 【系统 WebSocket】连接断开，总数为:0
2025-08-15 17:48:40.009 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler:585 - Scheduler MyScheduler_$_DESKTOP-EU3ACCV1755251243489 paused.
2025-08-15 17:48:40.063 [SpringApplicationShutdownHook] INFO  o.s.scheduling.quartz.SchedulerFactoryBean:847 - Shutting down Quartz Scheduler
2025-08-15 17:48:40.064 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler:666 - Scheduler MyScheduler_$_DESKTOP-EU3ACCV1755251243489 shutting down.
2025-08-15 17:48:40.064 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler:585 - Scheduler MyScheduler_$_DESKTOP-EU3ACCV1755251243489 paused.
2025-08-15 17:48:40.064 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler:740 - Scheduler MyScheduler_$_DESKTOP-EU3ACCV1755251243489 shutdown complete.
2025-08-15 17:48:40.071 [SpringApplicationShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:211 - dynamic-datasource start closing ....
2025-08-15 17:48:40.072 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource:2175 - {dataSource-1} closing ...
2025-08-15 17:48:40.074 [SpringApplicationShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource:2248 - {dataSource-1} closed
2025-08-15 17:48:40.075 [SpringApplicationShutdownHook] INFO  c.b.dynamic.datasource.DynamicRoutingDataSource:215 - dynamic-datasource all closed success,bye
