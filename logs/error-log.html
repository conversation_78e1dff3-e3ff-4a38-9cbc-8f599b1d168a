<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
  <head>
    <title>Logback Log Messages</title>
<style  type="text/css">
table { margin-left: 2em; margin-right: 2em; border-left: 2px solid #AAA; }
TR.even { background: #FFFFFF; }
TR.odd { background: #EAEAEA; }
TR.warn TD.Level, TR.error TD.Level, TR.fatal TD.Level {font-weight: bold; color: #FF4040 }
TD { padding-right: 1ex; padding-left: 1ex; border-right: 2px solid #AAA; }
TD.Time, TD.Date { text-align: right; font-family: courier, monospace; font-size: smaller; }
TD.Thread { text-align: left; }
TD.Level { text-align: right; }
TD.Logger { text-align: left; }
TR.header { background: #596ED5; color: #FFF; font-weight: bold; font-size: larger; }
TD.Exception { background: #A2AEE8; font-family: courier, monospace;}
</style>

  </head>
<body>
<hr/>
<p>Log session start time Fri Aug 15 17:47:09 CST 2025</p><p></p>

<table cellspacing="0">
<tr class="header">
<td class="Level">Level</td>
<td class="Date">Date</td>
<td class="Message">Message</td>
<td class="MethodOfCaller">MethodOfCaller</td>
<td class="FileOfCaller">FileOfCaller</td>
<td class="LineOfCaller">LineOfCaller</td>
</tr>


<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:09,419</td>
<td class="Message">HV000001: Hibernate Validator 6.2.5.Final</td>
<td class="MethodOfCaller">&lt;clinit&gt;</td>
<td class="FileOfCaller">Version.java</td>
<td class="LineOfCaller">21</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:09,494</td>
<td class="Message">Starting DetectApplication using Java 17.0.6 on DESKTOP-EU3ACCV with PID 17780 (E:\my-work-2025\work-wordcompass\wordCompass\service\wordCompass-server\build\classes\java\main started by Administrator in E:\my-work-2025\work-wordcompass\wordCompass)</td>
<td class="MethodOfCaller">logStarting</td>
<td class="FileOfCaller">StartupInfoLogger.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:09,495</td>
<td class="Message">The following 1 profile is active: &quot;dev&quot;</td>
<td class="MethodOfCaller">logStartupProfileInfo</td>
<td class="FileOfCaller">SpringApplication.java</td>
<td class="LineOfCaller">637</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:11,499</td>
<td class="Message">Multiple Spring Data modules found, entering strict repository configuration mode</td>
<td class="MethodOfCaller">multipleStoresDetected</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">262</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:11,501</td>
<td class="Message">Bootstrapping Spring Data Solr repositories in DEFAULT mode.</td>
<td class="MethodOfCaller">registerRepositoriesIn</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">132</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:11,751</td>
<td class="Message">Finished Spring Data repository scanning in 230 ms. Found 1 Solr repository interfaces.</td>
<td class="MethodOfCaller">registerRepositoriesIn</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">201</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:12,311</td>
<td class="Message">Multiple Spring Data modules found, entering strict repository configuration mode</td>
<td class="MethodOfCaller">multipleStoresDetected</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">262</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:12,314</td>
<td class="Message">Bootstrapping Spring Data Redis repositories in DEFAULT mode.</td>
<td class="MethodOfCaller">registerRepositoriesIn</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">132</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:12,530</td>
<td class="Message">Spring Data Redis - Could not safely identify store assignment for repository candidate interface org.jeecg.modules.km.kno.repository.KnoBaseDetailVoRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository</td>
<td class="MethodOfCaller">isStrictRepositoryCandidate</td>
<td class="FileOfCaller">RepositoryConfigurationExtensionSupport.java</td>
<td class="LineOfCaller">349</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:12,531</td>
<td class="Message">Finished Spring Data repository scanning in 206 ms. Found 0 Redis repository interfaces.</td>
<td class="MethodOfCaller">registerRepositoriesIn</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">201</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:13,212</td>
<td class="Message">Bean &#39;spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties&#39; of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:13,217</td>
<td class="Message">Bean &#39;org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration&#39; of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:13,261</td>
<td class="Message">Bean &#39;org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration&#39; of type [org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:13,264</td>
<td class="Message">Bean &#39;org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusMetricsExportAutoConfiguration&#39; of type [org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusMetricsExportAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:13,269</td>
<td class="Message">Bean &#39;management.metrics.export.prometheus-org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusProperties&#39; of type [org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:13,272</td>
<td class="Message">Bean &#39;prometheusConfig&#39; of type [org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusPropertiesConfigAdapter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:13,278</td>
<td class="Message">Bean &#39;collectorRegistry&#39; of type [io.prometheus.client.CollectorRegistry] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:13,281</td>
<td class="Message">Bean &#39;org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration&#39; of type [org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:13,282</td>
<td class="Message">Bean &#39;micrometerClock&#39; of type [io.micrometer.core.instrument.Clock$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:13,318</td>
<td class="Message">Bean &#39;prometheusMeterRegistry&#39; of type [io.micrometer.prometheus.PrometheusMeterRegistry] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:13,323</td>
<td class="Message">Bean &#39;micrometerOptions&#39; of type [io.lettuce.core.metrics.MicrometerOptions] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:13,324</td>
<td class="Message">Bean &#39;lettuceMetrics&#39; of type [org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration$$Lambda$645/0x000000080121b5a0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:13,497</td>
<td class="Message">Bean &#39;lettuceClientResources&#39; of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:13,711</td>
<td class="Message">Bean &#39;redisConnectionFactory&#39; of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:13,720</td>
<td class="Message">Bean &#39;jeecgBaseConfig&#39; of type [org.jeecg.config.JeecgBaseConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:13,725</td>
<td class="Message">Bean &#39;shiroConfig&#39; of type [org.jeecg.config.shiro.ShiroConfig$$EnhancerBySpringCGLIB$$34836bc2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:14,177</td>
<td class="Message">Bean &#39;spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties&#39; of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:14,186</td>
<td class="Message">Bean &#39;com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration&#39; of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration$$EnhancerBySpringCGLIB$$56b8db1d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:14,202</td>
<td class="Message">Bean &#39;dsProcessor&#39; of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:14,265</td>
<td class="Message">Bean &#39;shiroRealm&#39; of type [org.jeecg.config.shiro.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:14,340</td>
<td class="Message">===============(1)创建缓存管理器RedisCacheManager</td>
<td class="MethodOfCaller">redisCacheManager</td>
<td class="FileOfCaller">ShiroConfig.java</td>
<td class="LineOfCaller">247</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:14,341</td>
<td class="Message">===============(2)创建RedisManager,连接Redis..</td>
<td class="MethodOfCaller">redisManager</td>
<td class="FileOfCaller">ShiroConfig.java</td>
<td class="LineOfCaller">265</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:14,346</td>
<td class="Message">Bean &#39;redisManager&#39; of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:14,353</td>
<td class="Message">Bean &#39;securityManager&#39; of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:14,380</td>
<td class="Message">Bean &#39;authorizationAttributeSourceAdvisor&#39; of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:14,405</td>
<td class="Message">Bean &#39;org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration&#39; of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$e8600179] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:14,411</td>
<td class="Message">Bean &#39;eventBus&#39; of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:14,916</td>
<td class="Message">Tomcat initialized with port(s): 8080 (http)</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">TomcatWebServer.java</td>
<td class="LineOfCaller">108</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:14,933</td>
<td class="Message">Initializing ProtocolHandler [&quot;http-nio-8080&quot;]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:14,935</td>
<td class="Message">Starting service [Tomcat]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:14,935</td>
<td class="Message">Starting Servlet engine: [Apache Tomcat/9.0.73]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:15,400</td>
<td class="Message">Initializing Spring embedded WebApplicationContext</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:15,400</td>
<td class="Message">Root WebApplicationContext: initialization completed in 5821 ms</td>
<td class="MethodOfCaller">prepareWebApplicationContext</td>
<td class="FileOfCaller">ServletWebServerApplicationContext.java</td>
<td class="LineOfCaller">292</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:17,073</td>
<td class="Message">{dataSource-1,master} inited</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">DruidDataSource.java</td>
<td class="LineOfCaller">1010</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:17,075</td>
<td class="Message">dynamic-datasource - add a datasource named [master] success</td>
<td class="MethodOfCaller">addDataSource</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">154</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:17,076</td>
<td class="Message">dynamic-datasource initial loaded [1] datasource,primary datasource named [master]</td>
<td class="MethodOfCaller">afterPropertiesSet</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">237</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:18,892</td>
<td class="Message"> --- redis config init --- </td>
<td class="MethodOfCaller">redisTemplate</td>
<td class="FileOfCaller">RedisConfig.java</td>
<td class="LineOfCaller">56</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:21,819</td>
<td class="Message">知识服务RestTemplate初始化完成，连接超时: 30000ms，读取超时: 600000ms，最大重试次数: 3，重试间隔: 1000ms</td>
<td class="MethodOfCaller">initKnowledgeRestTemplate</td>
<td class="FileOfCaller">KnowledgeScriptService.java</td>
<td class="LineOfCaller">66</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:21,918</td>
<td class="Message">向量服务RestTemplate初始化完成，连接超时: 30000ms，读取超时: 300000ms</td>
<td class="MethodOfCaller">initVectorRestTemplate</td>
<td class="FileOfCaller">VectorStoreService.java</td>
<td class="LineOfCaller">52</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:23,486</td>
<td class="Message">Using default implementation for ThreadExecutor</td>
<td class="MethodOfCaller">instantiate</td>
<td class="FileOfCaller">StdSchedulerFactory.java</td>
<td class="LineOfCaller">1220</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:23,491</td>
<td class="Message">Job execution threads will use class loader of thread: main</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">SimpleThreadPool.java</td>
<td class="LineOfCaller">268</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:23,551</td>
<td class="Message">Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl</td>
<td class="MethodOfCaller">&lt;init&gt;</td>
<td class="FileOfCaller">SchedulerSignalerImpl.java</td>
<td class="LineOfCaller">61</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:23,554</td>
<td class="Message">Quartz Scheduler v.2.3.2 created.</td>
<td class="MethodOfCaller">&lt;init&gt;</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">229</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:23,586</td>
<td class="Message">Using db table-based data access locking (synchronization).</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">JobStoreSupport.java</td>
<td class="LineOfCaller">672</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:23,612</td>
<td class="Message">JobStoreCMT initialized.</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">JobStoreCMT.java</td>
<td class="LineOfCaller">145</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:23,617</td>
<td class="Message">Scheduler meta-data: Quartz Scheduler (v2.3.2) &#39;MyScheduler&#39; with instanceId &#39;DESKTOP-EU3ACCV1755251243489&#39;
  Scheduler class: &#39;org.quartz.core.QuartzScheduler&#39; - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool &#39;org.quartz.simpl.SimpleThreadPool&#39; - with 10 threads.
  Using job-store &#39;org.springframework.scheduling.quartz.LocalDataSourceJobStore&#39; - which supports persistence. and is clustered.
</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">294</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:23,623</td>
<td class="Message">Quartz scheduler &#39;MyScheduler&#39; initialized from an externally provided properties instance.</td>
<td class="MethodOfCaller">instantiate</td>
<td class="FileOfCaller">StdSchedulerFactory.java</td>
<td class="LineOfCaller">1374</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:23,624</td>
<td class="Message">Quartz scheduler version: 2.3.2</td>
<td class="MethodOfCaller">instantiate</td>
<td class="FileOfCaller">StdSchedulerFactory.java</td>
<td class="LineOfCaller">1378</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:23,625</td>
<td class="Message">JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@35be9318</td>
<td class="MethodOfCaller">setJobFactory</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">2293</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:27,408</td>
<td class="Message">Exposing 2 endpoint(s) beneath base path &#39;/actuator&#39;</td>
<td class="MethodOfCaller">&lt;init&gt;</td>
<td class="FileOfCaller">EndpointLinksResolver.java</td>
<td class="LineOfCaller">58</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:27,549</td>
<td class="Message"> Init CodeGenerate Config [ Get Db Config From application.yml ] </td>
<td class="MethodOfCaller">initCodeGenerateDbConfig</td>
<td class="FileOfCaller">CodeGenerateDbConfig.java</td>
<td class="LineOfCaller">46</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:29,125</td>
<td class="Message">Starting ProtocolHandler [&quot;http-nio-8080&quot;]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:29,153</td>
<td class="Message">Tomcat started on port(s): 8080 (http) with context path &#39;/kmse&#39;</td>
<td class="MethodOfCaller">start</td>
<td class="FileOfCaller">TomcatWebServer.java</td>
<td class="LineOfCaller">220</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:31,618</td>
<td class="Message">Will start Quartz Scheduler [MyScheduler] in 1 seconds</td>
<td class="MethodOfCaller">startScheduler</td>
<td class="FileOfCaller">SchedulerFactoryBean.java</td>
<td class="LineOfCaller">734</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:31,637</td>
<td class="Message">开始同步KnoBaseDetail数据到Solr...</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">490</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:31,640</td>
<td class="Message">Started DetectApplication in 22.935 seconds (JVM running for 24.6)</td>
<td class="MethodOfCaller">logStarted</td>
<td class="FileOfCaller">StartupInfoLogger.java</td>
<td class="LineOfCaller">61</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:31,653</td>
<td class="Message"> Init Code Generate Template [ 检测如果是JAR启动环境，Copy模板到config目录 ] </td>
<td class="MethodOfCaller">onApplicationEvent</td>
<td class="FileOfCaller">CodeTemplateInitListener.java</td>
<td class="LineOfCaller">29</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:31,655</td>
<td class="Message">
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8080/kmse/
	External: 	http://**************:8080/kmse/
	Swagger文档: 	http://**************:8080/kmse/doc.html
----------------------------------------------------------</td>
<td class="MethodOfCaller">main</td>
<td class="FileOfCaller">DetectApplication.java</td>
<td class="LineOfCaller">39</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:31,818</td>
<td class="Message">Initializing Spring DispatcherServlet &#39;dispatcherServlet&#39;</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:31,819</td>
<td class="Message">Initializing Servlet &#39;dispatcherServlet&#39;</td>
<td class="MethodOfCaller">initServletBean</td>
<td class="FileOfCaller">FrameworkServlet.java</td>
<td class="LineOfCaller">525</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:31,823</td>
<td class="Message">Completed initialization in 4 ms</td>
<td class="MethodOfCaller">initServletBean</td>
<td class="FileOfCaller">FrameworkServlet.java</td>
<td class="LineOfCaller">547</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-08-15 17:47:32,012</td>
<td class="Message">Solr health check failed</td>
<td class="MethodOfCaller">logExceptionIfPresent</td>
<td class="FileOfCaller">AbstractHealthIndicator.java</td>
<td class="LineOfCaller">94</td>
</tr>
<tr><td class="Exception" colspan="6">org.apache.solr.client.solrj.SolrServerException: Server refused connection at: http://127.0.0.1:8099/solr-881
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:688)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:231)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.solr.SolrHealthIndicator$RootStatusCheck.getStatus(SolrHealthIndicator.java:116)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.solr.SolrHealthIndicator.initializeStatusCheck(SolrHealthIndicator.java:79)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.solr.SolrHealthIndicator.initializeStatusCheck(SolrHealthIndicator.java:67)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.solr.SolrHealthIndicator.doHealthCheck(SolrHealthIndicator.java:53)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:124)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:814)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:802)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1472)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1310)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1405)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.GeneratedMethodAccessor29.invoke(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.rmi/sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:360)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:200)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:197)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.security.AccessController.doPrivileged(AccessController.java:712)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.rmi/sun.rmi.transport.Transport.serviceCall(Transport.java:196)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.rmi/sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:587)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:828)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:705)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:704)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.Thread.run(Thread.java:833)
<br />Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 51 common frames omitted
<br />Caused by: java.net.ConnectException: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnect(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.Socket.connect(Socket.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 61 common frames omitted
</td></tr>
<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-08-15 17:47:32,357</td>
<td class="Message">同步KnoBaseDetail数据到Solr失败</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">507</td>
</tr>
<tr><td class="Exception" colspan="6">org.springframework.dao.DataAccessResourceFailureException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrExceptionTranslator.translateExceptionIfPossible(SolrExceptionTranslator.java:79)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:169)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.delete(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrOperations.delete(SolrOperations.java:228)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.repository.support.SimpleSolrRepository.deleteAll(SimpleSolrRepository.java:212)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService.deleteAll(KnoBaseDetailSolrService.java:313)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$FastClassBySpringCGLIB$$1d6fbb9b.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$EnhancerBySpringCGLIB$$ac026c6e.deleteAll(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailService.syncToSolr(KnoBaseDetailService.java:497)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.Thread.run(Thread.java:833)
<br />Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:940)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:903)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.lambda$delete$6(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:167)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 65 common frames omitted
<br />Caused by: java.net.ConnectException: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnect(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.Socket.connect(Socket.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 82 common frames omitted
</td></tr>
<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:32,621</td>
<td class="Message">Starting Quartz Scheduler now, after delay of 1 seconds</td>
<td class="MethodOfCaller">run</td>
<td class="FileOfCaller">SchedulerFactoryBean.java</td>
<td class="LineOfCaller">750</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:32,751</td>
<td class="Message">ClusterManager: detected 1 failed or restarted instances.</td>
<td class="MethodOfCaller">logWarnIfNonZero</td>
<td class="FileOfCaller">JobStoreSupport.java</td>
<td class="LineOfCaller">3644</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:32,752</td>
<td class="Message">ClusterManager: Scanning for instance &quot;DESKTOP-EU3ACCV1755246544624&quot;&#39;s failed in-progress jobs.</td>
<td class="MethodOfCaller">clusterRecover</td>
<td class="FileOfCaller">JobStoreSupport.java</td>
<td class="LineOfCaller">3503</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:32,761</td>
<td class="Message">Scheduler MyScheduler_$_DESKTOP-EU3ACCV1755251243489 started.</td>
<td class="MethodOfCaller">start</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">547</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:47:45,989</td>
<td class="Message">A cookie header was received [Hm_lvt_0febd9e3cacb3f627ddac64d52caac39=1754964440,1755243411,1755246569,1755248930;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-08-15 17:47:46,416</td>
<td class="Message">Unable to make field private final java.lang.Class java.lang.invoke.SerializedLambda.capturingClass accessible: module java.base does not &quot;opens java.lang.invoke&quot; to unnamed module @65e2dbf3</td>
<td class="MethodOfCaller">&lt;clinit&gt;</td>
<td class="FileOfCaller">ReflectLambdaMeta.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:48:40,009</td>
<td class="Message">Scheduler MyScheduler_$_DESKTOP-EU3ACCV1755251243489 paused.</td>
<td class="MethodOfCaller">standby</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">585</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:48:40,063</td>
<td class="Message">Shutting down Quartz Scheduler</td>
<td class="MethodOfCaller">destroy</td>
<td class="FileOfCaller">SchedulerFactoryBean.java</td>
<td class="LineOfCaller">847</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:48:40,064</td>
<td class="Message">Scheduler MyScheduler_$_DESKTOP-EU3ACCV1755251243489 shutting down.</td>
<td class="MethodOfCaller">shutdown</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">666</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:48:40,064</td>
<td class="Message">Scheduler MyScheduler_$_DESKTOP-EU3ACCV1755251243489 paused.</td>
<td class="MethodOfCaller">standby</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">585</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:48:40,064</td>
<td class="Message">Scheduler MyScheduler_$_DESKTOP-EU3ACCV1755251243489 shutdown complete.</td>
<td class="MethodOfCaller">shutdown</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">740</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:48:40,071</td>
<td class="Message">dynamic-datasource start closing ....</td>
<td class="MethodOfCaller">destroy</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">211</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:48:40,072</td>
<td class="Message">{dataSource-1} closing ...</td>
<td class="MethodOfCaller">close</td>
<td class="FileOfCaller">DruidDataSource.java</td>
<td class="LineOfCaller">2175</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:48:40,074</td>
<td class="Message">{dataSource-1} closed</td>
<td class="MethodOfCaller">close</td>
<td class="FileOfCaller">DruidDataSource.java</td>
<td class="LineOfCaller">2248</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-15 17:48:40,075</td>
<td class="Message">dynamic-datasource all closed success,bye</td>
<td class="MethodOfCaller">destroy</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">215</td>
</tr>
</table>
</body></html><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
  <head>
    <title>Logback Log Messages</title>
<style  type="text/css">
table { margin-left: 2em; margin-right: 2em; border-left: 2px solid #AAA; }
TR.even { background: #FFFFFF; }
TR.odd { background: #EAEAEA; }
TR.warn TD.Level, TR.error TD.Level, TR.fatal TD.Level {font-weight: bold; color: #FF4040 }
TD { padding-right: 1ex; padding-left: 1ex; border-right: 2px solid #AAA; }
TD.Time, TD.Date { text-align: right; font-family: courier, monospace; font-size: smaller; }
TD.Thread { text-align: left; }
TD.Level { text-align: right; }
TD.Logger { text-align: left; }
TR.header { background: #596ED5; color: #FFF; font-weight: bold; font-size: larger; }
TD.Exception { background: #A2AEE8; font-family: courier, monospace;}
</style>

  </head>
<body>
<hr/>
<p>Log session start time Sat Aug 16 12:49:36 CST 2025</p><p></p>

<table cellspacing="0">
<tr class="header">
<td class="Level">Level</td>
<td class="Date">Date</td>
<td class="Message">Message</td>
<td class="MethodOfCaller">MethodOfCaller</td>
<td class="FileOfCaller">FileOfCaller</td>
<td class="LineOfCaller">LineOfCaller</td>
</tr>


<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:36,232</td>
<td class="Message">HV000001: Hibernate Validator 6.2.5.Final</td>
<td class="MethodOfCaller">&lt;clinit&gt;</td>
<td class="FileOfCaller">Version.java</td>
<td class="LineOfCaller">21</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:36,299</td>
<td class="Message">Starting WordCompassApplication using Java 17.0.6 on DESKTOP-EU3ACCV with PID 31256 (E:\my-work-2025\work-wordcompass\wordCompass\service\wordCompass-server\build\classes\java\main started by Administrator in E:\my-work-2025\work-wordcompass\wordCompass)</td>
<td class="MethodOfCaller">logStarting</td>
<td class="FileOfCaller">StartupInfoLogger.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:36,301</td>
<td class="Message">The following 1 profile is active: &quot;dev&quot;</td>
<td class="MethodOfCaller">logStartupProfileInfo</td>
<td class="FileOfCaller">SpringApplication.java</td>
<td class="LineOfCaller">637</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:38,210</td>
<td class="Message">Multiple Spring Data modules found, entering strict repository configuration mode</td>
<td class="MethodOfCaller">multipleStoresDetected</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">262</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:38,212</td>
<td class="Message">Bootstrapping Spring Data Solr repositories in DEFAULT mode.</td>
<td class="MethodOfCaller">registerRepositoriesIn</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">132</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:38,408</td>
<td class="Message">Finished Spring Data repository scanning in 182 ms. Found 1 Solr repository interfaces.</td>
<td class="MethodOfCaller">registerRepositoriesIn</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">201</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:38,930</td>
<td class="Message">Multiple Spring Data modules found, entering strict repository configuration mode</td>
<td class="MethodOfCaller">multipleStoresDetected</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">262</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:38,931</td>
<td class="Message">Bootstrapping Spring Data Redis repositories in DEFAULT mode.</td>
<td class="MethodOfCaller">registerRepositoriesIn</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">132</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:39,125</td>
<td class="Message">Spring Data Redis - Could not safely identify store assignment for repository candidate interface org.jeecg.modules.km.kno.repository.KnoBaseDetailVoRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository</td>
<td class="MethodOfCaller">isStrictRepositoryCandidate</td>
<td class="FileOfCaller">RepositoryConfigurationExtensionSupport.java</td>
<td class="LineOfCaller">349</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:39,125</td>
<td class="Message">Finished Spring Data repository scanning in 184 ms. Found 0 Redis repository interfaces.</td>
<td class="MethodOfCaller">registerRepositoriesIn</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">201</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:39,574</td>
<td class="Message">Bean &#39;spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties&#39; of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:39,578</td>
<td class="Message">Bean &#39;org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration&#39; of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:39,601</td>
<td class="Message">Bean &#39;org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration&#39; of type [org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:39,602</td>
<td class="Message">Bean &#39;org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusMetricsExportAutoConfiguration&#39; of type [org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusMetricsExportAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:39,605</td>
<td class="Message">Bean &#39;management.metrics.export.prometheus-org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusProperties&#39; of type [org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:39,607</td>
<td class="Message">Bean &#39;prometheusConfig&#39; of type [org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusPropertiesConfigAdapter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:39,610</td>
<td class="Message">Bean &#39;collectorRegistry&#39; of type [io.prometheus.client.CollectorRegistry] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:39,611</td>
<td class="Message">Bean &#39;org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration&#39; of type [org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:39,611</td>
<td class="Message">Bean &#39;micrometerClock&#39; of type [io.micrometer.core.instrument.Clock$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:39,634</td>
<td class="Message">Bean &#39;prometheusMeterRegistry&#39; of type [io.micrometer.prometheus.PrometheusMeterRegistry] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:39,638</td>
<td class="Message">Bean &#39;micrometerOptions&#39; of type [io.lettuce.core.metrics.MicrometerOptions] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:39,639</td>
<td class="Message">Bean &#39;lettuceMetrics&#39; of type [org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration$$Lambda$633/0x000000080120b5a0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:39,751</td>
<td class="Message">Bean &#39;lettuceClientResources&#39; of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:39,864</td>
<td class="Message">Bean &#39;redisConnectionFactory&#39; of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:39,871</td>
<td class="Message">Bean &#39;jeecgBaseConfig&#39; of type [org.jeecg.config.JeecgBaseConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:39,874</td>
<td class="Message">Bean &#39;shiroConfig&#39; of type [org.jeecg.config.shiro.ShiroConfig$$EnhancerBySpringCGLIB$$3bdd0ae3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:40,165</td>
<td class="Message">Bean &#39;spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties&#39; of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:40,174</td>
<td class="Message">Bean &#39;com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration&#39; of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration$$EnhancerBySpringCGLIB$$5e127a3e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:40,189</td>
<td class="Message">Bean &#39;dsProcessor&#39; of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:40,240</td>
<td class="Message">Bean &#39;shiroRealm&#39; of type [org.jeecg.config.shiro.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:40,308</td>
<td class="Message">===============(1)创建缓存管理器RedisCacheManager</td>
<td class="MethodOfCaller">redisCacheManager</td>
<td class="FileOfCaller">ShiroConfig.java</td>
<td class="LineOfCaller">247</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:40,310</td>
<td class="Message">===============(2)创建RedisManager,连接Redis..</td>
<td class="MethodOfCaller">redisManager</td>
<td class="FileOfCaller">ShiroConfig.java</td>
<td class="LineOfCaller">265</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:40,313</td>
<td class="Message">Bean &#39;redisManager&#39; of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:40,319</td>
<td class="Message">Bean &#39;securityManager&#39; of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:40,341</td>
<td class="Message">Bean &#39;authorizationAttributeSourceAdvisor&#39; of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:40,363</td>
<td class="Message">Bean &#39;org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration&#39; of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$efb9a09a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:40,367</td>
<td class="Message">Bean &#39;eventBus&#39; of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:40,711</td>
<td class="Message">Tomcat initialized with port(s): 8080 (http)</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">TomcatWebServer.java</td>
<td class="LineOfCaller">108</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:40,721</td>
<td class="Message">Initializing ProtocolHandler [&quot;http-nio-8080&quot;]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:40,722</td>
<td class="Message">Starting service [Tomcat]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:40,722</td>
<td class="Message">Starting Servlet engine: [Apache Tomcat/9.0.73]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:40,991</td>
<td class="Message">Initializing Spring embedded WebApplicationContext</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:40,991</td>
<td class="Message">Root WebApplicationContext: initialization completed in 4614 ms</td>
<td class="MethodOfCaller">prepareWebApplicationContext</td>
<td class="FileOfCaller">ServletWebServerApplicationContext.java</td>
<td class="LineOfCaller">292</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:42,044</td>
<td class="Message">{dataSource-1,master} inited</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">DruidDataSource.java</td>
<td class="LineOfCaller">1010</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:42,046</td>
<td class="Message">dynamic-datasource - add a datasource named [master] success</td>
<td class="MethodOfCaller">addDataSource</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">154</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:42,046</td>
<td class="Message">dynamic-datasource initial loaded [1] datasource,primary datasource named [master]</td>
<td class="MethodOfCaller">afterPropertiesSet</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">237</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:43,512</td>
<td class="Message"> --- redis config init --- </td>
<td class="MethodOfCaller">redisTemplate</td>
<td class="FileOfCaller">RedisConfig.java</td>
<td class="LineOfCaller">56</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:46,279</td>
<td class="Message">知识服务RestTemplate初始化完成，连接超时: 30000ms，读取超时: 600000ms，最大重试次数: 3，重试间隔: 1000ms</td>
<td class="MethodOfCaller">initKnowledgeRestTemplate</td>
<td class="FileOfCaller">KnowledgeScriptService.java</td>
<td class="LineOfCaller">66</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:46,363</td>
<td class="Message">向量服务RestTemplate初始化完成，连接超时: 30000ms，读取超时: 300000ms</td>
<td class="MethodOfCaller">initVectorRestTemplate</td>
<td class="FileOfCaller">VectorStoreService.java</td>
<td class="LineOfCaller">52</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:46,939</td>
<td class="Message">Using default implementation for ThreadExecutor</td>
<td class="MethodOfCaller">instantiate</td>
<td class="FileOfCaller">StdSchedulerFactory.java</td>
<td class="LineOfCaller">1220</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:46,942</td>
<td class="Message">Job execution threads will use class loader of thread: main</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">SimpleThreadPool.java</td>
<td class="LineOfCaller">268</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:46,953</td>
<td class="Message">Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl</td>
<td class="MethodOfCaller">&lt;init&gt;</td>
<td class="FileOfCaller">SchedulerSignalerImpl.java</td>
<td class="LineOfCaller">61</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:46,953</td>
<td class="Message">Quartz Scheduler v.2.3.2 created.</td>
<td class="MethodOfCaller">&lt;init&gt;</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">229</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:46,955</td>
<td class="Message">Using db table-based data access locking (synchronization).</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">JobStoreSupport.java</td>
<td class="LineOfCaller">672</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:46,957</td>
<td class="Message">JobStoreCMT initialized.</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">JobStoreCMT.java</td>
<td class="LineOfCaller">145</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:46,957</td>
<td class="Message">Scheduler meta-data: Quartz Scheduler (v2.3.2) &#39;MyScheduler&#39; with instanceId &#39;DESKTOP-EU3ACCV1755319786941&#39;
  Scheduler class: &#39;org.quartz.core.QuartzScheduler&#39; - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool &#39;org.quartz.simpl.SimpleThreadPool&#39; - with 10 threads.
  Using job-store &#39;org.springframework.scheduling.quartz.LocalDataSourceJobStore&#39; - which supports persistence. and is clustered.
</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">294</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:46,958</td>
<td class="Message">Quartz scheduler &#39;MyScheduler&#39; initialized from an externally provided properties instance.</td>
<td class="MethodOfCaller">instantiate</td>
<td class="FileOfCaller">StdSchedulerFactory.java</td>
<td class="LineOfCaller">1374</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:46,958</td>
<td class="Message">Quartz scheduler version: 2.3.2</td>
<td class="MethodOfCaller">instantiate</td>
<td class="FileOfCaller">StdSchedulerFactory.java</td>
<td class="LineOfCaller">1378</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:46,958</td>
<td class="Message">JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@3652ce04</td>
<td class="MethodOfCaller">setJobFactory</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">2293</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:48,966</td>
<td class="Message">Exposing 2 endpoint(s) beneath base path &#39;/actuator&#39;</td>
<td class="MethodOfCaller">&lt;init&gt;</td>
<td class="FileOfCaller">EndpointLinksResolver.java</td>
<td class="LineOfCaller">58</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:49,091</td>
<td class="Message"> Init CodeGenerate Config [ Get Db Config From application.yml ] </td>
<td class="MethodOfCaller">initCodeGenerateDbConfig</td>
<td class="FileOfCaller">CodeGenerateDbConfig.java</td>
<td class="LineOfCaller">46</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:50,553</td>
<td class="Message">Starting ProtocolHandler [&quot;http-nio-8080&quot;]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:50,584</td>
<td class="Message">Tomcat started on port(s): 8080 (http) with context path &#39;/wordCompassApi&#39;</td>
<td class="MethodOfCaller">start</td>
<td class="FileOfCaller">TomcatWebServer.java</td>
<td class="LineOfCaller">220</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:52,946</td>
<td class="Message">Will start Quartz Scheduler [MyScheduler] in 1 seconds</td>
<td class="MethodOfCaller">startScheduler</td>
<td class="FileOfCaller">SchedulerFactoryBean.java</td>
<td class="LineOfCaller">734</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:52,962</td>
<td class="Message">开始同步KnoBaseDetail数据到Solr...</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">490</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:52,967</td>
<td class="Message">Started WordCompassApplication in 17.559 seconds (JVM running for 20.138)</td>
<td class="MethodOfCaller">logStarted</td>
<td class="FileOfCaller">StartupInfoLogger.java</td>
<td class="LineOfCaller">61</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:52,979</td>
<td class="Message"> Init Code Generate Template [ 检测如果是JAR启动环境，Copy模板到config目录 ] </td>
<td class="MethodOfCaller">onApplicationEvent</td>
<td class="FileOfCaller">CodeTemplateInitListener.java</td>
<td class="LineOfCaller">29</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:52,982</td>
<td class="Message">
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8080/wordCompassApi/
	External: 	http://*************:8080/wordCompassApi/
	Swagger文档: 	http://*************:8080/wordCompassApi/doc.html
----------------------------------------------------------</td>
<td class="MethodOfCaller">main</td>
<td class="FileOfCaller">WordCompassApplication.java</td>
<td class="LineOfCaller">39</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-08-16 12:49:53,613</td>
<td class="Message">同步KnoBaseDetail数据到Solr失败</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">507</td>
</tr>
<tr><td class="Exception" colspan="6">org.springframework.dao.DataAccessResourceFailureException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrExceptionTranslator.translateExceptionIfPossible(SolrExceptionTranslator.java:79)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:169)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.delete(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrOperations.delete(SolrOperations.java:228)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.repository.support.SimpleSolrRepository.deleteAll(SimpleSolrRepository.java:212)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService.deleteAll(KnoBaseDetailSolrService.java:313)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$FastClassBySpringCGLIB$$1d6fbb9b.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$EnhancerBySpringCGLIB$$daa8b9a9.deleteAll(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailService.syncToSolr(KnoBaseDetailService.java:497)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.Thread.run(Thread.java:833)
<br />Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:940)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:903)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.lambda$delete$6(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:167)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 65 common frames omitted
<br />Caused by: java.net.ConnectException: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnect(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.Socket.connect(Socket.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 82 common frames omitted
</td></tr>
<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:53,950</td>
<td class="Message">Starting Quartz Scheduler now, after delay of 1 seconds</td>
<td class="MethodOfCaller">run</td>
<td class="FileOfCaller">SchedulerFactoryBean.java</td>
<td class="LineOfCaller">750</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:54,058</td>
<td class="Message">ClusterManager: detected 1 failed or restarted instances.</td>
<td class="MethodOfCaller">logWarnIfNonZero</td>
<td class="FileOfCaller">JobStoreSupport.java</td>
<td class="LineOfCaller">3644</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:54,059</td>
<td class="Message">ClusterManager: Scanning for instance &quot;DESKTOP-EU3ACCV1755251243489&quot;&#39;s failed in-progress jobs.</td>
<td class="MethodOfCaller">clusterRecover</td>
<td class="FileOfCaller">JobStoreSupport.java</td>
<td class="LineOfCaller">3503</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:49:54,066</td>
<td class="Message">Scheduler MyScheduler_$_DESKTOP-EU3ACCV1755319786941 started.</td>
<td class="MethodOfCaller">start</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">547</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:50:26,981</td>
<td class="Message">A cookie header was received [Hm_lvt_0febd9e3cacb3f627ddac64d52caac39=1755243411,1755246569,1755248930,1755319815;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:50:26,994</td>
<td class="Message">Initializing Spring DispatcherServlet &#39;dispatcherServlet&#39;</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:50:26,994</td>
<td class="Message">Initializing Servlet &#39;dispatcherServlet&#39;</td>
<td class="MethodOfCaller">initServletBean</td>
<td class="FileOfCaller">FrameworkServlet.java</td>
<td class="LineOfCaller">525</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:50:26,999</td>
<td class="Message">Completed initialization in 5 ms</td>
<td class="MethodOfCaller">initServletBean</td>
<td class="FileOfCaller">FrameworkServlet.java</td>
<td class="LineOfCaller">547</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:50:27,176</td>
<td class="Message">获取验证码，Redis key = a3420689587752d348c76d0baf25c3fd，checkCode = tfb8</td>
<td class="MethodOfCaller">randomImage</td>
<td class="FileOfCaller">LoginController.java</td>
<td class="LineOfCaller">542</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-08-16 12:51:17,368</td>
<td class="Message">Unable to make field private final java.lang.Class java.lang.invoke.SerializedLambda.capturingClass accessible: module java.base does not &quot;opens java.lang.invoke&quot; to unnamed module @65e2dbf3</td>
<td class="MethodOfCaller">&lt;clinit&gt;</td>
<td class="FileOfCaller">ReflectLambdaMeta.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:51:17,480</td>
<td class="Message"> 登录接口用户的租户ID = 1000</td>
<td class="MethodOfCaller">setLoginTenant</td>
<td class="FileOfCaller">SysUserServiceImpl.java</td>
<td class="LineOfCaller">901</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:51:17,519</td>
<td class="Message">redis remove key:sys:cache:encrypt:user::admin</td>
<td class="MethodOfCaller">remove</td>
<td class="FileOfCaller">JeecgRedisCacheWriter.java</td>
<td class="LineOfCaller">113</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:51:17,663</td>
<td class="Message">加密操作，Aspect程序耗时：8ms</td>
<td class="MethodOfCaller">around</td>
<td class="FileOfCaller">SensitiveDataAspect.java</td>
<td class="LineOfCaller">76</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:51:52,738</td>
<td class="Message">查询磁盘信息:6个</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">117</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:51:52,769</td>
<td class="Message">{name=OS (C:), rest=186166153216, restPPT=56, max=429700067328}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:51:52,776</td>
<td class="Message">{name=本地磁盘 (D:), rest=138221797376, restPPT=48, max=268607250432}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:51:52,781</td>
<td class="Message">{name=本地磁盘 (E:), rest=57584033792, restPPT=61, max=151161372672}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:51:52,786</td>
<td class="Message">{name=本地磁盘 (F:), rest=69867331584, restPPT=53, max=150828929024}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:51:52,793</td>
<td class="Message">{name=本地磁盘 (G:), rest=137231179776, restPPT=72, max=500170412032}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:51:52,799</td>
<td class="Message">{name=本地磁盘 (H:), rest=260168253440, restPPT=47, max=500030472192}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:53:31,587</td>
<td class="Message">======获取全部菜单数据=====耗时:39毫秒</td>
<td class="MethodOfCaller">list</td>
<td class="FileOfCaller">SysPermissionController.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:58:11,064</td>
<td class="Message">查询磁盘信息:6个</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">117</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:58:11,070</td>
<td class="Message">{name=OS (C:), rest=185906327552, restPPT=56, max=429700067328}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:58:11,076</td>
<td class="Message">{name=本地磁盘 (D:), rest=138221797376, restPPT=48, max=268607250432}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:58:11,079</td>
<td class="Message">{name=本地磁盘 (E:), rest=57583968256, restPPT=61, max=151161372672}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:58:11,085</td>
<td class="Message">{name=本地磁盘 (F:), rest=69867331584, restPPT=53, max=150828929024}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:58:11,090</td>
<td class="Message">{name=本地磁盘 (G:), rest=137231179776, restPPT=72, max=500170412032}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:58:11,092</td>
<td class="Message">{name=本地磁盘 (H:), rest=260168253440, restPPT=47, max=500030472192}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:59:33,349</td>
<td class="Message">查询磁盘信息:6个</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">117</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:59:33,353</td>
<td class="Message">{name=OS (C:), rest=185903562752, restPPT=56, max=429700067328}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:59:33,365</td>
<td class="Message">{name=本地磁盘 (D:), rest=138221797376, restPPT=48, max=268607250432}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:59:33,370</td>
<td class="Message">{name=本地磁盘 (E:), rest=57583833088, restPPT=61, max=151161372672}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:59:33,374</td>
<td class="Message">{name=本地磁盘 (F:), rest=69867331584, restPPT=53, max=150828929024}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:59:33,376</td>
<td class="Message">{name=本地磁盘 (G:), rest=137231179776, restPPT=72, max=500170412032}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 12:59:33,380</td>
<td class="Message">{name=本地磁盘 (H:), rest=260168253440, restPPT=47, max=500030472192}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 13:04:52,963</td>
<td class="Message">开始同步KnoBaseDetail数据到Solr...</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">490</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-08-16 13:04:53,121</td>
<td class="Message">同步KnoBaseDetail数据到Solr失败</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">507</td>
</tr>
<tr><td class="Exception" colspan="6">org.springframework.dao.DataAccessResourceFailureException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrExceptionTranslator.translateExceptionIfPossible(SolrExceptionTranslator.java:79)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:169)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.delete(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrOperations.delete(SolrOperations.java:228)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.repository.support.SimpleSolrRepository.deleteAll(SimpleSolrRepository.java:212)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService.deleteAll(KnoBaseDetailSolrService.java:313)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$FastClassBySpringCGLIB$$1d6fbb9b.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$EnhancerBySpringCGLIB$$daa8b9a9.deleteAll(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailService.syncToSolr(KnoBaseDetailService.java:497)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.Thread.run(Thread.java:833)
<br />Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:940)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:903)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.lambda$delete$6(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:167)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 65 common frames omitted
<br />Caused by: java.net.ConnectException: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnect(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.Socket.connect(Socket.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 82 common frames omitted
</td></tr>
<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 13:19:52,963</td>
<td class="Message">开始同步KnoBaseDetail数据到Solr...</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">490</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-08-16 13:19:53,120</td>
<td class="Message">同步KnoBaseDetail数据到Solr失败</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">507</td>
</tr>
<tr><td class="Exception" colspan="6">org.springframework.dao.DataAccessResourceFailureException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrExceptionTranslator.translateExceptionIfPossible(SolrExceptionTranslator.java:79)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:169)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.delete(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrOperations.delete(SolrOperations.java:228)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.repository.support.SimpleSolrRepository.deleteAll(SimpleSolrRepository.java:212)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService.deleteAll(KnoBaseDetailSolrService.java:313)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$FastClassBySpringCGLIB$$1d6fbb9b.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$EnhancerBySpringCGLIB$$daa8b9a9.deleteAll(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailService.syncToSolr(KnoBaseDetailService.java:497)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.Thread.run(Thread.java:833)
<br />Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:940)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:903)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.lambda$delete$6(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:167)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 65 common frames omitted
<br />Caused by: java.net.ConnectException: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnect(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.Socket.connect(Socket.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 82 common frames omitted
</td></tr>
<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 13:34:52,963</td>
<td class="Message">开始同步KnoBaseDetail数据到Solr...</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">490</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-08-16 13:34:53,167</td>
<td class="Message">同步KnoBaseDetail数据到Solr失败</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">507</td>
</tr>
<tr><td class="Exception" colspan="6">org.springframework.dao.DataAccessResourceFailureException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrExceptionTranslator.translateExceptionIfPossible(SolrExceptionTranslator.java:79)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:169)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.delete(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrOperations.delete(SolrOperations.java:228)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.repository.support.SimpleSolrRepository.deleteAll(SimpleSolrRepository.java:212)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService.deleteAll(KnoBaseDetailSolrService.java:313)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$FastClassBySpringCGLIB$$1d6fbb9b.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$EnhancerBySpringCGLIB$$daa8b9a9.deleteAll(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailService.syncToSolr(KnoBaseDetailService.java:497)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.Thread.run(Thread.java:833)
<br />Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:940)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:903)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.lambda$delete$6(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:167)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 65 common frames omitted
<br />Caused by: java.net.ConnectException: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnect(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.Socket.connect(Socket.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 82 common frames omitted
</td></tr>
<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 13:49:52,963</td>
<td class="Message">开始同步KnoBaseDetail数据到Solr...</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">490</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-08-16 13:49:53,126</td>
<td class="Message">同步KnoBaseDetail数据到Solr失败</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">507</td>
</tr>
<tr><td class="Exception" colspan="6">org.springframework.dao.DataAccessResourceFailureException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrExceptionTranslator.translateExceptionIfPossible(SolrExceptionTranslator.java:79)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:169)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.delete(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrOperations.delete(SolrOperations.java:228)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.repository.support.SimpleSolrRepository.deleteAll(SimpleSolrRepository.java:212)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService.deleteAll(KnoBaseDetailSolrService.java:313)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$FastClassBySpringCGLIB$$1d6fbb9b.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$EnhancerBySpringCGLIB$$daa8b9a9.deleteAll(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailService.syncToSolr(KnoBaseDetailService.java:497)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.Thread.run(Thread.java:833)
<br />Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:940)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:903)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.lambda$delete$6(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:167)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 65 common frames omitted
<br />Caused by: java.net.ConnectException: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnect(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.Socket.connect(Socket.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 82 common frames omitted
</td></tr>
<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 14:04:52,963</td>
<td class="Message">开始同步KnoBaseDetail数据到Solr...</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">490</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-08-16 14:04:53,114</td>
<td class="Message">同步KnoBaseDetail数据到Solr失败</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">507</td>
</tr>
<tr><td class="Exception" colspan="6">org.springframework.dao.DataAccessResourceFailureException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrExceptionTranslator.translateExceptionIfPossible(SolrExceptionTranslator.java:79)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:169)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.delete(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrOperations.delete(SolrOperations.java:228)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.repository.support.SimpleSolrRepository.deleteAll(SimpleSolrRepository.java:212)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService.deleteAll(KnoBaseDetailSolrService.java:313)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$FastClassBySpringCGLIB$$1d6fbb9b.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$EnhancerBySpringCGLIB$$daa8b9a9.deleteAll(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailService.syncToSolr(KnoBaseDetailService.java:497)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.Thread.run(Thread.java:833)
<br />Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:940)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:903)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.lambda$delete$6(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:167)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 65 common frames omitted
<br />Caused by: java.net.ConnectException: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnect(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.Socket.connect(Socket.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 82 common frames omitted
</td></tr>
<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 14:19:52,964</td>
<td class="Message">开始同步KnoBaseDetail数据到Solr...</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">490</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-08-16 14:19:53,165</td>
<td class="Message">同步KnoBaseDetail数据到Solr失败</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">507</td>
</tr>
<tr><td class="Exception" colspan="6">org.springframework.dao.DataAccessResourceFailureException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrExceptionTranslator.translateExceptionIfPossible(SolrExceptionTranslator.java:79)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:169)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.delete(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrOperations.delete(SolrOperations.java:228)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.repository.support.SimpleSolrRepository.deleteAll(SimpleSolrRepository.java:212)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService.deleteAll(KnoBaseDetailSolrService.java:313)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$FastClassBySpringCGLIB$$1d6fbb9b.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$EnhancerBySpringCGLIB$$daa8b9a9.deleteAll(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailService.syncToSolr(KnoBaseDetailService.java:497)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.Thread.run(Thread.java:833)
<br />Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:940)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:903)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.lambda$delete$6(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:167)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 65 common frames omitted
<br />Caused by: java.net.ConnectException: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnect(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.Socket.connect(Socket.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 82 common frames omitted
</td></tr>
<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 14:34:52,963</td>
<td class="Message">开始同步KnoBaseDetail数据到Solr...</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">490</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-08-16 14:34:53,139</td>
<td class="Message">同步KnoBaseDetail数据到Solr失败</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">507</td>
</tr>
<tr><td class="Exception" colspan="6">org.springframework.dao.DataAccessResourceFailureException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrExceptionTranslator.translateExceptionIfPossible(SolrExceptionTranslator.java:79)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:169)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.delete(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrOperations.delete(SolrOperations.java:228)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.repository.support.SimpleSolrRepository.deleteAll(SimpleSolrRepository.java:212)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService.deleteAll(KnoBaseDetailSolrService.java:313)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$FastClassBySpringCGLIB$$1d6fbb9b.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$EnhancerBySpringCGLIB$$daa8b9a9.deleteAll(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailService.syncToSolr(KnoBaseDetailService.java:497)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.Thread.run(Thread.java:833)
<br />Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:940)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:903)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.lambda$delete$6(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:167)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 65 common frames omitted
<br />Caused by: java.net.ConnectException: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnect(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.Socket.connect(Socket.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 82 common frames omitted
</td></tr>
<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 14:49:52,962</td>
<td class="Message">开始同步KnoBaseDetail数据到Solr...</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">490</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-08-16 14:49:53,160</td>
<td class="Message">同步KnoBaseDetail数据到Solr失败</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">507</td>
</tr>
<tr><td class="Exception" colspan="6">org.springframework.dao.DataAccessResourceFailureException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrExceptionTranslator.translateExceptionIfPossible(SolrExceptionTranslator.java:79)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:169)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.delete(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrOperations.delete(SolrOperations.java:228)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.repository.support.SimpleSolrRepository.deleteAll(SimpleSolrRepository.java:212)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService.deleteAll(KnoBaseDetailSolrService.java:313)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$FastClassBySpringCGLIB$$1d6fbb9b.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$EnhancerBySpringCGLIB$$daa8b9a9.deleteAll(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailService.syncToSolr(KnoBaseDetailService.java:497)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.Thread.run(Thread.java:833)
<br />Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:940)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:903)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.lambda$delete$6(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:167)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 65 common frames omitted
<br />Caused by: java.net.ConnectException: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnect(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.Socket.connect(Socket.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 82 common frames omitted
</td></tr>
<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 15:04:52,962</td>
<td class="Message">开始同步KnoBaseDetail数据到Solr...</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">490</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-08-16 15:04:53,125</td>
<td class="Message">同步KnoBaseDetail数据到Solr失败</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">507</td>
</tr>
<tr><td class="Exception" colspan="6">org.springframework.dao.DataAccessResourceFailureException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrExceptionTranslator.translateExceptionIfPossible(SolrExceptionTranslator.java:79)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:169)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.delete(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrOperations.delete(SolrOperations.java:228)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.repository.support.SimpleSolrRepository.deleteAll(SimpleSolrRepository.java:212)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService.deleteAll(KnoBaseDetailSolrService.java:313)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$FastClassBySpringCGLIB$$1d6fbb9b.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$EnhancerBySpringCGLIB$$daa8b9a9.deleteAll(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailService.syncToSolr(KnoBaseDetailService.java:497)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.Thread.run(Thread.java:833)
<br />Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:940)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:903)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.lambda$delete$6(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:167)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 65 common frames omitted
<br />Caused by: java.net.ConnectException: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnect(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.Socket.connect(Socket.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 82 common frames omitted
</td></tr>
<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 15:19:52,963</td>
<td class="Message">开始同步KnoBaseDetail数据到Solr...</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">490</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-08-16 15:19:53,167</td>
<td class="Message">同步KnoBaseDetail数据到Solr失败</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">507</td>
</tr>
<tr><td class="Exception" colspan="6">org.springframework.dao.DataAccessResourceFailureException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrExceptionTranslator.translateExceptionIfPossible(SolrExceptionTranslator.java:79)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:169)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.delete(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrOperations.delete(SolrOperations.java:228)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.repository.support.SimpleSolrRepository.deleteAll(SimpleSolrRepository.java:212)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService.deleteAll(KnoBaseDetailSolrService.java:313)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$FastClassBySpringCGLIB$$1d6fbb9b.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$EnhancerBySpringCGLIB$$daa8b9a9.deleteAll(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailService.syncToSolr(KnoBaseDetailService.java:497)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.Thread.run(Thread.java:833)
<br />Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:940)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:903)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.lambda$delete$6(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:167)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 65 common frames omitted
<br />Caused by: java.net.ConnectException: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnect(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.Socket.connect(Socket.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 82 common frames omitted
</td></tr>
<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 15:34:52,963</td>
<td class="Message">开始同步KnoBaseDetail数据到Solr...</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">490</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-08-16 15:34:53,113</td>
<td class="Message">同步KnoBaseDetail数据到Solr失败</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">507</td>
</tr>
<tr><td class="Exception" colspan="6">org.springframework.dao.DataAccessResourceFailureException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrExceptionTranslator.translateExceptionIfPossible(SolrExceptionTranslator.java:79)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:169)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.delete(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrOperations.delete(SolrOperations.java:228)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.repository.support.SimpleSolrRepository.deleteAll(SimpleSolrRepository.java:212)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService.deleteAll(KnoBaseDetailSolrService.java:313)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$FastClassBySpringCGLIB$$1d6fbb9b.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$EnhancerBySpringCGLIB$$daa8b9a9.deleteAll(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailService.syncToSolr(KnoBaseDetailService.java:497)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.Thread.run(Thread.java:833)
<br />Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:940)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:903)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.lambda$delete$6(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:167)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 65 common frames omitted
<br />Caused by: java.net.ConnectException: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnect(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.Socket.connect(Socket.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 82 common frames omitted
</td></tr>
<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 15:49:52,962</td>
<td class="Message">开始同步KnoBaseDetail数据到Solr...</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">490</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-08-16 15:49:53,119</td>
<td class="Message">同步KnoBaseDetail数据到Solr失败</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">507</td>
</tr>
<tr><td class="Exception" colspan="6">org.springframework.dao.DataAccessResourceFailureException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrExceptionTranslator.translateExceptionIfPossible(SolrExceptionTranslator.java:79)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:169)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.delete(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrOperations.delete(SolrOperations.java:228)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.repository.support.SimpleSolrRepository.deleteAll(SimpleSolrRepository.java:212)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService.deleteAll(KnoBaseDetailSolrService.java:313)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$FastClassBySpringCGLIB$$1d6fbb9b.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$EnhancerBySpringCGLIB$$daa8b9a9.deleteAll(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailService.syncToSolr(KnoBaseDetailService.java:497)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.Thread.run(Thread.java:833)
<br />Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:940)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:903)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.lambda$delete$6(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:167)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 65 common frames omitted
<br />Caused by: java.net.ConnectException: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnect(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.Socket.connect(Socket.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 82 common frames omitted
</td></tr>
<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:04:52,967</td>
<td class="Message">开始同步KnoBaseDetail数据到Solr...</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">490</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-08-16 16:04:53,146</td>
<td class="Message">同步KnoBaseDetail数据到Solr失败</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">507</td>
</tr>
<tr><td class="Exception" colspan="6">org.springframework.dao.DataAccessResourceFailureException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrExceptionTranslator.translateExceptionIfPossible(SolrExceptionTranslator.java:79)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:169)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.delete(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrOperations.delete(SolrOperations.java:228)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.repository.support.SimpleSolrRepository.deleteAll(SimpleSolrRepository.java:212)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService.deleteAll(KnoBaseDetailSolrService.java:313)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$FastClassBySpringCGLIB$$1d6fbb9b.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$EnhancerBySpringCGLIB$$daa8b9a9.deleteAll(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailService.syncToSolr(KnoBaseDetailService.java:497)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.Thread.run(Thread.java:833)
<br />Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:940)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:903)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.lambda$delete$6(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:167)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 65 common frames omitted
<br />Caused by: java.net.ConnectException: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnect(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.Socket.connect(Socket.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 82 common frames omitted
</td></tr>
<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:19:52,963</td>
<td class="Message">开始同步KnoBaseDetail数据到Solr...</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">490</td>
</tr>

<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-08-16 16:19:53,116</td>
<td class="Message">同步KnoBaseDetail数据到Solr失败</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">507</td>
</tr>
<tr><td class="Exception" colspan="6">org.springframework.dao.DataAccessResourceFailureException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrExceptionTranslator.translateExceptionIfPossible(SolrExceptionTranslator.java:79)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:169)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.delete(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrOperations.delete(SolrOperations.java:228)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.repository.support.SimpleSolrRepository.deleteAll(SimpleSolrRepository.java:212)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService.deleteAll(KnoBaseDetailSolrService.java:313)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$FastClassBySpringCGLIB$$1d6fbb9b.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$EnhancerBySpringCGLIB$$daa8b9a9.deleteAll(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailService.syncToSolr(KnoBaseDetailService.java:497)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.Thread.run(Thread.java:833)
<br />Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:940)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:903)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.lambda$delete$6(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:167)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 65 common frames omitted
<br />Caused by: java.net.ConnectException: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnect(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.Socket.connect(Socket.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 82 common frames omitted
</td></tr>
<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:29:29,432</td>
<td class="Message">Scheduler MyScheduler_$_DESKTOP-EU3ACCV1755319786941 paused.</td>
<td class="MethodOfCaller">standby</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">585</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:29:29,490</td>
<td class="Message">Shutting down Quartz Scheduler</td>
<td class="MethodOfCaller">destroy</td>
<td class="FileOfCaller">SchedulerFactoryBean.java</td>
<td class="LineOfCaller">847</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:29:29,491</td>
<td class="Message">Scheduler MyScheduler_$_DESKTOP-EU3ACCV1755319786941 shutting down.</td>
<td class="MethodOfCaller">shutdown</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">666</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:29:29,491</td>
<td class="Message">Scheduler MyScheduler_$_DESKTOP-EU3ACCV1755319786941 paused.</td>
<td class="MethodOfCaller">standby</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">585</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:29:29,492</td>
<td class="Message">Scheduler MyScheduler_$_DESKTOP-EU3ACCV1755319786941 shutdown complete.</td>
<td class="MethodOfCaller">shutdown</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">740</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:29:29,504</td>
<td class="Message">dynamic-datasource start closing ....</td>
<td class="MethodOfCaller">destroy</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">211</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:29:29,505</td>
<td class="Message">{dataSource-1} closing ...</td>
<td class="MethodOfCaller">close</td>
<td class="FileOfCaller">DruidDataSource.java</td>
<td class="LineOfCaller">2175</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:29:29,508</td>
<td class="Message">{dataSource-1} closed</td>
<td class="MethodOfCaller">close</td>
<td class="FileOfCaller">DruidDataSource.java</td>
<td class="LineOfCaller">2248</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:29:29,508</td>
<td class="Message">dynamic-datasource all closed success,bye</td>
<td class="MethodOfCaller">destroy</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">215</td>
</tr>
</table>
</body></html><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
  <head>
    <title>Logback Log Messages</title>
<style  type="text/css">
table { margin-left: 2em; margin-right: 2em; border-left: 2px solid #AAA; }
TR.even { background: #FFFFFF; }
TR.odd { background: #EAEAEA; }
TR.warn TD.Level, TR.error TD.Level, TR.fatal TD.Level {font-weight: bold; color: #FF4040 }
TD { padding-right: 1ex; padding-left: 1ex; border-right: 2px solid #AAA; }
TD.Time, TD.Date { text-align: right; font-family: courier, monospace; font-size: smaller; }
TD.Thread { text-align: left; }
TD.Level { text-align: right; }
TD.Logger { text-align: left; }
TR.header { background: #596ED5; color: #FFF; font-weight: bold; font-size: larger; }
TD.Exception { background: #A2AEE8; font-family: courier, monospace;}
</style>

  </head>
<body>
<hr/>
<p>Log session start time Sat Aug 16 16:30:35 CST 2025</p><p></p>

<table cellspacing="0">
<tr class="header">
<td class="Level">Level</td>
<td class="Date">Date</td>
<td class="Message">Message</td>
<td class="MethodOfCaller">MethodOfCaller</td>
<td class="FileOfCaller">FileOfCaller</td>
<td class="LineOfCaller">LineOfCaller</td>
</tr>


<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:35,916</td>
<td class="Message">HV000001: Hibernate Validator 6.2.5.Final</td>
<td class="MethodOfCaller">&lt;clinit&gt;</td>
<td class="FileOfCaller">Version.java</td>
<td class="LineOfCaller">21</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:35,966</td>
<td class="Message">Starting WordCompassApplication using Java 17.0.6 on DESKTOP-EU3ACCV with PID 24364 (E:\my-work-2025\work-wordcompass\wordCompass\service\wordCompass-server\build\classes\java\main started by Administrator in E:\my-work-2025\work-wordcompass\wordCompass)</td>
<td class="MethodOfCaller">logStarting</td>
<td class="FileOfCaller">StartupInfoLogger.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:35,967</td>
<td class="Message">The following 1 profile is active: &quot;dev&quot;</td>
<td class="MethodOfCaller">logStartupProfileInfo</td>
<td class="FileOfCaller">SpringApplication.java</td>
<td class="LineOfCaller">637</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:37,637</td>
<td class="Message">Multiple Spring Data modules found, entering strict repository configuration mode</td>
<td class="MethodOfCaller">multipleStoresDetected</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">262</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:37,638</td>
<td class="Message">Bootstrapping Spring Data Solr repositories in DEFAULT mode.</td>
<td class="MethodOfCaller">registerRepositoriesIn</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">132</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:37,827</td>
<td class="Message">Finished Spring Data repository scanning in 176 ms. Found 1 Solr repository interfaces.</td>
<td class="MethodOfCaller">registerRepositoriesIn</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">201</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:38,323</td>
<td class="Message">Multiple Spring Data modules found, entering strict repository configuration mode</td>
<td class="MethodOfCaller">multipleStoresDetected</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">262</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:38,324</td>
<td class="Message">Bootstrapping Spring Data Redis repositories in DEFAULT mode.</td>
<td class="MethodOfCaller">registerRepositoriesIn</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">132</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:38,505</td>
<td class="Message">Spring Data Redis - Could not safely identify store assignment for repository candidate interface org.jeecg.modules.km.kno.repository.KnoBaseDetailVoRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository</td>
<td class="MethodOfCaller">isStrictRepositoryCandidate</td>
<td class="FileOfCaller">RepositoryConfigurationExtensionSupport.java</td>
<td class="LineOfCaller">349</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:38,506</td>
<td class="Message">Finished Spring Data repository scanning in 172 ms. Found 0 Redis repository interfaces.</td>
<td class="MethodOfCaller">registerRepositoriesIn</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">201</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:38,968</td>
<td class="Message">Bean &#39;spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties&#39; of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:38,973</td>
<td class="Message">Bean &#39;org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration&#39; of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:38,997</td>
<td class="Message">Bean &#39;org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration&#39; of type [org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:38,999</td>
<td class="Message">Bean &#39;org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusMetricsExportAutoConfiguration&#39; of type [org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusMetricsExportAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:39,004</td>
<td class="Message">Bean &#39;management.metrics.export.prometheus-org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusProperties&#39; of type [org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:39,007</td>
<td class="Message">Bean &#39;prometheusConfig&#39; of type [org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusPropertiesConfigAdapter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:39,009</td>
<td class="Message">Bean &#39;collectorRegistry&#39; of type [io.prometheus.client.CollectorRegistry] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:39,011</td>
<td class="Message">Bean &#39;org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration&#39; of type [org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:39,011</td>
<td class="Message">Bean &#39;micrometerClock&#39; of type [io.micrometer.core.instrument.Clock$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:39,037</td>
<td class="Message">Bean &#39;prometheusMeterRegistry&#39; of type [io.micrometer.prometheus.PrometheusMeterRegistry] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:39,042</td>
<td class="Message">Bean &#39;micrometerOptions&#39; of type [io.lettuce.core.metrics.MicrometerOptions] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:39,042</td>
<td class="Message">Bean &#39;lettuceMetrics&#39; of type [org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration$$Lambda$645/0x0000000801233898] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:39,169</td>
<td class="Message">Bean &#39;lettuceClientResources&#39; of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:39,293</td>
<td class="Message">Bean &#39;redisConnectionFactory&#39; of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:39,301</td>
<td class="Message">Bean &#39;jeecgBaseConfig&#39; of type [org.jeecg.config.JeecgBaseConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:39,304</td>
<td class="Message">Bean &#39;shiroConfig&#39; of type [org.jeecg.config.shiro.ShiroConfig$$EnhancerBySpringCGLIB$$4f43eaa4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:39,582</td>
<td class="Message">Bean &#39;spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties&#39; of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:39,589</td>
<td class="Message">Bean &#39;com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration&#39; of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration$$EnhancerBySpringCGLIB$$717959ff] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:39,603</td>
<td class="Message">Bean &#39;dsProcessor&#39; of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:39,651</td>
<td class="Message">Bean &#39;shiroRealm&#39; of type [org.jeecg.config.shiro.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:39,710</td>
<td class="Message">===============(1)创建缓存管理器RedisCacheManager</td>
<td class="MethodOfCaller">redisCacheManager</td>
<td class="FileOfCaller">ShiroConfig.java</td>
<td class="LineOfCaller">247</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:39,713</td>
<td class="Message">===============(2)创建RedisManager,连接Redis..</td>
<td class="MethodOfCaller">redisManager</td>
<td class="FileOfCaller">ShiroConfig.java</td>
<td class="LineOfCaller">265</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:39,715</td>
<td class="Message">Bean &#39;redisManager&#39; of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:39,722</td>
<td class="Message">Bean &#39;securityManager&#39; of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:39,745</td>
<td class="Message">Bean &#39;authorizationAttributeSourceAdvisor&#39; of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:39,770</td>
<td class="Message">Bean &#39;org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration&#39; of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$320805b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:39,774</td>
<td class="Message">Bean &#39;eventBus&#39; of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:40,158</td>
<td class="Message">Tomcat initialized with port(s): 8080 (http)</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">TomcatWebServer.java</td>
<td class="LineOfCaller">108</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:40,172</td>
<td class="Message">Initializing ProtocolHandler [&quot;http-nio-8080&quot;]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:40,172</td>
<td class="Message">Starting service [Tomcat]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:40,172</td>
<td class="Message">Starting Servlet engine: [Apache Tomcat/9.0.73]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:40,496</td>
<td class="Message">Initializing Spring embedded WebApplicationContext</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:40,496</td>
<td class="Message">Root WebApplicationContext: initialization completed in 4474 ms</td>
<td class="MethodOfCaller">prepareWebApplicationContext</td>
<td class="FileOfCaller">ServletWebServerApplicationContext.java</td>
<td class="LineOfCaller">292</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:41,543</td>
<td class="Message">{dataSource-1,master} inited</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">DruidDataSource.java</td>
<td class="LineOfCaller">1010</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:41,544</td>
<td class="Message">dynamic-datasource - add a datasource named [master] success</td>
<td class="MethodOfCaller">addDataSource</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">154</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:41,544</td>
<td class="Message">dynamic-datasource initial loaded [1] datasource,primary datasource named [master]</td>
<td class="MethodOfCaller">afterPropertiesSet</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">237</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:43,028</td>
<td class="Message"> --- redis config init --- </td>
<td class="MethodOfCaller">redisTemplate</td>
<td class="FileOfCaller">RedisConfig.java</td>
<td class="LineOfCaller">56</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:45,962</td>
<td class="Message">知识服务RestTemplate初始化完成，连接超时: 30000ms，读取超时: 600000ms，最大重试次数: 3，重试间隔: 1000ms</td>
<td class="MethodOfCaller">initKnowledgeRestTemplate</td>
<td class="FileOfCaller">KnowledgeScriptService.java</td>
<td class="LineOfCaller">66</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:46,051</td>
<td class="Message">向量服务RestTemplate初始化完成，连接超时: 30000ms，读取超时: 300000ms</td>
<td class="MethodOfCaller">initVectorRestTemplate</td>
<td class="FileOfCaller">VectorStoreService.java</td>
<td class="LineOfCaller">52</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:46,710</td>
<td class="Message">Using default implementation for ThreadExecutor</td>
<td class="MethodOfCaller">instantiate</td>
<td class="FileOfCaller">StdSchedulerFactory.java</td>
<td class="LineOfCaller">1220</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:46,713</td>
<td class="Message">Job execution threads will use class loader of thread: main</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">SimpleThreadPool.java</td>
<td class="LineOfCaller">268</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:46,725</td>
<td class="Message">Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl</td>
<td class="MethodOfCaller">&lt;init&gt;</td>
<td class="FileOfCaller">SchedulerSignalerImpl.java</td>
<td class="LineOfCaller">61</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:46,726</td>
<td class="Message">Quartz Scheduler v.2.3.2 created.</td>
<td class="MethodOfCaller">&lt;init&gt;</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">229</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:46,729</td>
<td class="Message">Using db table-based data access locking (synchronization).</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">JobStoreSupport.java</td>
<td class="LineOfCaller">672</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:46,731</td>
<td class="Message">JobStoreCMT initialized.</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">JobStoreCMT.java</td>
<td class="LineOfCaller">145</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:46,732</td>
<td class="Message">Scheduler meta-data: Quartz Scheduler (v2.3.2) &#39;MyScheduler&#39; with instanceId &#39;DESKTOP-EU3ACCV1755333046712&#39;
  Scheduler class: &#39;org.quartz.core.QuartzScheduler&#39; - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool &#39;org.quartz.simpl.SimpleThreadPool&#39; - with 10 threads.
  Using job-store &#39;org.springframework.scheduling.quartz.LocalDataSourceJobStore&#39; - which supports persistence. and is clustered.
</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">294</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:46,733</td>
<td class="Message">Quartz scheduler &#39;MyScheduler&#39; initialized from an externally provided properties instance.</td>
<td class="MethodOfCaller">instantiate</td>
<td class="FileOfCaller">StdSchedulerFactory.java</td>
<td class="LineOfCaller">1374</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:46,733</td>
<td class="Message">Quartz scheduler version: 2.3.2</td>
<td class="MethodOfCaller">instantiate</td>
<td class="FileOfCaller">StdSchedulerFactory.java</td>
<td class="LineOfCaller">1378</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:46,733</td>
<td class="Message">JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@1a034bbc</td>
<td class="MethodOfCaller">setJobFactory</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">2293</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:48,836</td>
<td class="Message">Exposing 2 endpoint(s) beneath base path &#39;/actuator&#39;</td>
<td class="MethodOfCaller">&lt;init&gt;</td>
<td class="FileOfCaller">EndpointLinksResolver.java</td>
<td class="LineOfCaller">58</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:48,997</td>
<td class="Message"> Init CodeGenerate Config [ Get Db Config From application.yml ] </td>
<td class="MethodOfCaller">initCodeGenerateDbConfig</td>
<td class="FileOfCaller">CodeGenerateDbConfig.java</td>
<td class="LineOfCaller">46</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:51,088</td>
<td class="Message">Starting ProtocolHandler [&quot;http-nio-8080&quot;]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:51,125</td>
<td class="Message">Tomcat started on port(s): 8080 (http) with context path &#39;/wordCompassApi&#39;</td>
<td class="MethodOfCaller">start</td>
<td class="FileOfCaller">TomcatWebServer.java</td>
<td class="LineOfCaller">220</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:54,003</td>
<td class="Message">Will start Quartz Scheduler [MyScheduler] in 1 seconds</td>
<td class="MethodOfCaller">startScheduler</td>
<td class="FileOfCaller">SchedulerFactoryBean.java</td>
<td class="LineOfCaller">734</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:54,022</td>
<td class="Message">开始同步KnoBaseDetail数据到Solr...</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">490</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:54,028</td>
<td class="Message">Started WordCompassApplication in 18.798 seconds (JVM running for 19.984)</td>
<td class="MethodOfCaller">logStarted</td>
<td class="FileOfCaller">StartupInfoLogger.java</td>
<td class="LineOfCaller">61</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:54,041</td>
<td class="Message"> Init Code Generate Template [ 检测如果是JAR启动环境，Copy模板到config目录 ] </td>
<td class="MethodOfCaller">onApplicationEvent</td>
<td class="FileOfCaller">CodeTemplateInitListener.java</td>
<td class="LineOfCaller">29</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:54,044</td>
<td class="Message">
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8080/wordCompassApi/
	External: 	http://*************:8080/wordCompassApi/
	Swagger文档: 	http://*************:8080/wordCompassApi/doc.html
----------------------------------------------------------</td>
<td class="MethodOfCaller">main</td>
<td class="FileOfCaller">WordCompassApplication.java</td>
<td class="LineOfCaller">39</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:54,551</td>
<td class="Message">Initializing Spring DispatcherServlet &#39;dispatcherServlet&#39;</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:54,552</td>
<td class="Message">Initializing Servlet &#39;dispatcherServlet&#39;</td>
<td class="MethodOfCaller">initServletBean</td>
<td class="FileOfCaller">FrameworkServlet.java</td>
<td class="LineOfCaller">525</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:54,556</td>
<td class="Message">Completed initialization in 4 ms</td>
<td class="MethodOfCaller">initServletBean</td>
<td class="FileOfCaller">FrameworkServlet.java</td>
<td class="LineOfCaller">547</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-08-16 16:30:54,663</td>
<td class="Message">Solr health check failed</td>
<td class="MethodOfCaller">logExceptionIfPresent</td>
<td class="FileOfCaller">AbstractHealthIndicator.java</td>
<td class="LineOfCaller">94</td>
</tr>
<tr><td class="Exception" colspan="6">org.apache.solr.client.solrj.SolrServerException: Server refused connection at: http://127.0.0.1:8099/solr-881
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:688)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:231)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.solr.SolrHealthIndicator$RootStatusCheck.getStatus(SolrHealthIndicator.java:116)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.solr.SolrHealthIndicator.initializeStatusCheck(SolrHealthIndicator.java:79)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.solr.SolrHealthIndicator.initializeStatusCheck(SolrHealthIndicator.java:67)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.solr.SolrHealthIndicator.doHealthCheck(SolrHealthIndicator.java:53)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:124)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:814)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:802)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1472)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1310)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1405)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.GeneratedMethodAccessor140.invoke(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.rmi/sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:360)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:200)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:197)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.security.AccessController.doPrivileged(AccessController.java:712)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.rmi/sun.rmi.transport.Transport.serviceCall(Transport.java:196)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.rmi/sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:587)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:828)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:705)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:704)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.Thread.run(Thread.java:833)
<br />Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 51 common frames omitted
<br />Caused by: java.net.ConnectException: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnect(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.Socket.connect(Socket.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 61 common frames omitted
</td></tr>
<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-08-16 16:30:54,722</td>
<td class="Message">同步KnoBaseDetail数据到Solr失败</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">507</td>
</tr>
<tr><td class="Exception" colspan="6">org.springframework.dao.DataAccessResourceFailureException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrExceptionTranslator.translateExceptionIfPossible(SolrExceptionTranslator.java:79)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:169)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.delete(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrOperations.delete(SolrOperations.java:228)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.repository.support.SimpleSolrRepository.deleteAll(SimpleSolrRepository.java:212)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService.deleteAll(KnoBaseDetailSolrService.java:313)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$FastClassBySpringCGLIB$$1d6fbb9b.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$EnhancerBySpringCGLIB$$deb63725.deleteAll(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailService.syncToSolr(KnoBaseDetailService.java:497)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.Thread.run(Thread.java:833)
<br />Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:940)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:903)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.lambda$delete$6(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:167)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 65 common frames omitted
<br />Caused by: java.net.ConnectException: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnect(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.Socket.connect(Socket.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 82 common frames omitted
</td></tr>
<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:55,006</td>
<td class="Message">Starting Quartz Scheduler now, after delay of 1 seconds</td>
<td class="MethodOfCaller">run</td>
<td class="FileOfCaller">SchedulerFactoryBean.java</td>
<td class="LineOfCaller">750</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:55,134</td>
<td class="Message">ClusterManager: detected 1 failed or restarted instances.</td>
<td class="MethodOfCaller">logWarnIfNonZero</td>
<td class="FileOfCaller">JobStoreSupport.java</td>
<td class="LineOfCaller">3644</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:55,135</td>
<td class="Message">ClusterManager: Scanning for instance &quot;DESKTOP-EU3ACCV1755319786941&quot;&#39;s failed in-progress jobs.</td>
<td class="MethodOfCaller">clusterRecover</td>
<td class="FileOfCaller">JobStoreSupport.java</td>
<td class="LineOfCaller">3503</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:30:55,141</td>
<td class="Message">Scheduler MyScheduler_$_DESKTOP-EU3ACCV1755333046712 started.</td>
<td class="MethodOfCaller">start</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">547</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:31:14,817</td>
<td class="Message">A cookie header was received [Hm_lvt_0febd9e3cacb3f627ddac64d52caac39=1755243411,1755246569,1755248930,1755319815;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-08-16 16:31:15,165</td>
<td class="Message">Unable to make field private final java.lang.Class java.lang.invoke.SerializedLambda.capturingClass accessible: module java.base does not &quot;opens java.lang.invoke&quot; to unnamed module @65e2dbf3</td>
<td class="MethodOfCaller">&lt;clinit&gt;</td>
<td class="FileOfCaller">ReflectLambdaMeta.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:31:40,480</td>
<td class="Message">======获取全部菜单数据=====耗时:40毫秒</td>
<td class="MethodOfCaller">list</td>
<td class="FileOfCaller">SysPermissionController.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:32:16,753</td>
<td class="Message">======获取全部菜单数据=====耗时:37毫秒</td>
<td class="MethodOfCaller">list</td>
<td class="FileOfCaller">SysPermissionController.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:32:37,432</td>
<td class="Message">======获取全部菜单数据=====耗时:39毫秒</td>
<td class="MethodOfCaller">list</td>
<td class="FileOfCaller">SysPermissionController.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:33:35,367</td>
<td class="Message">-------通过数据库读取用户拥有的角色Rules------username： admin,Roles size: 2</td>
<td class="MethodOfCaller">getUserRoleSet</td>
<td class="FileOfCaller">SysBaseApiImpl.java</td>
<td class="LineOfCaller">1097</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:33:35,417</td>
<td class="Message">-------通过数据库读取用户拥有的权限Perms------username： admin,Perms size: 161</td>
<td class="MethodOfCaller">getUserPermissionSet</td>
<td class="FileOfCaller">SysBaseApiImpl.java</td>
<td class="LineOfCaller">1128</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:33:35,417</td>
<td class="Message">===============Shiro权限认证成功==============</td>
<td class="MethodOfCaller">doGetAuthorizationInfo</td>
<td class="FileOfCaller">ShiroRealm.java</td>
<td class="LineOfCaller">80</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:33:35,738</td>
<td class="Message">======获取全部菜单数据=====耗时:50毫秒</td>
<td class="MethodOfCaller">list</td>
<td class="FileOfCaller">SysPermissionController.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:34:04,017</td>
<td class="Message">======获取全部菜单数据=====耗时:29毫秒</td>
<td class="MethodOfCaller">list</td>
<td class="FileOfCaller">SysPermissionController.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:36:10,781</td>
<td class="Message">======获取全部菜单数据=====耗时:38毫秒</td>
<td class="MethodOfCaller">list</td>
<td class="FileOfCaller">SysPermissionController.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:36:25,553</td>
<td class="Message">======获取全部菜单数据=====耗时:25毫秒</td>
<td class="MethodOfCaller">list</td>
<td class="FileOfCaller">SysPermissionController.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:36:41,004</td>
<td class="Message">======获取全部菜单数据=====耗时:34毫秒</td>
<td class="MethodOfCaller">list</td>
<td class="FileOfCaller">SysPermissionController.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-08-16 16:37:15,163</td>
<td class="Message">SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@45754c2e] Transaction not enabled</td>
<td class="MethodOfCaller">executeBatch</td>
<td class="FileOfCaller">SqlHelper.java</td>
<td class="LineOfCaller">179</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:37:15,186</td>
<td class="Message">======角色授权成功=====耗时:32毫秒</td>
<td class="MethodOfCaller">saveRolePermission</td>
<td class="FileOfCaller">SysPermissionController.java</td>
<td class="LineOfCaller">567</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:37:29,270</td>
<td class="Message">Sign Interceptor request URI = /wordCompassApi/sys/dict/getDictItems/valid_status</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">SignAuthInterceptor.java</td>
<td class="LineOfCaller">36</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:37:29,343</td>
<td class="Message">Param paramsJsonStr : {}</td>
<td class="MethodOfCaller">getParamsSign</td>
<td class="FileOfCaller">SignUtil.java</td>
<td class="LineOfCaller">50</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:37:29,346</td>
<td class="Message">Param Sign : E19D6243CB1945AB4F7202A1B00F77D5</td>
<td class="MethodOfCaller">verifySign</td>
<td class="FileOfCaller">SignUtil.java</td>
<td class="LineOfCaller">37</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:37:29,354</td>
<td class="Message"> dictCode : valid_status</td>
<td class="MethodOfCaller">getDictItems</td>
<td class="FileOfCaller">SysDictController.java</td>
<td class="LineOfCaller">168</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:37:31,951</td>
<td class="Message">Sign Interceptor request URI = /wordCompassApi/sys/dict/getDictItems/valid_status</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">SignAuthInterceptor.java</td>
<td class="LineOfCaller">36</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:37:31,953</td>
<td class="Message">Param paramsJsonStr : {}</td>
<td class="MethodOfCaller">getParamsSign</td>
<td class="FileOfCaller">SignUtil.java</td>
<td class="LineOfCaller">50</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:37:31,953</td>
<td class="Message">Param Sign : E19D6243CB1945AB4F7202A1B00F77D5</td>
<td class="MethodOfCaller">verifySign</td>
<td class="FileOfCaller">SignUtil.java</td>
<td class="LineOfCaller">37</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:37:31,954</td>
<td class="Message"> dictCode : valid_status</td>
<td class="MethodOfCaller">getDictItems</td>
<td class="FileOfCaller">SysDictController.java</td>
<td class="LineOfCaller">168</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:38:14,472</td>
<td class="Message">Sign Interceptor request URI = /wordCompassApi/sys/dict/getDictItems/valid_status</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">SignAuthInterceptor.java</td>
<td class="LineOfCaller">36</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:38:14,472</td>
<td class="Message">Param paramsJsonStr : {}</td>
<td class="MethodOfCaller">getParamsSign</td>
<td class="FileOfCaller">SignUtil.java</td>
<td class="LineOfCaller">50</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:38:14,472</td>
<td class="Message">Param Sign : E19D6243CB1945AB4F7202A1B00F77D5</td>
<td class="MethodOfCaller">verifySign</td>
<td class="FileOfCaller">SignUtil.java</td>
<td class="LineOfCaller">37</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:38:14,474</td>
<td class="Message"> dictCode : valid_status</td>
<td class="MethodOfCaller">getDictItems</td>
<td class="FileOfCaller">SysDictController.java</td>
<td class="LineOfCaller">168</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:38:17,748</td>
<td class="Message">查询磁盘信息:6个</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">117</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:38:17,785</td>
<td class="Message">{name=OS (C:), rest=184148058112, restPPT=57, max=429700067328}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:38:17,793</td>
<td class="Message">{name=本地磁盘 (D:), rest=138218975232, restPPT=48, max=268607250432}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:38:17,800</td>
<td class="Message">{name=本地磁盘 (E:), rest=57586896896, restPPT=61, max=151161372672}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:38:17,808</td>
<td class="Message">{name=本地磁盘 (F:), rest=69865852928, restPPT=53, max=150828929024}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:38:17,817</td>
<td class="Message">{name=本地磁盘 (G:), rest=137231179776, restPPT=72, max=500170412032}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:38:17,825</td>
<td class="Message">{name=本地磁盘 (H:), rest=260168253440, restPPT=47, max=500030472192}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:38:28,863</td>
<td class="Message">======获取全部菜单数据=====耗时:46毫秒</td>
<td class="MethodOfCaller">list</td>
<td class="FileOfCaller">SysPermissionController.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:38:32,592</td>
<td class="Message">======获取全部菜单数据=====耗时:25毫秒</td>
<td class="MethodOfCaller">list</td>
<td class="FileOfCaller">SysPermissionController.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:38:49,328</td>
<td class="Message">查询磁盘信息:6个</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">117</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:38:49,332</td>
<td class="Message">{name=OS (C:), rest=184148115456, restPPT=57, max=429700067328}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:38:49,336</td>
<td class="Message">{name=本地磁盘 (D:), rest=138218975232, restPPT=48, max=268607250432}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:38:49,341</td>
<td class="Message">{name=本地磁盘 (E:), rest=57586896896, restPPT=61, max=151161372672}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:38:49,345</td>
<td class="Message">{name=本地磁盘 (F:), rest=69865852928, restPPT=53, max=150828929024}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:38:49,349</td>
<td class="Message">{name=本地磁盘 (G:), rest=137231179776, restPPT=72, max=500170412032}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:38:49,353</td>
<td class="Message">{name=本地磁盘 (H:), rest=260168253440, restPPT=47, max=500030472192}</td>
<td class="MethodOfCaller">queryDiskInfo</td>
<td class="FileOfCaller">ActuatorRedisController.java</td>
<td class="LineOfCaller">130</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:38:51,453</td>
<td class="Message">Sign Interceptor request URI = /wordCompassApi/sys/dict/getDictItems/valid_status</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">SignAuthInterceptor.java</td>
<td class="LineOfCaller">36</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:38:51,453</td>
<td class="Message">Param paramsJsonStr : {}</td>
<td class="MethodOfCaller">getParamsSign</td>
<td class="FileOfCaller">SignUtil.java</td>
<td class="LineOfCaller">50</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:38:51,453</td>
<td class="Message">Param Sign : E19D6243CB1945AB4F7202A1B00F77D5</td>
<td class="MethodOfCaller">verifySign</td>
<td class="FileOfCaller">SignUtil.java</td>
<td class="LineOfCaller">37</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:38:51,455</td>
<td class="Message"> dictCode : valid_status</td>
<td class="MethodOfCaller">getDictItems</td>
<td class="FileOfCaller">SysDictController.java</td>
<td class="LineOfCaller">168</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:39:03,736</td>
<td class="Message">======获取全部菜单数据=====耗时:29毫秒</td>
<td class="MethodOfCaller">list</td>
<td class="FileOfCaller">SysPermissionController.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:39:06,985</td>
<td class="Message">======获取全部菜单数据=====耗时:31毫秒</td>
<td class="MethodOfCaller">list</td>
<td class="FileOfCaller">SysPermissionController.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:39:16,625</td>
<td class="Message">======获取全部菜单数据=====耗时:33毫秒</td>
<td class="MethodOfCaller">list</td>
<td class="FileOfCaller">SysPermissionController.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:39:56,274</td>
<td class="Message">======获取全部菜单数据=====耗时:27毫秒</td>
<td class="MethodOfCaller">list</td>
<td class="FileOfCaller">SysPermissionController.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:40:46,814</td>
<td class="Message">======获取全部菜单数据=====耗时:33毫秒</td>
<td class="MethodOfCaller">list</td>
<td class="FileOfCaller">SysPermissionController.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:41:34,718</td>
<td class="Message">======获取全部菜单数据=====耗时:28毫秒</td>
<td class="MethodOfCaller">list</td>
<td class="FileOfCaller">SysPermissionController.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:41:38,549</td>
<td class="Message">======获取全部菜单数据=====耗时:30毫秒</td>
<td class="MethodOfCaller">list</td>
<td class="FileOfCaller">SysPermissionController.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:41:42,737</td>
<td class="Message">======获取全部菜单数据=====耗时:34毫秒</td>
<td class="MethodOfCaller">list</td>
<td class="FileOfCaller">SysPermissionController.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:41:54,323</td>
<td class="Message">======获取全部菜单数据=====耗时:33毫秒</td>
<td class="MethodOfCaller">list</td>
<td class="FileOfCaller">SysPermissionController.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:41:58,411</td>
<td class="Message">======获取全部菜单数据=====耗时:29毫秒</td>
<td class="MethodOfCaller">list</td>
<td class="FileOfCaller">SysPermissionController.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:43:41,860</td>
<td class="Message">======获取全部菜单数据=====耗时:51毫秒</td>
<td class="MethodOfCaller">list</td>
<td class="FileOfCaller">SysPermissionController.java</td>
<td class="LineOfCaller">111</td>
</tr>

<tr class="warn odd">
<td class="Level">WARN</td>
<td class="Date">2025-08-16 16:43:58,136</td>
<td class="Message">SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@621c944c] Transaction not enabled</td>
<td class="MethodOfCaller">executeBatch</td>
<td class="FileOfCaller">SqlHelper.java</td>
<td class="LineOfCaller">179</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:43:58,152</td>
<td class="Message">======角色授权成功=====耗时:16毫秒</td>
<td class="MethodOfCaller">saveRolePermission</td>
<td class="FileOfCaller">SysPermissionController.java</td>
<td class="LineOfCaller">567</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:44:28,876</td>
<td class="Message">Sign Interceptor request URI = /wordCompassApi/sys/dict/getDictItems/valid_status</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">SignAuthInterceptor.java</td>
<td class="LineOfCaller">36</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:44:28,876</td>
<td class="Message">Param paramsJsonStr : {}</td>
<td class="MethodOfCaller">getParamsSign</td>
<td class="FileOfCaller">SignUtil.java</td>
<td class="LineOfCaller">50</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:44:28,876</td>
<td class="Message">Param Sign : E19D6243CB1945AB4F7202A1B00F77D5</td>
<td class="MethodOfCaller">verifySign</td>
<td class="FileOfCaller">SignUtil.java</td>
<td class="LineOfCaller">37</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:44:28,876</td>
<td class="Message"> dictCode : valid_status</td>
<td class="MethodOfCaller">getDictItems</td>
<td class="FileOfCaller">SysDictController.java</td>
<td class="LineOfCaller">168</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:44:33,318</td>
<td class="Message">Sign Interceptor request URI = /wordCompassApi/sys/dict/getDictItems/valid_status</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">SignAuthInterceptor.java</td>
<td class="LineOfCaller">36</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:44:33,319</td>
<td class="Message">Param paramsJsonStr : {}</td>
<td class="MethodOfCaller">getParamsSign</td>
<td class="FileOfCaller">SignUtil.java</td>
<td class="LineOfCaller">50</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:44:33,320</td>
<td class="Message">Param Sign : E19D6243CB1945AB4F7202A1B00F77D5</td>
<td class="MethodOfCaller">verifySign</td>
<td class="FileOfCaller">SignUtil.java</td>
<td class="LineOfCaller">37</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:44:33,320</td>
<td class="Message"> dictCode : valid_status</td>
<td class="MethodOfCaller">getDictItems</td>
<td class="FileOfCaller">SysDictController.java</td>
<td class="LineOfCaller">168</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:44:50,973</td>
<td class="Message">Sign Interceptor request URI = /wordCompassApi/sys/dict/getDictItems/valid_status</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">SignAuthInterceptor.java</td>
<td class="LineOfCaller">36</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:44:50,974</td>
<td class="Message">Param paramsJsonStr : {}</td>
<td class="MethodOfCaller">getParamsSign</td>
<td class="FileOfCaller">SignUtil.java</td>
<td class="LineOfCaller">50</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:44:50,974</td>
<td class="Message">Param Sign : E19D6243CB1945AB4F7202A1B00F77D5</td>
<td class="MethodOfCaller">verifySign</td>
<td class="FileOfCaller">SignUtil.java</td>
<td class="LineOfCaller">37</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:44:50,975</td>
<td class="Message"> dictCode : valid_status</td>
<td class="MethodOfCaller">getDictItems</td>
<td class="FileOfCaller">SysDictController.java</td>
<td class="LineOfCaller">168</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:44:57,784</td>
<td class="Message">Sign Interceptor request URI = /wordCompassApi/sys/dict/getDictItems/valid_status</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">SignAuthInterceptor.java</td>
<td class="LineOfCaller">36</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:44:57,785</td>
<td class="Message">Param paramsJsonStr : {}</td>
<td class="MethodOfCaller">getParamsSign</td>
<td class="FileOfCaller">SignUtil.java</td>
<td class="LineOfCaller">50</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:44:57,785</td>
<td class="Message">Param Sign : E19D6243CB1945AB4F7202A1B00F77D5</td>
<td class="MethodOfCaller">verifySign</td>
<td class="FileOfCaller">SignUtil.java</td>
<td class="LineOfCaller">37</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:44:57,786</td>
<td class="Message"> dictCode : valid_status</td>
<td class="MethodOfCaller">getDictItems</td>
<td class="FileOfCaller">SysDictController.java</td>
<td class="LineOfCaller">168</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:45:00,343</td>
<td class="Message">Sign Interceptor request URI = /wordCompassApi/sys/dict/getDictItems/valid_status</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">SignAuthInterceptor.java</td>
<td class="LineOfCaller">36</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:45:00,343</td>
<td class="Message">Param paramsJsonStr : {}</td>
<td class="MethodOfCaller">getParamsSign</td>
<td class="FileOfCaller">SignUtil.java</td>
<td class="LineOfCaller">50</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:45:00,346</td>
<td class="Message">Param Sign : E19D6243CB1945AB4F7202A1B00F77D5</td>
<td class="MethodOfCaller">verifySign</td>
<td class="FileOfCaller">SignUtil.java</td>
<td class="LineOfCaller">37</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:45:00,346</td>
<td class="Message"> dictCode : valid_status</td>
<td class="MethodOfCaller">getDictItems</td>
<td class="FileOfCaller">SysDictController.java</td>
<td class="LineOfCaller">168</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:45:18,826</td>
<td class="Message">Sign Interceptor request URI = /wordCompassApi/sys/dict/getDictItems/valid_status</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">SignAuthInterceptor.java</td>
<td class="LineOfCaller">36</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:45:18,827</td>
<td class="Message">Param paramsJsonStr : {}</td>
<td class="MethodOfCaller">getParamsSign</td>
<td class="FileOfCaller">SignUtil.java</td>
<td class="LineOfCaller">50</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:45:18,827</td>
<td class="Message">Param Sign : E19D6243CB1945AB4F7202A1B00F77D5</td>
<td class="MethodOfCaller">verifySign</td>
<td class="FileOfCaller">SignUtil.java</td>
<td class="LineOfCaller">37</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:45:18,828</td>
<td class="Message"> dictCode : valid_status</td>
<td class="MethodOfCaller">getDictItems</td>
<td class="FileOfCaller">SysDictController.java</td>
<td class="LineOfCaller">168</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:45:20,324</td>
<td class="Message">Sign Interceptor request URI = /wordCompassApi/sys/dict/getDictItems/valid_status</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">SignAuthInterceptor.java</td>
<td class="LineOfCaller">36</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:45:20,326</td>
<td class="Message">Param paramsJsonStr : {}</td>
<td class="MethodOfCaller">getParamsSign</td>
<td class="FileOfCaller">SignUtil.java</td>
<td class="LineOfCaller">50</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:45:20,326</td>
<td class="Message">Param Sign : E19D6243CB1945AB4F7202A1B00F77D5</td>
<td class="MethodOfCaller">verifySign</td>
<td class="FileOfCaller">SignUtil.java</td>
<td class="LineOfCaller">37</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:45:20,326</td>
<td class="Message"> dictCode : valid_status</td>
<td class="MethodOfCaller">getDictItems</td>
<td class="FileOfCaller">SysDictController.java</td>
<td class="LineOfCaller">168</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 16:45:54,028</td>
<td class="Message">开始同步KnoBaseDetail数据到Solr...</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">490</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-08-16 16:45:54,225</td>
<td class="Message">同步KnoBaseDetail数据到Solr失败</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">507</td>
</tr>
<tr><td class="Exception" colspan="6">org.springframework.dao.DataAccessResourceFailureException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrExceptionTranslator.translateExceptionIfPossible(SolrExceptionTranslator.java:79)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:169)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.delete(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrOperations.delete(SolrOperations.java:228)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.repository.support.SimpleSolrRepository.deleteAll(SimpleSolrRepository.java:212)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService.deleteAll(KnoBaseDetailSolrService.java:313)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$FastClassBySpringCGLIB$$1d6fbb9b.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$EnhancerBySpringCGLIB$$deb63725.deleteAll(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailService.syncToSolr(KnoBaseDetailService.java:497)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.Thread.run(Thread.java:833)
<br />Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:940)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:903)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.lambda$delete$6(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:167)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 65 common frames omitted
<br />Caused by: java.net.ConnectException: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnect(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.Socket.connect(Socket.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 82 common frames omitted
</td></tr>
<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:00:54,024</td>
<td class="Message">开始同步KnoBaseDetail数据到Solr...</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">490</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-08-16 17:00:54,250</td>
<td class="Message">同步KnoBaseDetail数据到Solr失败</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">507</td>
</tr>
<tr><td class="Exception" colspan="6">org.springframework.dao.DataAccessResourceFailureException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrExceptionTranslator.translateExceptionIfPossible(SolrExceptionTranslator.java:79)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:169)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.delete(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrOperations.delete(SolrOperations.java:228)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.repository.support.SimpleSolrRepository.deleteAll(SimpleSolrRepository.java:212)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService.deleteAll(KnoBaseDetailSolrService.java:313)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$FastClassBySpringCGLIB$$1d6fbb9b.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$EnhancerBySpringCGLIB$$deb63725.deleteAll(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailService.syncToSolr(KnoBaseDetailService.java:497)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.Thread.run(Thread.java:833)
<br />Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:940)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:903)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.lambda$delete$6(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:167)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 65 common frames omitted
<br />Caused by: java.net.ConnectException: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnect(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.Socket.connect(Socket.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 82 common frames omitted
</td></tr>
<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:03:49,090</td>
<td class="Message">Sign Interceptor request URI = /wordCompassApi/sys/dict/getDictItems/user_status</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">SignAuthInterceptor.java</td>
<td class="LineOfCaller">36</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:03:49,090</td>
<td class="Message">Sign Interceptor request URI = /wordCompassApi/sys/dict/getDictItems/valid_status</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">SignAuthInterceptor.java</td>
<td class="LineOfCaller">36</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:03:49,090</td>
<td class="Message">Sign Interceptor request URI = /wordCompassApi/sys/dict/getDictItems/sex</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">SignAuthInterceptor.java</td>
<td class="LineOfCaller">36</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:03:49,090</td>
<td class="Message">Param paramsJsonStr : {}</td>
<td class="MethodOfCaller">getParamsSign</td>
<td class="FileOfCaller">SignUtil.java</td>
<td class="LineOfCaller">50</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:03:49,090</td>
<td class="Message">Param Sign : E19D6243CB1945AB4F7202A1B00F77D5</td>
<td class="MethodOfCaller">verifySign</td>
<td class="FileOfCaller">SignUtil.java</td>
<td class="LineOfCaller">37</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:03:49,090</td>
<td class="Message">Param paramsJsonStr : {}</td>
<td class="MethodOfCaller">getParamsSign</td>
<td class="FileOfCaller">SignUtil.java</td>
<td class="LineOfCaller">50</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:03:49,090</td>
<td class="Message">Param Sign : E19D6243CB1945AB4F7202A1B00F77D5</td>
<td class="MethodOfCaller">verifySign</td>
<td class="FileOfCaller">SignUtil.java</td>
<td class="LineOfCaller">37</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:03:49,090</td>
<td class="Message">Param paramsJsonStr : {}</td>
<td class="MethodOfCaller">getParamsSign</td>
<td class="FileOfCaller">SignUtil.java</td>
<td class="LineOfCaller">50</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:03:49,093</td>
<td class="Message">Param Sign : E19D6243CB1945AB4F7202A1B00F77D5</td>
<td class="MethodOfCaller">verifySign</td>
<td class="FileOfCaller">SignUtil.java</td>
<td class="LineOfCaller">37</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:03:49,093</td>
<td class="Message"> dictCode : sex</td>
<td class="MethodOfCaller">getDictItems</td>
<td class="FileOfCaller">SysDictController.java</td>
<td class="LineOfCaller">168</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:03:49,093</td>
<td class="Message"> dictCode : user_status</td>
<td class="MethodOfCaller">getDictItems</td>
<td class="FileOfCaller">SysDictController.java</td>
<td class="LineOfCaller">168</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:03:49,093</td>
<td class="Message"> dictCode : valid_status</td>
<td class="MethodOfCaller">getDictItems</td>
<td class="FileOfCaller">SysDictController.java</td>
<td class="LineOfCaller">168</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:03:49,292</td>
<td class="Message">字典拼接的查询SQL：select name,id from sys_position</td>
<td class="MethodOfCaller">isPassByDict</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">127</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:03:49,313</td>
<td class="Message">获取select sql信息 ：{sys_position=SelectSqlInfo{fromTableName=&#39;sys_position&#39;, fromSubSelect=null, aliasName=&#39;null&#39;, selectFields=[name, id], realSelectFields=[name, id], selectAll=false}} </td>
<td class="MethodOfCaller">isPassBySql</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">76</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:03:49,340</td>
<td class="Message">表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, def_item=item_name,id, def_case=case_name,id, sys_dict=dict_code, oa_officialdoc_organcode=id,organ_name, demo=id,name, design_form=id,desform_name,desform_code, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, def_task=task_name,id, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name}</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">59</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:03:49,340</td>
<td class="Message">checkWhiteList tableName: sys_position</td>
<td class="MethodOfCaller">checkWhiteList</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">156</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:03:49,340</td>
<td class="Message">白名单校验：查询表&quot;sys_position&quot;，查询字段 [name, id] 通过校验</td>
<td class="MethodOfCaller">checkWhiteList</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">195</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:03:49,340</td>
<td class="Message">  获取sql信息 ：[QueryTable{name=&#39;sys_position&#39;, alias=&#39;&#39;, fields=[name, id], all=false}] </td>
<td class="MethodOfCaller">isPass</td>
<td class="FileOfCaller">AbstractQueryBlackListHandler.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:03:49,394</td>
<td class="Message">字典拼接的查询SQL：select name,id from sys_position</td>
<td class="MethodOfCaller">isPassByDict</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">127</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:03:49,396</td>
<td class="Message">获取select sql信息 ：{sys_position=SelectSqlInfo{fromTableName=&#39;sys_position&#39;, fromSubSelect=null, aliasName=&#39;null&#39;, selectFields=[name, id], realSelectFields=[name, id], selectAll=false}} </td>
<td class="MethodOfCaller">isPassBySql</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">76</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:03:49,404</td>
<td class="Message">表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, def_item=item_name,id, def_case=case_name,id, sys_dict=dict_code, oa_officialdoc_organcode=id,organ_name, demo=id,name, design_form=id,desform_name,desform_code, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, def_task=task_name,id, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name}</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">59</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:03:49,404</td>
<td class="Message">checkWhiteList tableName: sys_position</td>
<td class="MethodOfCaller">checkWhiteList</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">156</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:03:49,408</td>
<td class="Message">白名单校验：查询表&quot;sys_position&quot;，查询字段 [name, id] 通过校验</td>
<td class="MethodOfCaller">checkWhiteList</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">195</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:03:49,408</td>
<td class="Message">  获取sql信息 ：[QueryTable{name=&#39;sys_position&#39;, alias=&#39;&#39;, fields=[name, id], all=false}] </td>
<td class="MethodOfCaller">isPass</td>
<td class="FileOfCaller">AbstractQueryBlackListHandler.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:03:49,440</td>
<td class="Message">字典拼接的查询SQL：select name,id from sys_position</td>
<td class="MethodOfCaller">isPassByDict</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">127</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:03:49,446</td>
<td class="Message">获取select sql信息 ：{sys_position=SelectSqlInfo{fromTableName=&#39;sys_position&#39;, fromSubSelect=null, aliasName=&#39;null&#39;, selectFields=[name, id], realSelectFields=[name, id], selectAll=false}} </td>
<td class="MethodOfCaller">isPassBySql</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">76</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:03:49,453</td>
<td class="Message">表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, def_item=item_name,id, def_case=case_name,id, sys_dict=dict_code, oa_officialdoc_organcode=id,organ_name, demo=id,name, design_form=id,desform_name,desform_code, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, def_task=task_name,id, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name}</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">59</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:03:49,453</td>
<td class="Message">checkWhiteList tableName: sys_position</td>
<td class="MethodOfCaller">checkWhiteList</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">156</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:03:49,453</td>
<td class="Message">白名单校验：查询表&quot;sys_position&quot;，查询字段 [name, id] 通过校验</td>
<td class="MethodOfCaller">checkWhiteList</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">195</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:03:49,453</td>
<td class="Message">  获取sql信息 ：[QueryTable{name=&#39;sys_position&#39;, alias=&#39;&#39;, fields=[name, id], all=false}] </td>
<td class="MethodOfCaller">isPass</td>
<td class="FileOfCaller">AbstractQueryBlackListHandler.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:03:49,458</td>
<td class="Message">字典拼接的查询SQL：select name,id from sys_position</td>
<td class="MethodOfCaller">isPassByDict</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">127</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:03:49,465</td>
<td class="Message">获取select sql信息 ：{sys_position=SelectSqlInfo{fromTableName=&#39;sys_position&#39;, fromSubSelect=null, aliasName=&#39;null&#39;, selectFields=[name, id], realSelectFields=[name, id], selectAll=false}} </td>
<td class="MethodOfCaller">isPassBySql</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">76</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:03:49,467</td>
<td class="Message">表字典白名单初始化完成：{sys_permission=id,name, sys_position=name,id, onl_drag_comp=id,comp_name, def_item=item_name,id, def_case=case_name,id, sys_dict=dict_code, oa_officialdoc_organcode=id,organ_name, demo=id,name, design_form=id,desform_name,desform_code, sys_depart=id,org_code,depart_name, sys_user=phone,work_no,id,email,realname,username, onl_cgreport_head=code, def_task=task_name,id, sys_category=id,name, onl_cgform_head=table_txt,table_name, oa_wps_file=id,name}</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">59</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:03:49,470</td>
<td class="Message">checkWhiteList tableName: sys_position</td>
<td class="MethodOfCaller">checkWhiteList</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">156</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:03:49,470</td>
<td class="Message">白名单校验：查询表&quot;sys_position&quot;，查询字段 [name, id] 通过校验</td>
<td class="MethodOfCaller">checkWhiteList</td>
<td class="FileOfCaller">DictTableWhiteListHandlerImpl.java</td>
<td class="LineOfCaller">195</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:03:49,470</td>
<td class="Message">  获取sql信息 ：[QueryTable{name=&#39;sys_position&#39;, alias=&#39;&#39;, fields=[name, id], all=false}] </td>
<td class="MethodOfCaller">isPass</td>
<td class="FileOfCaller">AbstractQueryBlackListHandler.java</td>
<td class="LineOfCaller">64</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:07:00,453</td>
<td class="Message">Sign Interceptor request URI = /wordCompassApi/sys/dict/getDictItems/sex</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">SignAuthInterceptor.java</td>
<td class="LineOfCaller">36</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:07:00,453</td>
<td class="Message">Sign Interceptor request URI = /wordCompassApi/sys/dict/getDictItems/user_status</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">SignAuthInterceptor.java</td>
<td class="LineOfCaller">36</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:07:00,453</td>
<td class="Message">Param paramsJsonStr : {}</td>
<td class="MethodOfCaller">getParamsSign</td>
<td class="FileOfCaller">SignUtil.java</td>
<td class="LineOfCaller">50</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:07:00,453</td>
<td class="Message">Param paramsJsonStr : {}</td>
<td class="MethodOfCaller">getParamsSign</td>
<td class="FileOfCaller">SignUtil.java</td>
<td class="LineOfCaller">50</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:07:00,458</td>
<td class="Message">Param Sign : E19D6243CB1945AB4F7202A1B00F77D5</td>
<td class="MethodOfCaller">verifySign</td>
<td class="FileOfCaller">SignUtil.java</td>
<td class="LineOfCaller">37</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:07:00,458</td>
<td class="Message">Param Sign : E19D6243CB1945AB4F7202A1B00F77D5</td>
<td class="MethodOfCaller">verifySign</td>
<td class="FileOfCaller">SignUtil.java</td>
<td class="LineOfCaller">37</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:07:00,458</td>
<td class="Message"> dictCode : sex</td>
<td class="MethodOfCaller">getDictItems</td>
<td class="FileOfCaller">SysDictController.java</td>
<td class="LineOfCaller">168</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:07:00,459</td>
<td class="Message"> dictCode : user_status</td>
<td class="MethodOfCaller">getDictItems</td>
<td class="FileOfCaller">SysDictController.java</td>
<td class="LineOfCaller">168</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:07:44,790</td>
<td class="Message">Scheduler MyScheduler_$_DESKTOP-EU3ACCV1755333046712 paused.</td>
<td class="MethodOfCaller">standby</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">585</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:07:44,860</td>
<td class="Message">Shutting down Quartz Scheduler</td>
<td class="MethodOfCaller">destroy</td>
<td class="FileOfCaller">SchedulerFactoryBean.java</td>
<td class="LineOfCaller">847</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:07:44,860</td>
<td class="Message">Scheduler MyScheduler_$_DESKTOP-EU3ACCV1755333046712 shutting down.</td>
<td class="MethodOfCaller">shutdown</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">666</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:07:44,860</td>
<td class="Message">Scheduler MyScheduler_$_DESKTOP-EU3ACCV1755333046712 paused.</td>
<td class="MethodOfCaller">standby</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">585</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:07:44,863</td>
<td class="Message">Scheduler MyScheduler_$_DESKTOP-EU3ACCV1755333046712 shutdown complete.</td>
<td class="MethodOfCaller">shutdown</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">740</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:07:44,876</td>
<td class="Message">dynamic-datasource start closing ....</td>
<td class="MethodOfCaller">destroy</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">211</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:07:44,879</td>
<td class="Message">{dataSource-1} closing ...</td>
<td class="MethodOfCaller">close</td>
<td class="FileOfCaller">DruidDataSource.java</td>
<td class="LineOfCaller">2175</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:07:44,884</td>
<td class="Message">{dataSource-1} closed</td>
<td class="MethodOfCaller">close</td>
<td class="FileOfCaller">DruidDataSource.java</td>
<td class="LineOfCaller">2248</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:07:44,884</td>
<td class="Message">dynamic-datasource all closed success,bye</td>
<td class="MethodOfCaller">destroy</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">215</td>
</tr>
</table>
</body></html><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html>
  <head>
    <title>Logback Log Messages</title>
<style  type="text/css">
table { margin-left: 2em; margin-right: 2em; border-left: 2px solid #AAA; }
TR.even { background: #FFFFFF; }
TR.odd { background: #EAEAEA; }
TR.warn TD.Level, TR.error TD.Level, TR.fatal TD.Level {font-weight: bold; color: #FF4040 }
TD { padding-right: 1ex; padding-left: 1ex; border-right: 2px solid #AAA; }
TD.Time, TD.Date { text-align: right; font-family: courier, monospace; font-size: smaller; }
TD.Thread { text-align: left; }
TD.Level { text-align: right; }
TD.Logger { text-align: left; }
TR.header { background: #596ED5; color: #FFF; font-weight: bold; font-size: larger; }
TD.Exception { background: #A2AEE8; font-family: courier, monospace;}
</style>

  </head>
<body>
<hr/>
<p>Log session start time Sat Aug 16 17:07:58 CST 2025</p><p></p>

<table cellspacing="0">
<tr class="header">
<td class="Level">Level</td>
<td class="Date">Date</td>
<td class="Message">Message</td>
<td class="MethodOfCaller">MethodOfCaller</td>
<td class="FileOfCaller">FileOfCaller</td>
<td class="LineOfCaller">LineOfCaller</td>
</tr>


<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:07:58,548</td>
<td class="Message">HV000001: Hibernate Validator 6.2.5.Final</td>
<td class="MethodOfCaller">&lt;clinit&gt;</td>
<td class="FileOfCaller">Version.java</td>
<td class="LineOfCaller">21</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:07:58,607</td>
<td class="Message">Starting WordCompassApplication using Java 17.0.6 on DESKTOP-EU3ACCV with PID 35688 (E:\my-work-2025\work-wordcompass\wordCompass\service\wordCompass-server\build\classes\java\main started by Administrator in E:\my-work-2025\work-wordcompass\wordCompass)</td>
<td class="MethodOfCaller">logStarting</td>
<td class="FileOfCaller">StartupInfoLogger.java</td>
<td class="LineOfCaller">55</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:07:58,608</td>
<td class="Message">The following 1 profile is active: &quot;dev&quot;</td>
<td class="MethodOfCaller">logStartupProfileInfo</td>
<td class="FileOfCaller">SpringApplication.java</td>
<td class="LineOfCaller">637</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:00,346</td>
<td class="Message">Multiple Spring Data modules found, entering strict repository configuration mode</td>
<td class="MethodOfCaller">multipleStoresDetected</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">262</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:00,348</td>
<td class="Message">Bootstrapping Spring Data Solr repositories in DEFAULT mode.</td>
<td class="MethodOfCaller">registerRepositoriesIn</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">132</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:00,536</td>
<td class="Message">Finished Spring Data repository scanning in 167 ms. Found 1 Solr repository interfaces.</td>
<td class="MethodOfCaller">registerRepositoriesIn</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">201</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:01,024</td>
<td class="Message">Multiple Spring Data modules found, entering strict repository configuration mode</td>
<td class="MethodOfCaller">multipleStoresDetected</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">262</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:01,025</td>
<td class="Message">Bootstrapping Spring Data Redis repositories in DEFAULT mode.</td>
<td class="MethodOfCaller">registerRepositoriesIn</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">132</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:01,173</td>
<td class="Message">Spring Data Redis - Could not safely identify store assignment for repository candidate interface org.jeecg.modules.km.kno.repository.KnoBaseDetailVoRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository</td>
<td class="MethodOfCaller">isStrictRepositoryCandidate</td>
<td class="FileOfCaller">RepositoryConfigurationExtensionSupport.java</td>
<td class="LineOfCaller">349</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:01,174</td>
<td class="Message">Finished Spring Data repository scanning in 139 ms. Found 0 Redis repository interfaces.</td>
<td class="MethodOfCaller">registerRepositoriesIn</td>
<td class="FileOfCaller">RepositoryConfigurationDelegate.java</td>
<td class="LineOfCaller">201</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:01,627</td>
<td class="Message">Bean &#39;spring.redis-org.springframework.boot.autoconfigure.data.redis.RedisProperties&#39; of type [org.springframework.boot.autoconfigure.data.redis.RedisProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:01,630</td>
<td class="Message">Bean &#39;org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration&#39; of type [org.springframework.boot.autoconfigure.data.redis.LettuceConnectionConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:01,653</td>
<td class="Message">Bean &#39;org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration&#39; of type [org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:01,654</td>
<td class="Message">Bean &#39;org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusMetricsExportAutoConfiguration&#39; of type [org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusMetricsExportAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:01,657</td>
<td class="Message">Bean &#39;management.metrics.export.prometheus-org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusProperties&#39; of type [org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:01,668</td>
<td class="Message">Bean &#39;prometheusConfig&#39; of type [org.springframework.boot.actuate.autoconfigure.metrics.export.prometheus.PrometheusPropertiesConfigAdapter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:01,672</td>
<td class="Message">Bean &#39;collectorRegistry&#39; of type [io.prometheus.client.CollectorRegistry] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:01,674</td>
<td class="Message">Bean &#39;org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration&#39; of type [org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:01,674</td>
<td class="Message">Bean &#39;micrometerClock&#39; of type [io.micrometer.core.instrument.Clock$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:01,694</td>
<td class="Message">Bean &#39;prometheusMeterRegistry&#39; of type [io.micrometer.prometheus.PrometheusMeterRegistry] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:01,699</td>
<td class="Message">Bean &#39;micrometerOptions&#39; of type [io.lettuce.core.metrics.MicrometerOptions] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:01,700</td>
<td class="Message">Bean &#39;lettuceMetrics&#39; of type [org.springframework.boot.actuate.autoconfigure.metrics.redis.LettuceMetricsAutoConfiguration$$Lambda$645/0x00000008012239e8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:01,797</td>
<td class="Message">Bean &#39;lettuceClientResources&#39; of type [io.lettuce.core.resource.DefaultClientResources] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:01,914</td>
<td class="Message">Bean &#39;redisConnectionFactory&#39; of type [org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:01,922</td>
<td class="Message">Bean &#39;jeecgBaseConfig&#39; of type [org.jeecg.config.JeecgBaseConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:01,925</td>
<td class="Message">Bean &#39;shiroConfig&#39; of type [org.jeecg.config.shiro.ShiroConfig$$EnhancerBySpringCGLIB$$9623f6b8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:02,202</td>
<td class="Message">Bean &#39;spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties&#39; of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:02,211</td>
<td class="Message">Bean &#39;com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration&#39; of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration$$EnhancerBySpringCGLIB$$b8596613] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:02,226</td>
<td class="Message">Bean &#39;dsProcessor&#39; of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:02,284</td>
<td class="Message">Bean &#39;shiroRealm&#39; of type [org.jeecg.config.shiro.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:02,365</td>
<td class="Message">===============(1)创建缓存管理器RedisCacheManager</td>
<td class="MethodOfCaller">redisCacheManager</td>
<td class="FileOfCaller">ShiroConfig.java</td>
<td class="LineOfCaller">247</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:02,367</td>
<td class="Message">===============(2)创建RedisManager,连接Redis..</td>
<td class="MethodOfCaller">redisManager</td>
<td class="FileOfCaller">ShiroConfig.java</td>
<td class="LineOfCaller">265</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:02,372</td>
<td class="Message">Bean &#39;redisManager&#39; of type [org.crazycake.shiro.RedisManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:02,378</td>
<td class="Message">Bean &#39;securityManager&#39; of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:02,403</td>
<td class="Message">Bean &#39;authorizationAttributeSourceAdvisor&#39; of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:02,428</td>
<td class="Message">Bean &#39;org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration&#39; of type [org.apache.shiro.spring.boot.autoconfigure.ShiroBeanAutoConfiguration$$EnhancerBySpringCGLIB$$4a008c6f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:02,433</td>
<td class="Message">Bean &#39;eventBus&#39; of type [org.apache.shiro.event.support.DefaultEventBus] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)</td>
<td class="MethodOfCaller">postProcessAfterInitialization</td>
<td class="FileOfCaller">PostProcessorRegistrationDelegate.java</td>
<td class="LineOfCaller">376</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:02,833</td>
<td class="Message">Tomcat initialized with port(s): 8080 (http)</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">TomcatWebServer.java</td>
<td class="LineOfCaller">108</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:02,844</td>
<td class="Message">Initializing ProtocolHandler [&quot;http-nio-8080&quot;]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:02,845</td>
<td class="Message">Starting service [Tomcat]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:02,845</td>
<td class="Message">Starting Servlet engine: [Apache Tomcat/9.0.73]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:03,078</td>
<td class="Message">Initializing Spring embedded WebApplicationContext</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:03,078</td>
<td class="Message">Root WebApplicationContext: initialization completed in 4414 ms</td>
<td class="MethodOfCaller">prepareWebApplicationContext</td>
<td class="FileOfCaller">ServletWebServerApplicationContext.java</td>
<td class="LineOfCaller">292</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:04,203</td>
<td class="Message">{dataSource-1,master} inited</td>
<td class="MethodOfCaller">init</td>
<td class="FileOfCaller">DruidDataSource.java</td>
<td class="LineOfCaller">1010</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:04,206</td>
<td class="Message">dynamic-datasource - add a datasource named [master] success</td>
<td class="MethodOfCaller">addDataSource</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">154</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:04,206</td>
<td class="Message">dynamic-datasource initial loaded [1] datasource,primary datasource named [master]</td>
<td class="MethodOfCaller">afterPropertiesSet</td>
<td class="FileOfCaller">DynamicRoutingDataSource.java</td>
<td class="LineOfCaller">237</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:05,936</td>
<td class="Message"> --- redis config init --- </td>
<td class="MethodOfCaller">redisTemplate</td>
<td class="FileOfCaller">RedisConfig.java</td>
<td class="LineOfCaller">56</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:08,644</td>
<td class="Message">知识服务RestTemplate初始化完成，连接超时: 30000ms，读取超时: 600000ms，最大重试次数: 3，重试间隔: 1000ms</td>
<td class="MethodOfCaller">initKnowledgeRestTemplate</td>
<td class="FileOfCaller">KnowledgeScriptService.java</td>
<td class="LineOfCaller">66</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:08,727</td>
<td class="Message">向量服务RestTemplate初始化完成，连接超时: 30000ms，读取超时: 300000ms</td>
<td class="MethodOfCaller">initVectorRestTemplate</td>
<td class="FileOfCaller">VectorStoreService.java</td>
<td class="LineOfCaller">52</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:09,312</td>
<td class="Message">Using default implementation for ThreadExecutor</td>
<td class="MethodOfCaller">instantiate</td>
<td class="FileOfCaller">StdSchedulerFactory.java</td>
<td class="LineOfCaller">1220</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:09,316</td>
<td class="Message">Job execution threads will use class loader of thread: main</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">SimpleThreadPool.java</td>
<td class="LineOfCaller">268</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:09,326</td>
<td class="Message">Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl</td>
<td class="MethodOfCaller">&lt;init&gt;</td>
<td class="FileOfCaller">SchedulerSignalerImpl.java</td>
<td class="LineOfCaller">61</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:09,326</td>
<td class="Message">Quartz Scheduler v.2.3.2 created.</td>
<td class="MethodOfCaller">&lt;init&gt;</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">229</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:09,329</td>
<td class="Message">Using db table-based data access locking (synchronization).</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">JobStoreSupport.java</td>
<td class="LineOfCaller">672</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:09,330</td>
<td class="Message">JobStoreCMT initialized.</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">JobStoreCMT.java</td>
<td class="LineOfCaller">145</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:09,331</td>
<td class="Message">Scheduler meta-data: Quartz Scheduler (v2.3.2) &#39;MyScheduler&#39; with instanceId &#39;DESKTOP-EU3ACCV1755335289315&#39;
  Scheduler class: &#39;org.quartz.core.QuartzScheduler&#39; - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool &#39;org.quartz.simpl.SimpleThreadPool&#39; - with 10 threads.
  Using job-store &#39;org.springframework.scheduling.quartz.LocalDataSourceJobStore&#39; - which supports persistence. and is clustered.
</td>
<td class="MethodOfCaller">initialize</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">294</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:09,332</td>
<td class="Message">Quartz scheduler &#39;MyScheduler&#39; initialized from an externally provided properties instance.</td>
<td class="MethodOfCaller">instantiate</td>
<td class="FileOfCaller">StdSchedulerFactory.java</td>
<td class="LineOfCaller">1374</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:09,332</td>
<td class="Message">Quartz scheduler version: 2.3.2</td>
<td class="MethodOfCaller">instantiate</td>
<td class="FileOfCaller">StdSchedulerFactory.java</td>
<td class="LineOfCaller">1378</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:09,332</td>
<td class="Message">JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@419ac0f5</td>
<td class="MethodOfCaller">setJobFactory</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">2293</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:11,436</td>
<td class="Message">Exposing 2 endpoint(s) beneath base path &#39;/actuator&#39;</td>
<td class="MethodOfCaller">&lt;init&gt;</td>
<td class="FileOfCaller">EndpointLinksResolver.java</td>
<td class="LineOfCaller">58</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:11,578</td>
<td class="Message"> Init CodeGenerate Config [ Get Db Config From application.yml ] </td>
<td class="MethodOfCaller">initCodeGenerateDbConfig</td>
<td class="FileOfCaller">CodeGenerateDbConfig.java</td>
<td class="LineOfCaller">46</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:13,053</td>
<td class="Message">Starting ProtocolHandler [&quot;http-nio-8080&quot;]</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:13,082</td>
<td class="Message">Tomcat started on port(s): 8080 (http) with context path &#39;/wordCompassApi&#39;</td>
<td class="MethodOfCaller">start</td>
<td class="FileOfCaller">TomcatWebServer.java</td>
<td class="LineOfCaller">220</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:14,861</td>
<td class="Message">A cookie header was received [Hm_lvt_0febd9e3cacb3f627ddac64d52caac39=1755243411,1755246569,1755248930,1755319815;] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:14,884</td>
<td class="Message">Initializing Spring DispatcherServlet &#39;dispatcherServlet&#39;</td>
<td class="MethodOfCaller">log</td>
<td class="FileOfCaller">DirectJDKLog.java</td>
<td class="LineOfCaller">173</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:14,884</td>
<td class="Message">Initializing Servlet &#39;dispatcherServlet&#39;</td>
<td class="MethodOfCaller">initServletBean</td>
<td class="FileOfCaller">FrameworkServlet.java</td>
<td class="LineOfCaller">525</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:14,888</td>
<td class="Message">Completed initialization in 4 ms</td>
<td class="MethodOfCaller">initServletBean</td>
<td class="FileOfCaller">FrameworkServlet.java</td>
<td class="LineOfCaller">547</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:15,385</td>
<td class="Message">Will start Quartz Scheduler [MyScheduler] in 1 seconds</td>
<td class="MethodOfCaller">startScheduler</td>
<td class="FileOfCaller">SchedulerFactoryBean.java</td>
<td class="LineOfCaller">734</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:15,407</td>
<td class="Message">开始同步KnoBaseDetail数据到Solr...</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">490</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:15,413</td>
<td class="Message">Started WordCompassApplication in 17.448 seconds (JVM running for 18.706)</td>
<td class="MethodOfCaller">logStarted</td>
<td class="FileOfCaller">StartupInfoLogger.java</td>
<td class="LineOfCaller">61</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:15,438</td>
<td class="Message"> Init Code Generate Template [ 检测如果是JAR启动环境，Copy模板到config目录 ] </td>
<td class="MethodOfCaller">onApplicationEvent</td>
<td class="FileOfCaller">CodeTemplateInitListener.java</td>
<td class="LineOfCaller">29</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:15,439</td>
<td class="Message">
----------------------------------------------------------
	Application Jeecg-Boot is running! Access URLs:
	Local: 		http://localhost:8080/wordCompassApi/
	External: 	http://*************:8080/wordCompassApi/
	Swagger文档: 	http://*************:8080/wordCompassApi/doc.html
----------------------------------------------------------</td>
<td class="MethodOfCaller">main</td>
<td class="FileOfCaller">WordCompassApplication.java</td>
<td class="LineOfCaller">39</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-08-16 17:08:15,972</td>
<td class="Message">Solr health check failed</td>
<td class="MethodOfCaller">logExceptionIfPresent</td>
<td class="FileOfCaller">AbstractHealthIndicator.java</td>
<td class="LineOfCaller">94</td>
</tr>
<tr><td class="Exception" colspan="6">org.apache.solr.client.solrj.SolrServerException: Server refused connection at: http://127.0.0.1:8099/solr-881
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:688)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:231)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.solr.SolrHealthIndicator$RootStatusCheck.getStatus(SolrHealthIndicator.java:116)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.solr.SolrHealthIndicator.initializeStatusCheck(SolrHealthIndicator.java:79)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.solr.SolrHealthIndicator.initializeStatusCheck(SolrHealthIndicator.java:67)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.solr.SolrHealthIndicator.doHealthCheck(SolrHealthIndicator.java:53)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:124)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:814)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:802)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1472)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1310)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1405)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.management.rmi/javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.rmi/sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:360)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:200)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:197)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.security.AccessController.doPrivileged(AccessController.java:712)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.rmi/sun.rmi.transport.Transport.serviceCall(Transport.java:196)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.rmi/sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:587)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:828)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:705)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:704)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.Thread.run(Thread.java:833)
<br />Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 52 common frames omitted
<br />Caused by: java.net.ConnectException: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnect(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.Socket.connect(Socket.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 62 common frames omitted
</td></tr>
<tr class="error odd">
<td class="Level">ERROR</td>
<td class="Date">2025-08-16 17:08:16,106</td>
<td class="Message">同步KnoBaseDetail数据到Solr失败</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">507</td>
</tr>
<tr><td class="Exception" colspan="6">org.springframework.dao.DataAccessResourceFailureException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrExceptionTranslator.translateExceptionIfPossible(SolrExceptionTranslator.java:79)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:169)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.delete(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrOperations.delete(SolrOperations.java:228)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.repository.support.SimpleSolrRepository.deleteAll(SimpleSolrRepository.java:212)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService.deleteAll(KnoBaseDetailSolrService.java:313)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$FastClassBySpringCGLIB$$1d6fbb9b.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$EnhancerBySpringCGLIB$$feae8788.deleteAll(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailService.syncToSolr(KnoBaseDetailService.java:497)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.Thread.run(Thread.java:833)
<br />Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:940)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:903)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.lambda$delete$6(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:167)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 65 common frames omitted
<br />Caused by: java.net.ConnectException: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnect(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.Socket.connect(Socket.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 82 common frames omitted
</td></tr>
<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:16,388</td>
<td class="Message">Starting Quartz Scheduler now, after delay of 1 seconds</td>
<td class="MethodOfCaller">run</td>
<td class="FileOfCaller">SchedulerFactoryBean.java</td>
<td class="LineOfCaller">750</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:16,510</td>
<td class="Message">ClusterManager: detected 1 failed or restarted instances.</td>
<td class="MethodOfCaller">logWarnIfNonZero</td>
<td class="FileOfCaller">JobStoreSupport.java</td>
<td class="LineOfCaller">3644</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:16,510</td>
<td class="Message">ClusterManager: Scanning for instance &quot;DESKTOP-EU3ACCV1755333046712&quot;&#39;s failed in-progress jobs.</td>
<td class="MethodOfCaller">clusterRecover</td>
<td class="FileOfCaller">JobStoreSupport.java</td>
<td class="LineOfCaller">3503</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:08:16,518</td>
<td class="Message">Scheduler MyScheduler_$_DESKTOP-EU3ACCV1755335289315 started.</td>
<td class="MethodOfCaller">start</td>
<td class="FileOfCaller">QuartzScheduler.java</td>
<td class="LineOfCaller">547</td>
</tr>

<tr class="warn even">
<td class="Level">WARN</td>
<td class="Date">2025-08-16 17:08:30,593</td>
<td class="Message">Unable to make field private final java.lang.Class java.lang.invoke.SerializedLambda.capturingClass accessible: module java.base does not &quot;opens java.lang.invoke&quot; to unnamed module @65e2dbf3</td>
<td class="MethodOfCaller">&lt;clinit&gt;</td>
<td class="FileOfCaller">ReflectLambdaMeta.java</td>
<td class="LineOfCaller">41</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:09:54,197</td>
<td class="Message">Sign Interceptor request URI = /wordCompassApi/sys/dict/getDictItems/valid_status</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">SignAuthInterceptor.java</td>
<td class="LineOfCaller">36</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:09:54,267</td>
<td class="Message">Param paramsJsonStr : {}</td>
<td class="MethodOfCaller">getParamsSign</td>
<td class="FileOfCaller">SignUtil.java</td>
<td class="LineOfCaller">50</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:09:54,270</td>
<td class="Message">Param Sign : E19D6243CB1945AB4F7202A1B00F77D5</td>
<td class="MethodOfCaller">verifySign</td>
<td class="FileOfCaller">SignUtil.java</td>
<td class="LineOfCaller">37</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:09:54,287</td>
<td class="Message"> dictCode : valid_status</td>
<td class="MethodOfCaller">getDictItems</td>
<td class="FileOfCaller">SysDictController.java</td>
<td class="LineOfCaller">168</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:13:58,032</td>
<td class="Message">Sign Interceptor request URI = /wordCompassApi/sys/dict/getDictItems/valid_status</td>
<td class="MethodOfCaller">preHandle</td>
<td class="FileOfCaller">SignAuthInterceptor.java</td>
<td class="LineOfCaller">36</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:13:58,032</td>
<td class="Message">Param paramsJsonStr : {}</td>
<td class="MethodOfCaller">getParamsSign</td>
<td class="FileOfCaller">SignUtil.java</td>
<td class="LineOfCaller">50</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:13:58,034</td>
<td class="Message">Param Sign : E19D6243CB1945AB4F7202A1B00F77D5</td>
<td class="MethodOfCaller">verifySign</td>
<td class="FileOfCaller">SignUtil.java</td>
<td class="LineOfCaller">37</td>
</tr>

<tr class="info even">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:13:58,034</td>
<td class="Message"> dictCode : valid_status</td>
<td class="MethodOfCaller">getDictItems</td>
<td class="FileOfCaller">SysDictController.java</td>
<td class="LineOfCaller">168</td>
</tr>

<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:23:15,407</td>
<td class="Message">开始同步KnoBaseDetail数据到Solr...</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">490</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-08-16 17:23:15,588</td>
<td class="Message">同步KnoBaseDetail数据到Solr失败</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">507</td>
</tr>
<tr><td class="Exception" colspan="6">org.springframework.dao.DataAccessResourceFailureException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrExceptionTranslator.translateExceptionIfPossible(SolrExceptionTranslator.java:79)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:169)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.delete(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrOperations.delete(SolrOperations.java:228)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.repository.support.SimpleSolrRepository.deleteAll(SimpleSolrRepository.java:212)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService.deleteAll(KnoBaseDetailSolrService.java:313)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$FastClassBySpringCGLIB$$1d6fbb9b.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$EnhancerBySpringCGLIB$$feae8788.deleteAll(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailService.syncToSolr(KnoBaseDetailService.java:497)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.Thread.run(Thread.java:833)
<br />Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:940)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:903)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.lambda$delete$6(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:167)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 65 common frames omitted
<br />Caused by: java.net.ConnectException: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnect(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.Socket.connect(Socket.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 82 common frames omitted
</td></tr>
<tr class="info odd">
<td class="Level">INFO</td>
<td class="Date">2025-08-16 17:38:15,408</td>
<td class="Message">开始同步KnoBaseDetail数据到Solr...</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">490</td>
</tr>

<tr class="error even">
<td class="Level">ERROR</td>
<td class="Date">2025-08-16 17:38:15,564</td>
<td class="Message">同步KnoBaseDetail数据到Solr失败</td>
<td class="MethodOfCaller">syncToSolr</td>
<td class="FileOfCaller">KnoBaseDetailService.java</td>
<td class="LineOfCaller">507</td>
</tr>
<tr><td class="Exception" colspan="6">org.springframework.dao.DataAccessResourceFailureException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrExceptionTranslator.translateExceptionIfPossible(SolrExceptionTranslator.java:79)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:169)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.delete(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrOperations.delete(SolrOperations.java:228)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.repository.support.SimpleSolrRepository.deleteAll(SimpleSolrRepository.java:212)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:289)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:121)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:530)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:286)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:640)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:164)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:139)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:344)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:198)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:215)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at jdk.proxy2/jdk.proxy2.$Proxy187.deleteAll(Unknown Source)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService.deleteAll(KnoBaseDetailSolrService.java:313)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$FastClassBySpringCGLIB$$1d6fbb9b.invoke(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailSolrService$$EnhancerBySpringCGLIB$$feae8788.deleteAll(&lt;generated&gt;)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.jeecg.modules.km.kno.service.KnoBaseDetailService.syncToSolr(KnoBaseDetailService.java:497)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.reflect.Method.invoke(Method.java:568)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.ScheduledMethodRunnable.run(ScheduledMethodRunnable.java:84)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.scheduling.support.DelegatingErrorHandlingRunnable.run(DelegatingErrorHandlingRunnable.java:54)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset$$$capture(FutureTask.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.FutureTask.runAndReset(FutureTask.java)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:305)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.lang.Thread.run(Thread.java:833)
<br />Caused by: org.apache.http.conn.HttpHostConnectException: Connect to 127.0.0.1:8099 [/127.0.0.1] failed: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.executeMethod(HttpSolrClient.java:571)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:266)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.impl.HttpSolrClient.request(HttpSolrClient.java:248)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrRequest.process(SolrRequest.java:214)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:940)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.solr.client.solrj.SolrClient.deleteByQuery(SolrClient.java:903)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.lambda$delete$6(SolrTemplate.java:249)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.springframework.data.solr.core.SolrTemplate.execute(SolrTemplate.java:167)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 65 common frames omitted
<br />Caused by: java.net.ConnectException: Connection refused: no further information
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnect(Native Method)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.Net.pollConnectNow(Net.java:672)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.timedFinishConnect(NioSocketImpl.java:542)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/sun.nio.ch.NioSocketImpl.connect(NioSocketImpl.java:597)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.SocksSocketImpl.connect(SocksSocketImpl.java:327)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at java.base/java.net.Socket.connect(Socket.java:633)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
<br />&nbsp;&nbsp;&nbsp;&nbsp;at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
<br />&nbsp;&nbsp;&nbsp;&nbsp;	... 82 common frames omitted
</td></tr>