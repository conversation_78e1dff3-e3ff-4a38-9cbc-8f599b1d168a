// app.js
App({
  onLaunch() {
    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)
   
   // 模拟测试登录 正式环境注销 
   //this.login_test()

   this.initToken()
   this.initGlobalUser()

  },

  onShow (options) {
    // Do something when show.
    //this.initToken()
    //this.initGlobalUser()
  },

   //初始化token
   initToken() {
    console.log("initToken-----")
    let token = wx.getStorageSync("authStr")
    if(!token || token == '' || token == null){
        console.log("initToken--没找到缓存中的token需要重新登录一下")
        this.logout()
        wx.redirectTo({
          url:`/pages/login/login`,
      })
      return false
    }
    this.token = token
    console.log("initToken-----" + JSON.stringify(this.token))
  },

  //初始化登录用户
  initGlobalUser() {
    console.log("initGlobalUser-----")
    let loginUser = wx.getStorageSync("loginUser")
    if(!loginUser  || loginUser == null || loginUser.accountId == null || loginUser.accountId == ''){
      console.log("initGlobalUser--没找到缓存中的loginUser需要重新登录一下")
      this.logout()
      wx.redirectTo({
        url:`/pages/login/login`,
    })
    return false
    }
    this.globalData.userInfo = loginUser
    console.log("initGlobalUser-----" + JSON.stringify(this.globalData.userInfo))
  },

  // 存储token
  setToken(token) {
    this.token = token
  },
  setGlobalUser(userInfo) {
    this.globalData.userInfo = userInfo
  },

  login_test(){
    // 登录
    wx.login({
      success: res => {
        console.log("---wx.login---" + JSON.stringify(res))
        // 发送 res.code 到后台换取 openId, sessionKey, unionId

        //测试登录 设置缓存数据
         let token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.***********************************************************************************.tSei4WbElK9Jka2178Ki7Df7xqDBgZr1kQgBCFW9k44djrA_z_pzkfeSUbJ_-Tz0lRjucUOAGFLpA6rlVK3kpg"
         let data = {
          accessToken: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.***********************************************************************************.tSei4WbElK9Jka2178Ki7Df7xqDBgZr1kQgBCFW9k44djrA_z_pzkfeSUbJ_-Tz0lRjucUOAGFLpA6rlVK3kpg",
          accessTokenTime: *************,
          accountId: "100326848461817295",
          accountName: "李嫣婧",
          organId: "f0f5b9151d734063bef555ec07216211",
          organName: "计2020二班",
          phone: "***********",
          photourl: null,
          token: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.***********************************************************************************.bkm4HLuiGjTgU2XuwlvoE_HBzSRIFAe6uwm5U10xCIWi-55MnGOo-afe5M0hasTpVViiy2tBuqiv8_T8OqGSPw",
          tokenTime: *************,
          trueName: "李嫣婧",
          userType: "学生",
         }
         wx.setStorageSync('authStr', token);
         wx.setStorageSync('loginUser', data);
         this.setToken(token);
         this.setGlobalUser(data);
      }
    })
  },

  logout(){
    wx.removeStorageSync('authStr')
    wx.removeStorageSync('loginUser')
    wx.removeStorageSync('courseTypeList')
    wx.removeStorageSync('courseTagList')
    this.setToken('');
    this.setGlobalUser(null);
  },


  globalData: {
    userInfo: null,
    currentStudent: null,
    ossHost:'https://hniecloud-file.oss-cn-beijing.aliyuncs.com/', // oss 路径

    apiHost:'http://**************/wordCompassApi', // api路径 //开发 
    apiFileHost:'http://**************/wordCompassApi/sys/common/static/', // 文件路径 //开发
    //apiHost:'http://localhost:8080/wordCompassApi', // api路径 //开发
    //apiFileHost:'http://localhost:8080/wordCompassApi/sys/common/static/', // 文件路径 //开发
    //apiHost:'https://wx.eifansoft.com/wordCompassApi', // api路径 //开发 
    //apiFileHost:'https://wx.eifansoft.com/wordCompassApi/sys/common/static/', // 文件路径 //开发
    apiFilePath:'/wordCompassApi/sys/common/static/',     //文件的相对路径  //开发
    mpTmplId:'MTKxbdCkIm2LAi1-VjG0ezUxpxFUWbpuaXDKTz-eBPY', //模板消息Id //开发
    searchKeyword: '' // 全局搜索关键词
    
    //apiHost:'https://wx.hniecloud.com/campusse/api/', // api路径 //开发
    //apiFileHost:'https://wx.hniecloud.com/campusse/api/showFile/', // 文件路径 //开发
    //apiFilePath:'/campusse/api/showFile/',     //文件的相对路径  //开发
    //mpTmplId:'MTKxbdCkIm2LAi1-VjG0ezUxpxFUWbpuaXDKTz-eBPY' //模板消息Id //开发


    //apiHost:'https://xjxt.hnjdxy.cn/schoolserver/api/',             // api路径  // 正式
    //apiFileHost:'https://xjxt.hnjdxy.cn/schoolserver/api/showFile/', // 文件路径 // 正式
    //apiFilePath:'/schoolserver/api/showFile/',                           //文件的相对路径  // 正式
    //mpTmplId:'sW14aEi8IrOO4HmYnOuib-HJPg4zD-h7Bx_4qEOqCCQ' //模板消息Id // 正式
  },
  
  // 用户token
  token: "",
})
